create or replace function exec_sql(query text)
returns json as $$
declare
  result json;
  query_type text;
begin
  -- Extract the first word to determine query type
  query_type := upper(trim(split_part(trim(query), ' ', 1)));
  
  -- Handle different query types
  if query_type in ('SELECT', 'WITH') then
    -- For SELECT and WITH queries, return JSON aggregated results
    execute 'select json_agg(t) from (' || query || ') t' into result;
    return coalesce(result, '[]'::json);
  elsif query_type in ('INSERT', 'UPDATE', 'DELETE') then
    -- For modification queries, execute and return affected row count
    execute query;
    get diagnostics result = row_count;
    return json_build_object('affected_rows', result);
  else
    -- For other queries (CREATE, DROP, ALTER, etc.), just execute
    execute query;
    return json_build_object('status', 'success', 'message', 'Query executed successfully');
  end if;
end;
$$ language plpgsql;