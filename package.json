{"workspaces": ["packages/*"], "scripts": {"build": "npm run build --workspace @supabase/mcp-utils --workspace @supabase/mcp-server-supabase", "test": "npm run test --workspace @supabase/mcp-utils --workspace @supabase/mcp-server-supabase", "test:coverage": "npm run test:coverage --workspace @supabase/mcp-server-supabase", "format": "biome check --write .", "format:check": "biome check ."}, "devDependencies": {"@biomejs/biome": "1.9.4", "supabase": "^2.24.3"}}