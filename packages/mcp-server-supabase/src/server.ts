import { createMcpServer, type Tool } from '@supabase/mcp-utils';
import packageJson from '../package.json' with { type: 'json' };
import { createContentApiClient } from './content-api/index.js';
import { getLocalConfig, type LocalConfig, ConfigFactory, createConfigFromEnv, type ConfigManager, startConfigHealthMonitoring } from './config/index.js';
import { createLocalSupabasePlatform } from './platform/local-platform.js';
import type { SupabasePlatform } from './platform/types.js';
import { getDatabaseOperationTools } from './tools/database-operation-tools.js';
import { getDevelopmentTools } from './tools/development-tools.js';
import { getDocsTools } from './tools/docs-tools.js';
import { getEdgeFunctionTools } from './tools/edge-function-tools.js';
import { getLocalDevelopmentTools } from './tools/local-development-tools.js';
import { contextLogger, RequestTracker } from './utils/logger.js';
import { requestMiddleware, healthChecker } from './utils/middleware.js';
import { ConfigurationError, DatabaseError, ErrorCode } from './utils/errors.js';
import { toolRegistry } from './utils/tool-registry.js';
import { toolValidator } from './utils/tool-validator.js';
import { createNotificationSystem, type NotificationSystem, EventTypes } from './notifications/index.js';
import {
  MonitoringSystem,
  alertingSystem,
  distributedTracer
} from './monitoring/index.js';

const { version } = packageJson;

export type LocalSupabaseMcpServerOptions = {
  /**
   * Local Supabase configuration.
   */
  config?: Partial<LocalConfig>;

  /**
   * Enhanced configuration manager instance.
   */
  configManager?: ConfigManager;

  /**
   * The API URL for the Supabase Content API.
   */
  contentApiUrl?: string;

  /**
   * Executes database queries in read-only mode if true.
   */
  readOnly?: boolean;

  /**
   * Configuration profile to use.
   */
  profile?: 'development' | 'testing' | 'production' | 'local';

  /**
   * Enable configuration hot reloading.
   */
  enableHotReload?: boolean;

  /**
   * Enable notification system for real-time updates.
   */
  enableNotifications?: boolean;

  /**
   * Enable real-time protocol communication.
   */
  enableRealTimeUpdates?: boolean;

  /**
   * Enable comprehensive monitoring system.
   */
  enableMonitoring?: boolean;

  /**
   * Monitoring system configuration.
   */
  monitoringConfig?: {
    enableHealthEndpoints?: boolean;
    enableMetricsEndpoints?: boolean;
    enableDistributedTracing?: boolean;
    enableAlerting?: boolean;
    alertCheckInterval?: number;
  };
};

/**
 * Creates an MCP server for interacting with local Supabase instances.
 */
export async function createLocalSupabaseMcpServer(options: LocalSupabaseMcpServerOptions = {}) {
  const {
    config,
    configManager: providedConfigManager,
    readOnly,
    contentApiUrl = 'https://supabase.com/docs/api/graphql',
    profile,
    enableHotReload = false,
    enableNotifications = true,
    enableRealTimeUpdates = true,
    enableMonitoring = true,
    monitoringConfig = {},
  } = options;

  const logger = contextLogger.child({ component: 'LocalSupabaseMcpServer' });

  let localConfig: LocalConfig;
  let configManager: ConfigManager | undefined;
  let platform: SupabasePlatform;
  let contentApiClientPromise: Promise<any>;
  let notificationSystem: NotificationSystem | undefined;
  let monitoringSystem: MonitoringSystem | undefined;

  try {
    logger.info('Initializing local Supabase MCP server', { readOnly, contentApiUrl, profile, enableHotReload });

    // Initialize enhanced configuration management if requested
    if (providedConfigManager) {
      configManager = providedConfigManager;
      logger.debug('Using provided configuration manager');
    } else if (profile || enableHotReload) {
      // Create enhanced configuration manager
      try {
        if (profile) {
          configManager = await ConfigFactory.createFromPreset(profile, 'server', {
            enableHotReload,
          });
        } else {
          configManager = await createConfigFromEnv();
          if (enableHotReload) {
            configManager.set('ENABLE_HOT_RELOAD', true, 'cli');
          }
        }
        logger.debug('Created enhanced configuration manager', { profile, enableHotReload });
      } catch (error) {
        logger.warn('Failed to create enhanced configuration manager, falling back to legacy config', {
          profile,
          enableHotReload,
          error: error instanceof Error ? error.message : String(error),
        });
        configManager = undefined;
      }
    }

    // Get configuration from environment or provided config
    localConfig = config ? { ...getLocalConfig(), ...config } : getLocalConfig();

    // Override with enhanced configuration values if available
    if (configManager) {
      const enhancedConfig = {
        SUPABASE_URL: configManager.get('SUPABASE_URL', localConfig.SUPABASE_URL),
        SUPABASE_ANON_KEY: configManager.get('SUPABASE_ANON_KEY', localConfig.SUPABASE_ANON_KEY),
        SUPABASE_SERVICE_ROLE_KEY: configManager.get('SUPABASE_SERVICE_ROLE_KEY', localConfig.SUPABASE_SERVICE_ROLE_KEY),
        DATABASE_URL: configManager.get('DATABASE_URL', localConfig.DATABASE_URL),
        READ_ONLY: configManager.get('READ_ONLY', localConfig.READ_ONLY || readOnly),
        DEBUG_SQL: configManager.get('DEBUG_SQL', localConfig.DEBUG_SQL),
      };
      localConfig = enhancedConfig as LocalConfig;
    }

    logger.debug('Configuration loaded successfully', {
      url: localConfig.SUPABASE_URL,
      readOnly: localConfig.READ_ONLY,
      debugSql: localConfig.DEBUG_SQL,
      enhanced: !!configManager,
      profile: configManager ? (configManager as any).profile : 'legacy',
      hotReload: configManager?.get('ENABLE_HOT_RELOAD', false),
    });

    // Setup configuration change monitoring if enhanced config is available
    if (configManager && configManager.get('ENABLE_HOT_RELOAD')) {
      configManager.onChange((event) => {
        logger.info('Configuration changed', {
          key: event.key,
          source: event.source,
          timestamp: event.timestamp,
        });

        // Handle specific configuration changes that require action
        if (event.key === 'LOG_LEVEL') {
          logger.info('Log level changed, updating logger configuration', {
            oldLevel: event.oldValue,
            newLevel: event.newValue,
          });
        }
      });
      
      // Start configuration health monitoring if enabled
      if (configManager.get('ENABLE_METRICS', true)) {
        const monitoringInterval = configManager.get('HEALTH_CHECK_INTERVAL', 300000); // 5 minutes default
        startConfigHealthMonitoring(monitoringInterval);
        logger.debug('Configuration health monitoring started', { intervalMs: monitoringInterval });
      }
    }

    // Create the local platform
    platform = createLocalSupabasePlatform(localConfig);
    contentApiClientPromise = createContentApiClient(contentApiUrl);

    // Initialize monitoring system if enabled
    if (enableMonitoring) {
      monitoringSystem = new MonitoringSystem({
        enableHealthEndpoints: monitoringConfig.enableHealthEndpoints ?? true,
        enableMetricsEndpoints: monitoringConfig.enableMetricsEndpoints ?? true,
        enableDistributedTracing: monitoringConfig.enableDistributedTracing ?? true,
        enableAlerting: monitoringConfig.enableAlerting ?? true,
        alertCheckInterval: monitoringConfig.alertCheckInterval ?? 60000,
      });

      logger.info('Monitoring system initialized', {
        config: monitoringSystem.getStatus().config
      });
    }

    logger.info('Platform and content API client initialized');
  } catch (error) {
    logger.error('Failed to initialize server components', error instanceof Error ? error : undefined);
    throw new ConfigurationError(
      `Failed to initialize local Supabase MCP server: ${error instanceof Error ? error.message : String(error)}`,
      { cause: error instanceof Error ? error : undefined }
    );
  }

  // Add health checks
  healthChecker.addCheck('database', async () => {
    try {
      await platform.executeSql('local', { query: 'SELECT 1', read_only: true });
      return { status: 'pass', message: 'Database connection healthy' };
    } catch (error) {
      return {
        status: 'fail',
        message: `Database connection failed: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  });

  const server = createMcpServer({
    name: 'supabase-local',
    version,
    async onInitialize(info: any) {
      const requestId = RequestTracker.generateRequestId();
      const initLogger = logger.withRequestId(requestId);

      try {
        initLogger.info('Initializing MCP server', { info });

        // Initialize the local platform
        await platform.init?.(info);

        // Initialize monitoring system if enabled
        if (enableMonitoring && monitoringSystem) {
          await monitoringSystem.initialize();
          initLogger.info('Monitoring system started');
        }

        initLogger.info('MCP server initialization completed successfully');
      } catch (error) {
        initLogger.error('MCP server initialization failed', error instanceof Error ? error : undefined);
        throw new DatabaseError(
          `Failed to initialize platform: ${error instanceof Error ? error.message : String(error)}`,
          ErrorCode.DATABASE_ERROR,
          { requestId, cause: error instanceof Error ? error : undefined }
        );
      }
    },
    tools: async () => {
      const requestId = RequestTracker.generateRequestId();
      const toolsLogger = logger.withRequestId(requestId);

      try {
        toolsLogger.info('Loading tools for MCP server');

        const contentApiClient = await contentApiClientPromise;
        const tools: Record<string, Tool> = {};

        // For local instances, we always use a single "local" project ID
        const projectId = 'local';

        // Clear any existing tools in registry
        toolRegistry.clear();

        // Add local-relevant tools with enhanced validation and metadata
        const toolGroups = [
          {
            name: 'database',
            getter: () => getDatabaseOperationTools({ platform, projectId, readOnly }),
            metadata: {
              category: 'database',
              version: '1.0.0',
              tags: ['sql', 'database', 'postgres'],
              permissions: { readOnly: !!readOnly, requiresAuth: true },
              performance: { complexity: 'high' as const, cacheability: 'none' as const },
            }
          },
          {
            name: 'edge-functions',
            getter: () => getEdgeFunctionTools({ platform, projectId }),
            metadata: {
              category: 'edge-functions',
              version: '1.0.0',
              tags: ['serverless', 'functions', 'deployment'],
              permissions: { readOnly: false, requiresAuth: true },
              performance: { complexity: 'medium' as const, cacheability: 'short' as const },
            }
          },
          {
            name: 'development',
            getter: () => getDevelopmentTools({ platform, projectId }),
            metadata: {
              category: 'development',
              version: '1.0.0',
              tags: ['config', 'development', 'utilities'],
              permissions: { readOnly: true, requiresAuth: false },
              performance: { complexity: 'low' as const, cacheability: 'long' as const },
            }
          },
          {
            name: 'local-development',
            getter: () => getLocalDevelopmentTools({ platform, projectId }),
            metadata: {
              category: 'local-development',
              version: '1.0.0',
              tags: ['local', 'development', 'debugging'],
              permissions: { readOnly: true, requiresAuth: false },
              performance: { complexity: 'low' as const, cacheability: 'long' as const },
            }
          },
          {
            name: 'docs',
            getter: () => getDocsTools({ contentApiClient }),
            metadata: {
              category: 'documentation',
              version: '1.0.0',
              tags: ['docs', 'search', 'help'],
              permissions: { readOnly: true, requiresAuth: false },
              performance: { complexity: 'medium' as const, cacheability: 'long' as const },
            }
          },
        ];

        for (const group of toolGroups) {
          try {
            const groupTools = group.getter();

            // Register tools with enhanced metadata
            toolRegistry.registerGroup(group.name, groupTools, group.metadata);

            // Add to legacy tools object for compatibility
            Object.assign(tools, groupTools);

            toolsLogger.debug(`Loaded ${group.name} tools`, {
              toolCount: Object.keys(groupTools).length,
              toolNames: Object.keys(groupTools)
            });
          } catch (error) {
            toolsLogger.error(`Failed to load ${group.name} tools`, error instanceof Error ? error : undefined);
            // Continue loading other tool groups even if one fails
          }
        }

        // Validate all registered tools
        const registryStats = toolRegistry.getStatistics();
        toolsLogger.info('All tools loaded and validated successfully', {
          totalTools: Object.keys(tools).length,
          toolNames: Object.keys(tools),
          registryStats
        });

        // Run compliance checks on all tools
        let complianceIssues = 0;
        for (const [toolName, tool] of Object.entries(tools)) {
          try {
            const compliance = toolValidator.checkToolCompliance(tool);
            if (!compliance.compliant) {
              complianceIssues++;
              toolsLogger.warn(`Tool compliance issues found: ${toolName}`, {
                score: compliance.score,
                issues: compliance.issues.filter(i => i.severity === 'error'),
              });
            }
          } catch (error) {
            toolsLogger.error(`Failed to validate tool: ${toolName}`, error instanceof Error ? error : undefined);
          }
        }

        if (complianceIssues > 0) {
          toolsLogger.warn(`Found compliance issues in ${complianceIssues} tools`);
        }

        return tools;
      } catch (error) {
        toolsLogger.error('Failed to load tools', error instanceof Error ? error : undefined);
        throw new ConfigurationError(
          `Failed to load MCP tools: ${error instanceof Error ? error.message : String(error)}`,
          { requestId, cause: error instanceof Error ? error : undefined }
        );
      }
    },
  });

  // Apply middleware for enhanced error handling and logging
  requestMiddleware.applyToServer(server);

  logger.info('Local Supabase MCP server created successfully', {
    version,
    readOnly,
    url: localConfig.SUPABASE_URL
  });

  return server;
}

// Keep the old function name for backward compatibility, but it now creates a local server
export const createSupabaseMcpServer = createLocalSupabaseMcpServer;
