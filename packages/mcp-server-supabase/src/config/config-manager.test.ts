import { describe, expect, test, beforeEach, afterEach, vi } from 'vitest';
import { ConfigManager, type ConfigProfile } from './config-manager.js';
import { ConfigFactory } from './config-factory.js';
import { ConfigUtils } from './config-utils.js';
import { writeFileSync, unlinkSync, existsSync } from 'fs';
import { join } from 'path';

describe('ConfigManager', () => {
  let configManager: ConfigManager;
  const testConfigFile = join(process.cwd(), 'test-config.json');
  const testEnvFile = join(process.cwd(), 'test.env');

  beforeEach(() => {
    configManager = new ConfigManager('testing');
    
    // Clean up any existing test files
    if (existsSync(testConfigFile)) {
      unlinkSync(testConfigFile);
    }
    if (existsSync(testEnvFile)) {
      unlinkSync(testEnvFile);
    }
  });

  afterEach(() => {
    configManager.destroy();
    
    // Clean up test files
    if (existsSync(testConfigFile)) {
      unlinkSync(testConfigFile);
    }
    if (existsSync(testEnvFile)) {
      unlinkSync(testEnvFile);
    }
  });

  describe('Basic Operations', () => {
    test('should set and get configuration values', () => {
      configManager.set('TEST_KEY', 'test_value');
      
      expect(configManager.get('TEST_KEY')).toBe('test_value');
      expect(configManager.has('TEST_KEY')).toBe(true);
      expect(configManager.get('NON_EXISTENT_KEY', 'default')).toBe('default');
    });

    test('should track configuration metadata', () => {
      configManager.set('TEST_KEY', 'test_value', 'env');
      
      const metadata = configManager.getWithMetadata('TEST_KEY');
      expect(metadata).toBeDefined();
      expect(metadata!.value).toBe('test_value');
      expect(metadata!.source).toBe('env');
      expect(metadata!.profile).toBe('testing');
      expect(metadata!.lastUpdated).toBeInstanceOf(Date);
    });

    test('should identify sensitive keys', () => {
      configManager.set('SUPABASE_ANON_KEY', 'secret_key');
      configManager.set('REGULAR_KEY', 'regular_value');
      
      const sensitiveMetadata = configManager.getWithMetadata('SUPABASE_ANON_KEY');
      const regularMetadata = configManager.getWithMetadata('REGULAR_KEY');
      
      expect(sensitiveMetadata!.sensitive).toBe(true);
      expect(regularMetadata!.sensitive).toBe(false);
    });

    test('should redact sensitive values in toObject', () => {
      configManager.set('SUPABASE_ANON_KEY', 'secret_key');
      configManager.set('REGULAR_KEY', 'regular_value');
      
      const configWithoutSensitive = configManager.toObject(false);
      const configWithSensitive = configManager.toObject(true);
      
      expect(configWithoutSensitive['SUPABASE_ANON_KEY']).toBe('[REDACTED]');
      expect(configWithoutSensitive['REGULAR_KEY']).toBe('regular_value');
      
      expect(configWithSensitive['SUPABASE_ANON_KEY']).toBe('secret_key');
      expect(configWithSensitive['REGULAR_KEY']).toBe('regular_value');
    });
  });

  describe('Configuration Loading', () => {
    test('should load configuration from JSON file', async () => {
      // Clear environment variables that might interfere
      const originalEnv = process.env;
      process.env = { ...originalEnv };
      delete process.env.SUPABASE_URL;
      delete process.env.READ_ONLY;
      
      const testConfig = {
        SUPABASE_URL: 'http://localhost:54321',
        SUPABASE_ANON_KEY: 'test_anon_key',
        SUPABASE_SERVICE_ROLE_KEY: 'test_service_key',
        READ_ONLY: true,
      };
      
      writeFileSync(testConfigFile, JSON.stringify(testConfig));
      
      await configManager.load({
        configFiles: [testConfigFile],
      });
      
      expect(configManager.get('SUPABASE_URL')).toBe('http://localhost:54321');
      expect(configManager.get('READ_ONLY')).toBe(true);
      
      // Restore environment
      process.env = originalEnv;
    });

    test('should load configuration from env file', async () => {
      // Clear environment variables that might interfere
      const originalEnv = process.env;
      process.env = { ...originalEnv };
      delete process.env.SUPABASE_URL;
      delete process.env.SUPABASE_ANON_KEY;
      delete process.env.READ_ONLY;
      delete process.env.DEBUG_SQL;
      
      const envContent = `
SUPABASE_URL=http://localhost:54321
SUPABASE_ANON_KEY="test_anon_key"
READ_ONLY=true
# This is a comment
DEBUG_SQL=false
      `.trim();
      
      writeFileSync(testEnvFile, envContent);
      
      await configManager.load({
        configFiles: [testEnvFile],
      });
      
      expect(configManager.get('SUPABASE_URL')).toBe('http://localhost:54321');
      expect(configManager.get('SUPABASE_ANON_KEY')).toBe('test_anon_key');
      expect(configManager.get('READ_ONLY')).toBe(true);
      expect(configManager.get('DEBUG_SQL')).toBe(false);
      
      // Restore environment
      process.env = originalEnv;
    });

    test('should load configuration from environment variables', async () => {
      const originalEnv = process.env;
      process.env = {
        ...originalEnv,
        SUPABASE_URL: 'http://test.supabase.co',
        SUPABASE_ANON_KEY: 'env_anon_key',
        READ_ONLY: 'false',
      };
      
      await configManager.load();
      
      expect(configManager.get('SUPABASE_URL')).toBe('http://test.supabase.co');
      expect(configManager.get('READ_ONLY')).toBe(false);
      
      process.env = originalEnv;
    });

    test('should prioritize sources correctly (cli > env > file > default)', async () => {
      // Setup file config
      writeFileSync(testConfigFile, JSON.stringify({
        READ_ONLY: true,
        DEBUG_SQL: true,
      }));
      
      // Setup env config
      const originalEnv = process.env;
      process.env = {
        ...originalEnv,
        READ_ONLY: 'false',
      };
      
      await configManager.load({
        configFiles: [testConfigFile],
        cliArgs: {
          DEBUG_SQL: false,
        },
      });
      
      // CLI should override env and file
      expect(configManager.get('DEBUG_SQL')).toBe(false);
      // Env should override file
      expect(configManager.get('READ_ONLY')).toBe(false);
      
      process.env = originalEnv;
    });
  });

  describe('Configuration Validation', () => {
    test('should validate configuration successfully', async () => {
      configManager.set('SUPABASE_URL', 'http://localhost:54321');
      configManager.set('SUPABASE_ANON_KEY', 'test_anon_key');
      configManager.set('SUPABASE_SERVICE_ROLE_KEY', 'test_service_key');
      
      const validation = await configManager.validate();
      
      expect(validation.valid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    test('should detect validation errors', async () => {
      configManager.set('SUPABASE_URL', 'invalid-url');
      configManager.set('QUERY_TIMEOUT', -1000);
      
      const validation = await configManager.validate();
      
      expect(validation.valid).toBe(false);
      expect(validation.errors.length).toBeGreaterThan(0);
    });

    test('should provide validation warnings for production profile', async () => {
      const prodConfig = new ConfigManager('production');
      prodConfig.set('SUPABASE_URL', 'http://localhost:54321');
      prodConfig.set('SUPABASE_ANON_KEY', 'test_anon_key');
      prodConfig.set('SUPABASE_SERVICE_ROLE_KEY', 'test_service_key');
      prodConfig.set('DEBUG_SQL', true);
      prodConfig.set('LOG_LEVEL', 'debug');
      
      const validation = await prodConfig.validate();
      
      expect(validation.warnings.length).toBeGreaterThan(0);
      expect(validation.warnings.some(w => w.key === 'DEBUG_SQL')).toBe(true);
      
      prodConfig.destroy();
    });
  });

  describe('Change Events', () => {
    test('should emit change events', () => {
      const changeEvents: any[] = [];
      
      const unsubscribe = configManager.onChange((event) => {
        changeEvents.push(event);
      });
      
      configManager.set('TEST_KEY', 'value1');
      configManager.set('TEST_KEY', 'value2');
      
      expect(changeEvents).toHaveLength(2);
      expect(changeEvents[0].key).toBe('TEST_KEY');
      expect(changeEvents[0].newValue).toBe('value1');
      expect(changeEvents[1].oldValue).toBe('value1');
      expect(changeEvents[1].newValue).toBe('value2');
      
      unsubscribe();
    });
  });

  describe('Feature Flags', () => {
    test('should manage feature flags', () => {
      configManager.setFeatureFlag('test_feature', {
        enabled: true,
        description: 'Test feature flag',
      });
      
      expect(configManager.getFeatureFlag('test_feature')).toBe(true);
      expect(configManager.getFeatureFlag('non_existent_feature')).toBe(false);
    });

    test('should respect rollout percentage', () => {
      configManager.setFeatureFlag('rollout_feature', {
        enabled: true,
        description: 'Feature with rollout',
        rolloutPercentage: 50,
      });
      
      // Test multiple times to check consistency
      const results = Array.from({ length: 10 }, () => 
        configManager.getFeatureFlag('rollout_feature')
      );
      
      // Should be consistent for the same feature name
      expect(results.every(r => r === results[0])).toBe(true);
    });
  });
});

describe('ConfigFactory', () => {
  afterEach(() => {
    ConfigFactory.destroyAll();
  });

  test('should create and manage configuration instances', async () => {
    const config1 = await ConfigFactory.create('test1', {
      profile: 'development',
      autoLoad: false,
    });
    
    const config2 = await ConfigFactory.create('test2', {
      profile: 'testing',
      autoLoad: false,
    });
    
    expect(ConfigFactory.has('test1')).toBe(true);
    expect(ConfigFactory.has('test2')).toBe(true);
    expect(ConfigFactory.get('test1')).toBe(config1);
    expect(ConfigFactory.get('test2')).toBe(config2);
    
    expect(ConfigFactory.list()).toEqual(['test1', 'test2']);
  });

  test('should create configuration from preset', async () => {
    const config = await ConfigFactory.createFromPreset('development', 'dev-config');
    
    expect(ConfigFactory.has('dev-config')).toBe(true);
    expect((config as any).profile).toBe('development');
  });

  test('should clone configuration instances', async () => {
    const source = await ConfigFactory.create('source', {
      profile: 'development',
      autoLoad: false,
    });
    
    source.set('TEST_KEY', 'test_value');
    
    const cloned = await ConfigFactory.clone('source', 'cloned');
    
    expect(cloned.get('TEST_KEY')).toBe('test_value');
    expect(ConfigFactory.has('cloned')).toBe(true);
  });

  test('should validate all instances', async () => {
    const config1 = await ConfigFactory.create('valid', {
      profile: 'testing',
      autoLoad: false,
    });
    config1.set('SUPABASE_URL', 'http://localhost:54321');
    config1.set('SUPABASE_ANON_KEY', 'test_key');
    config1.set('SUPABASE_SERVICE_ROLE_KEY', 'test_service_key');
    
    const config2 = await ConfigFactory.create('invalid', {
      profile: 'testing',
      autoLoad: false,
    });
    config2.set('SUPABASE_URL', 'invalid-url');
    
    const validation = await ConfigFactory.validateAll();
    
    expect(validation.valid).toBe(false);
    expect(validation.results.valid.valid).toBe(true);
    expect(validation.results.invalid.valid).toBe(false);
  });
});

describe('ConfigUtils', () => {
  let config1: ConfigManager;
  let config2: ConfigManager;

  beforeEach(() => {
    config1 = new ConfigManager('development');
    config2 = new ConfigManager('development');
  });

  afterEach(() => {
    config1.destroy();
    config2.destroy();
  });

  test('should compare configurations', () => {
    config1.set('KEY1', 'value1');
    config1.set('KEY2', 'value2');
    
    config2.set('KEY2', 'different_value');
    config2.set('KEY3', 'value3');
    
    const comparison = ConfigUtils.compare(config1, config2);
    
    expect(comparison.added).toEqual(['KEY3']);
    expect(comparison.removed).toEqual(['KEY1']);
    expect(comparison.changed).toHaveLength(1);
    expect(comparison.changed[0].key).toBe('KEY2');
    expect(comparison.unchanged).toEqual([]);
  });

  test('should create and restore backups', async () => {
    config1.set('KEY1', 'value1');
    config1.set('KEY2', 'value2');
    
    const backup = ConfigUtils.createBackup(config1);
    
    expect(backup.metadata.totalKeys).toBe(2);
    expect(backup.config.KEY1.value).toBe('value1');
    
    const restored = await ConfigUtils.restoreFromBackup(backup, 'restored');
    
    expect(restored.get('KEY1')).toBe('value1');
    expect(restored.get('KEY2')).toBe('value2');
    
    restored.destroy();
  });

  test('should merge configurations', async () => {
    config1.set('KEY1', 'value1');
    config1.set('KEY2', 'value2');
    
    config2.set('KEY2', 'overridden_value');
    config2.set('KEY3', 'value3');
    
    const merged = await ConfigUtils.merge([config1, config2]);
    
    expect(merged.get('KEY1')).toBe('value1');
    expect(merged.get('KEY2')).toBe('overridden_value'); // Later config wins
    expect(merged.get('KEY3')).toBe('value3');
    
    merged.destroy();
  });

  test('should filter configurations', () => {
    config1.set('SUPABASE_URL', 'http://localhost:54321', 'env');
    config1.set('DEBUG_SQL', true, 'file');
    config1.set('REGULAR_KEY', 'value', 'cli');
    
    const envOnly = ConfigUtils.filter(config1, { sources: ['env'] });
    const sensitiveOnly = ConfigUtils.filter(config1, { sensitive: true });
    const patternMatch = ConfigUtils.filter(config1, { pattern: /^SUPABASE_/ });
    
    expect(Object.keys(envOnly)).toEqual(['SUPABASE_URL']);
    expect(Object.keys(sensitiveOnly)).toEqual([]);
    expect(Object.keys(patternMatch)).toEqual(['SUPABASE_URL']);
  });

  test('should generate documentation', () => {
    config1.set('SUPABASE_URL', 'http://localhost:54321');
    config1.set('DEBUG_SQL', true);
    config1.set('ENABLE_ANALYTICS', false);
    
    const docs = ConfigUtils.generateDocs(config1);
    
    expect(docs.profile).toBe('development');
    expect(docs.sections.length).toBeGreaterThan(0);
    expect(docs.summary.totalKeys).toBe(3);
    
    const supabaseSection = docs.sections.find(s => s.name === 'Supabase');
    expect(supabaseSection).toBeDefined();
    expect(supabaseSection!.keys.some(k => k.key === 'SUPABASE_URL')).toBe(true);
  });

  test('should export and import configurations', () => {
    config1.set('KEY1', 'value1');
    config1.set('KEY2', 42);
    config1.set('KEY3', true);
    
    const jsonExport = ConfigUtils.export(config1, 'json');
    const envExport = ConfigUtils.export(config1, 'env');
    
    expect(jsonExport).toContain('"KEY1": "value1"');
    expect(envExport).toContain('KEY1=value1');
    expect(envExport).toContain('KEY2=42');
    expect(envExport).toContain('KEY3=true');
    
    // Test import
    ConfigUtils.import(jsonExport, 'json', config2);
    
    expect(config2.get('KEY1')).toBe('value1');
    expect(config2.get('KEY2')).toBe(42);
    expect(config2.get('KEY3')).toBe(true);
  });
});
