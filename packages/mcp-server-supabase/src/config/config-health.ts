import { contextLogger } from '../utils/logger.js';
import { ConfigManager, type ConfigValidationResult } from './config-manager.js';
import { ConfigFactory } from './config-factory.js';

/**
 * Configuration health status
 */
export interface ConfigHealthStatus {
  healthy: boolean;
  timestamp: Date;
  instanceCount: number;
  validationResults: Record<string, ConfigValidationResult>;
  performanceMetrics: {
    validationTime: number;
    memoryUsage: number;
  };
  issues: Array<{
    severity: 'warning' | 'error';
    instance: string;
    message: string;
    key?: string;
  }>;
}

/**
 * Configuration monitor for health checks and performance monitoring
 */
export class ConfigHealthMonitor {
  private logger = contextLogger.child({ component: 'ConfigHealthMonitor' });
  private lastHealthCheck?: ConfigHealthStatus;
  private intervalId?: NodeJS.Timeout;

  /**
   * Start periodic health monitoring
   */
  startMonitoring(intervalMs: number = 60000): void {
    if (this.intervalId) {
      this.stopMonitoring();
    }

    this.logger.info('Starting configuration health monitoring', { intervalMs });
    
    this.intervalId = setInterval(async () => {
      try {
        const healthStatus = await this.checkHealth();
        
        if (!healthStatus.healthy) {
          this.logger.warn('Configuration health check failed', {
            issues: healthStatus.issues.length,
            instances: healthStatus.instanceCount,
          });
        } else {
          this.logger.debug('Configuration health check passed', {
            instances: healthStatus.instanceCount,
            validationTime: healthStatus.performanceMetrics.validationTime,
          });
        }
      } catch (error) {
        this.logger.error('Configuration health check error', error instanceof Error ? error : undefined);
      }
    }, intervalMs);
  }

  /**
   * Stop health monitoring
   */
  stopMonitoring(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = undefined;
      this.logger.info('Stopped configuration health monitoring');
    }
  }

  /**
   * Perform comprehensive health check
   */
  async checkHealth(): Promise<ConfigHealthStatus> {
    const startTime = Date.now();
    const instances = ConfigFactory.list();
    const validationResults: Record<string, ConfigValidationResult> = {};
    const issues: ConfigHealthStatus['issues'] = [];
    let healthy = true;

    // Validate all configuration instances
    for (const instanceName of instances) {
      const instance = ConfigFactory.get(instanceName);
      if (!instance) {
        issues.push({
          severity: 'error',
          instance: instanceName,
          message: 'Configuration instance not found',
        });
        healthy = false;
        continue;
      }

      try {
        const validation = await instance.validate();
        validationResults[instanceName] = validation;

        if (!validation.valid) {
          healthy = false;
          validation.errors.forEach(error => {
            issues.push({
              severity: 'error',
              instance: instanceName,
              message: error.message,
              key: error.key,
            });
          });
        }

        validation.warnings.forEach(warning => {
          issues.push({
            severity: 'warning',
            instance: instanceName,
            message: warning.message,
            key: warning.key,
          });
        });
      } catch (error) {
        healthy = false;
        issues.push({
          severity: 'error',
          instance: instanceName,
          message: `Validation failed: ${error instanceof Error ? error.message : String(error)}`,
        });
      }
    }

    const validationTime = Date.now() - startTime;
    const memoryUsage = process.memoryUsage().heapUsed;

    const healthStatus: ConfigHealthStatus = {
      healthy,
      timestamp: new Date(),
      instanceCount: instances.length,
      validationResults,
      performanceMetrics: {
        validationTime,
        memoryUsage,
      },
      issues,
    };

    this.lastHealthCheck = healthStatus;
    return healthStatus;
  }

  /**
   * Get last health check result
   */
  getLastHealthCheck(): ConfigHealthStatus | undefined {
    return this.lastHealthCheck;
  }

  /**
   * Get health summary
   */
  getHealthSummary(): {
    status: 'healthy' | 'degraded' | 'unhealthy';
    lastCheck?: Date;
    issueCount: number;
    errorCount: number;
    warningCount: number;
  } {
    if (!this.lastHealthCheck) {
      return {
        status: 'unhealthy',
        issueCount: 0,
        errorCount: 0,
        warningCount: 0,
      };
    }

    const errorCount = this.lastHealthCheck.issues.filter(i => i.severity === 'error').length;
    const warningCount = this.lastHealthCheck.issues.filter(i => i.severity === 'warning').length;

    let status: 'healthy' | 'degraded' | 'unhealthy';
    if (errorCount > 0) {
      status = 'unhealthy';
    } else if (warningCount > 0) {
      status = 'degraded';
    } else {
      status = 'healthy';
    }

    return {
      status,
      lastCheck: this.lastHealthCheck.timestamp,
      issueCount: this.lastHealthCheck.issues.length,
      errorCount,
      warningCount,
    };
  }

  /**
   * Get configuration metrics for monitoring
   */
  getMetrics(): Record<string, number | string> {
    const instances = ConfigFactory.list();
    const summary = ConfigFactory.getSummary();
    const healthSummary = this.getHealthSummary();

    const metrics: Record<string, number | string> = {
      'config.instances.total': instances.length,
      'config.health.status': healthSummary.status,
      'config.health.errors': healthSummary.errorCount,
      'config.health.warnings': healthSummary.warningCount,
      'config.validation.time': this.lastHealthCheck?.performanceMetrics.validationTime || -1,
      'config.memory.usage': this.lastHealthCheck?.performanceMetrics.memoryUsage || -1,
    };

    // Add per-instance metrics
    for (const [instanceName, instanceSummary] of Object.entries(summary)) {
      metrics[`config.instance.${instanceName}.keys`] = instanceSummary.keys;
      metrics[`config.instance.${instanceName}.profile`] = instanceSummary.profile;
      
      for (const [source, count] of Object.entries(instanceSummary.sources)) {
        metrics[`config.instance.${instanceName}.source.${source}`] = count;
      }
    }

    return metrics;
  }

  /**
   * Export health report for debugging
   */
  exportHealthReport(): Record<string, unknown> {
    return {
      healthStatus: this.lastHealthCheck,
      healthSummary: this.getHealthSummary(),
      metrics: this.getMetrics(),
      instances: ConfigFactory.getSummary(),
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    this.stopMonitoring();
    this.logger.info('Configuration health monitor destroyed');
  }
}

/**
 * Global configuration health monitor instance
 */
export const configHealthMonitor = new ConfigHealthMonitor();

/**
 * Convenience function to start configuration health monitoring
 */
export function startConfigHealthMonitoring(intervalMs?: number): void {
  configHealthMonitor.startMonitoring(intervalMs);
}

/**
 * Convenience function to get configuration health status
 */
export async function getConfigHealth(): Promise<ConfigHealthStatus> {
  return configHealthMonitor.checkHealth();
}

/**
 * Convenience function to get configuration health summary
 */
export function getConfigHealthSummary() {
  return configHealthMonitor.getHealthSummary();
}