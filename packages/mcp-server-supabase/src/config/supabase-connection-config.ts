import { z } from 'zod';
import { contextLogger } from '../utils/logger.js';
import { ConfigurationError } from '../utils/errors.js';

/**
 * Supabase connection configuration schema
 */
export const SupabaseConnectionConfigSchema = z.object({
  // Core Supabase connection details
  SUPABASE_URL: z.string().url().describe('Supabase instance URL'),
  SUPABASE_ANON_KEY: z.string().min(1).describe('Supabase anonymous key'),
  SUPABASE_SERVICE_ROLE_KEY: z.string().min(1).describe('Supabase service role key'),
  
  // Connection behavior settings
  SUPABASE_AUTO_REFRESH_TOKEN: z.boolean().default(false).describe('Enable automatic token refresh'),
  SUPABASE_PERSIST_SESSION: z.boolean().default(false).describe('Persist session across restarts'),
  SUPABASE_DETECT_SESSION_IN_URL: z.boolean().default(false).describe('Detect session in URL'),
  
  // HTTP client settings
  SUPABASE_FETCH_TIMEOUT: z.coerce.number().min(1000).max(300000).default(30000).describe('HTTP request timeout in milliseconds'),
  SUPABASE_MAX_RETRIES: z.coerce.number().min(0).max(10).default(3).describe('Maximum number of retry attempts'),
  SUPABASE_RETRY_DELAY: z.coerce.number().min(100).max(30000).default(1000).describe('Delay between retries in milliseconds'),
  
  // Connection pool settings for HTTP clients
  SUPABASE_POOL_SIZE: z.coerce.number().min(1).max(100).default(10).describe('Number of HTTP client instances to pool'),
  SUPABASE_POOL_IDLE_TIMEOUT: z.coerce.number().min(10000).max(3600000).default(300000).describe('Idle timeout for pooled connections in milliseconds'),
  SUPABASE_POOL_MAX_LIFETIME: z.coerce.number().min(60000).max(86400000).default(3600000).describe('Maximum lifetime for pooled connections in milliseconds'),
  
  // Request settings
  SUPABASE_REQUEST_TIMEOUT: z.coerce.number().min(1000).max(300000).default(45000).describe('Individual request timeout in milliseconds'),
  SUPABASE_CHUNK_SIZE: z.coerce.number().min(100).max(10000).default(1000).describe('Chunk size for batch operations'),
  
  // Monitoring and debugging
  SUPABASE_DEBUG: z.boolean().default(false).describe('Enable debug logging for Supabase operations'),
  SUPABASE_LOG_REQUESTS: z.boolean().default(false).describe('Log all HTTP requests to Supabase'),
  SUPABASE_LOG_SLOW_REQUESTS: z.boolean().default(true).describe('Log slow requests to Supabase'),
  SUPABASE_SLOW_REQUEST_THRESHOLD: z.coerce.number().min(100).max(60000).default(2000).describe('Slow request threshold in milliseconds'),
  
  // Health check settings
  SUPABASE_HEALTH_CHECK_INTERVAL: z.coerce.number().min(10000).max(300000).default(60000).describe('Health check interval in milliseconds'),
  SUPABASE_HEALTH_CHECK_TIMEOUT: z.coerce.number().min(1000).max(30000).default(5000).describe('Health check timeout in milliseconds'),
  SUPABASE_HEALTH_CHECK_ENABLED: z.boolean().default(true).describe('Enable periodic health checks'),
  
  // Error handling
  SUPABASE_CIRCUIT_BREAKER_ENABLED: z.boolean().default(true).describe('Enable circuit breaker for error handling'),
  SUPABASE_CIRCUIT_BREAKER_THRESHOLD: z.coerce.number().min(1).max(100).default(5).describe('Circuit breaker failure threshold'),
  SUPABASE_CIRCUIT_BREAKER_TIMEOUT: z.coerce.number().min(1000).max(300000).default(30000).describe('Circuit breaker timeout in milliseconds'),
});

export type SupabaseConnectionConfig = z.infer<typeof SupabaseConnectionConfigSchema>;

/**
 * Environment variable mapping for Supabase configuration
 */
export const SUPABASE_ENV_MAPPING = {
  // Core configuration
  SUPABASE_URL: ['SUPABASE_URL', 'SUPABASE_INSTANCE_URL', 'SUPABASE_API_URL'],
  SUPABASE_ANON_KEY: ['SUPABASE_ANON_KEY', 'SUPABASE_ANONYMOUS_KEY', 'SUPABASE_PUBLIC_KEY'],
  SUPABASE_SERVICE_ROLE_KEY: ['SUPABASE_SERVICE_ROLE_KEY', 'SUPABASE_SERVICE_KEY', 'SUPABASE_ADMIN_KEY'],
  
  // Connection behavior
  SUPABASE_AUTO_REFRESH_TOKEN: ['SUPABASE_AUTO_REFRESH_TOKEN', 'SUPABASE_AUTO_REFRESH'],
  SUPABASE_PERSIST_SESSION: ['SUPABASE_PERSIST_SESSION', 'SUPABASE_PERSIST'],
  SUPABASE_DETECT_SESSION_IN_URL: ['SUPABASE_DETECT_SESSION_IN_URL', 'SUPABASE_DETECT_SESSION'],
  
  // HTTP client settings
  SUPABASE_FETCH_TIMEOUT: ['SUPABASE_FETCH_TIMEOUT', 'SUPABASE_TIMEOUT'],
  SUPABASE_MAX_RETRIES: ['SUPABASE_MAX_RETRIES', 'SUPABASE_RETRIES'],
  SUPABASE_RETRY_DELAY: ['SUPABASE_RETRY_DELAY'],
  
  // Connection pool settings
  SUPABASE_POOL_SIZE: ['SUPABASE_POOL_SIZE', 'SUPABASE_CONNECTIONS'],
  SUPABASE_POOL_IDLE_TIMEOUT: ['SUPABASE_POOL_IDLE_TIMEOUT'],
  SUPABASE_POOL_MAX_LIFETIME: ['SUPABASE_POOL_MAX_LIFETIME'],
  
  // Request settings
  SUPABASE_REQUEST_TIMEOUT: ['SUPABASE_REQUEST_TIMEOUT'],
  SUPABASE_CHUNK_SIZE: ['SUPABASE_CHUNK_SIZE'],
  
  // Monitoring
  SUPABASE_DEBUG: ['SUPABASE_DEBUG', 'DEBUG_SUPABASE'],
  SUPABASE_LOG_REQUESTS: ['SUPABASE_LOG_REQUESTS'],
  SUPABASE_LOG_SLOW_REQUESTS: ['SUPABASE_LOG_SLOW_REQUESTS'],
  SUPABASE_SLOW_REQUEST_THRESHOLD: ['SUPABASE_SLOW_REQUEST_THRESHOLD'],
  
  // Health checks
  SUPABASE_HEALTH_CHECK_INTERVAL: ['SUPABASE_HEALTH_CHECK_INTERVAL'],
  SUPABASE_HEALTH_CHECK_TIMEOUT: ['SUPABASE_HEALTH_CHECK_TIMEOUT'],
  SUPABASE_HEALTH_CHECK_ENABLED: ['SUPABASE_HEALTH_CHECK_ENABLED'],
  
  // Error handling
  SUPABASE_CIRCUIT_BREAKER_ENABLED: ['SUPABASE_CIRCUIT_BREAKER_ENABLED'],
  SUPABASE_CIRCUIT_BREAKER_THRESHOLD: ['SUPABASE_CIRCUIT_BREAKER_THRESHOLD'],
  SUPABASE_CIRCUIT_BREAKER_TIMEOUT: ['SUPABASE_CIRCUIT_BREAKER_TIMEOUT'],
} as const;

/**
 * Default configuration for the remote Supabase instance
 */
export const DEFAULT_SUPABASE_CONFIG: Partial<SupabaseConnectionConfig> = {
  SUPABASE_URL: 'https://devdb.syncrobit.net',
  SUPABASE_AUTO_REFRESH_TOKEN: false,
  SUPABASE_PERSIST_SESSION: false,
  SUPABASE_DETECT_SESSION_IN_URL: false,
  SUPABASE_FETCH_TIMEOUT: 30000,
  SUPABASE_MAX_RETRIES: 3,
  SUPABASE_RETRY_DELAY: 1000,
  SUPABASE_POOL_SIZE: 10,
  SUPABASE_POOL_IDLE_TIMEOUT: 300000,
  SUPABASE_POOL_MAX_LIFETIME: 3600000,
  SUPABASE_REQUEST_TIMEOUT: 45000,
  SUPABASE_CHUNK_SIZE: 1000,
  SUPABASE_DEBUG: false,
  SUPABASE_LOG_REQUESTS: false,
  SUPABASE_LOG_SLOW_REQUESTS: true,
  SUPABASE_SLOW_REQUEST_THRESHOLD: 2000,
  SUPABASE_HEALTH_CHECK_INTERVAL: 60000,
  SUPABASE_HEALTH_CHECK_TIMEOUT: 5000,
  SUPABASE_HEALTH_CHECK_ENABLED: true,
  SUPABASE_CIRCUIT_BREAKER_ENABLED: true,
  SUPABASE_CIRCUIT_BREAKER_THRESHOLD: 5,
  SUPABASE_CIRCUIT_BREAKER_TIMEOUT: 30000,
};

/**
 * Supabase connection configuration manager
 */
export class SupabaseConnectionConfigManager {
  private config: SupabaseConnectionConfig | null = null;
  private logger = contextLogger.child({ component: 'SupabaseConnectionConfigManager' });

  /**
   * Load Supabase configuration from environment variables
   */
  async loadFromEnv(): Promise<SupabaseConnectionConfig> {
    const rawConfig: Record<string, string | undefined> = {};

    // Load environment variables using mapping
    for (const [configKey, envKeys] of Object.entries(SUPABASE_ENV_MAPPING)) {
      for (const envKey of envKeys) {
        const envValue = process.env[envKey];
        if (envValue !== undefined) {
          rawConfig[configKey] = envValue;
          break; // Use the first found value
        }
      }
    }

    // Apply defaults for missing values
    for (const [key, defaultValue] of Object.entries(DEFAULT_SUPABASE_CONFIG)) {
      if (rawConfig[key] === undefined && defaultValue !== undefined) {
        rawConfig[key] = String(defaultValue);
      }
    }

    try {
      // Convert string values to appropriate types and validate
      const processedConfig = this.processEnvValues(rawConfig);
      this.config = SupabaseConnectionConfigSchema.parse(processedConfig);
      
      this.logger.info('Supabase connection configuration loaded successfully', {
        url: this.config.SUPABASE_URL,
        poolSize: this.config.SUPABASE_POOL_SIZE,
        timeout: this.config.SUPABASE_FETCH_TIMEOUT,
        retries: this.config.SUPABASE_MAX_RETRIES,
        healthCheckEnabled: this.config.SUPABASE_HEALTH_CHECK_ENABLED,
        circuitBreakerEnabled: this.config.SUPABASE_CIRCUIT_BREAKER_ENABLED,
      });

      return this.config;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const missingFields = error.errors
          .filter(e => e.code === 'invalid_type' && e.received === 'undefined')
          .map(e => e.path.join('.'));
        
        throw new ConfigurationError(
          `Supabase connection configuration validation failed. Missing required fields: ${missingFields.join(', ')}\n` +
          `Please set the following environment variables: ${this.getRequiredEnvVars(missingFields).join(', ')}`
        );
      }
      throw error;
    }
  }

  /**
   * Get current Supabase configuration
   */
  getConfig(): SupabaseConnectionConfig {
    if (!this.config) {
      throw new ConfigurationError('Supabase connection configuration not loaded. Call loadFromEnv() first.');
    }
    return this.config;
  }

  /**
   * Get Supabase client options for @supabase/supabase-js
   */
  getClientOptions(): {
    auth: {
      autoRefreshToken: boolean;
      persistSession: boolean;
      detectSessionInUrl: boolean;
    };
    global: {
      fetch?: typeof fetch;
      headers?: Record<string, string>;
    };
    db: {
      schema?: string;
    };
    realtime?: {
      params?: Record<string, any>;
    };
  } {
    const config = this.getConfig();
    
    return {
      auth: {
        autoRefreshToken: config.SUPABASE_AUTO_REFRESH_TOKEN,
        persistSession: config.SUPABASE_PERSIST_SESSION,
        detectSessionInUrl: config.SUPABASE_DETECT_SESSION_IN_URL,
      },
      global: {
        headers: {
          'X-Client-Info': '@supabase/mcp-server-supabase',
        },
      },
      db: {
        schema: 'public',
      },
    };
  }

  /**
   * Get HTTP client configuration
   */
  getHttpClientConfig(): {
    timeout: number;
    maxRetries: number;
    retryDelay: number;
    headers: Record<string, string>;
  } {
    const config = this.getConfig();
    
    return {
      timeout: config.SUPABASE_FETCH_TIMEOUT,
      maxRetries: config.SUPABASE_MAX_RETRIES,
      retryDelay: config.SUPABASE_RETRY_DELAY,
      headers: {
        'User-Agent': '@supabase/mcp-server-supabase',
        'X-Client-Info': '@supabase/mcp-server-supabase',
      },
    };
  }

  /**
   * Get connection pool configuration
   */
  getPoolConfig(): {
    size: number;
    idleTimeout: number;
    maxLifetime: number;
  } {
    const config = this.getConfig();
    
    return {
      size: config.SUPABASE_POOL_SIZE,
      idleTimeout: config.SUPABASE_POOL_IDLE_TIMEOUT,
      maxLifetime: config.SUPABASE_POOL_MAX_LIFETIME,
    };
  }

  /**
   * Get health check configuration
   */
  getHealthCheckConfig(): {
    enabled: boolean;
    interval: number;
    timeout: number;
  } {
    const config = this.getConfig();
    
    return {
      enabled: config.SUPABASE_HEALTH_CHECK_ENABLED,
      interval: config.SUPABASE_HEALTH_CHECK_INTERVAL,
      timeout: config.SUPABASE_HEALTH_CHECK_TIMEOUT,
    };
  }

  /**
   * Get circuit breaker configuration
   */
  getCircuitBreakerConfig(): {
    enabled: boolean;
    threshold: number;
    timeout: number;
  } {
    const config = this.getConfig();
    
    return {
      enabled: config.SUPABASE_CIRCUIT_BREAKER_ENABLED,
      threshold: config.SUPABASE_CIRCUIT_BREAKER_THRESHOLD,
      timeout: config.SUPABASE_CIRCUIT_BREAKER_TIMEOUT,
    };
  }

  /**
   * Create a .env template file with all Supabase configuration options
   */
  generateEnvTemplate(): string {
    return `# Supabase Connection Configuration Template
# Set these environment variables to configure connections to your Supabase instance

# === Core Supabase Configuration ===
SUPABASE_URL=https://devdb.syncrobit.net
SUPABASE_ANON_KEY=your_anonymous_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# === Connection Behavior ===
SUPABASE_AUTO_REFRESH_TOKEN=false
SUPABASE_PERSIST_SESSION=false
SUPABASE_DETECT_SESSION_IN_URL=false

# === HTTP Client Settings ===
SUPABASE_FETCH_TIMEOUT=30000
SUPABASE_MAX_RETRIES=3
SUPABASE_RETRY_DELAY=1000

# === Connection Pool Settings ===
SUPABASE_POOL_SIZE=10
SUPABASE_POOL_IDLE_TIMEOUT=300000
SUPABASE_POOL_MAX_LIFETIME=3600000

# === Request Settings ===
SUPABASE_REQUEST_TIMEOUT=45000
SUPABASE_CHUNK_SIZE=1000

# === Monitoring and Debugging ===
SUPABASE_DEBUG=false
SUPABASE_LOG_REQUESTS=false
SUPABASE_LOG_SLOW_REQUESTS=true
SUPABASE_SLOW_REQUEST_THRESHOLD=2000

# === Health Check Settings ===
SUPABASE_HEALTH_CHECK_ENABLED=true
SUPABASE_HEALTH_CHECK_INTERVAL=60000
SUPABASE_HEALTH_CHECK_TIMEOUT=5000

# === Error Handling ===
SUPABASE_CIRCUIT_BREAKER_ENABLED=true
SUPABASE_CIRCUIT_BREAKER_THRESHOLD=5
SUPABASE_CIRCUIT_BREAKER_TIMEOUT=30000
`;
  }

  /**
   * Validate that the current configuration can establish a connection
   */
  async validateConnection(): Promise<boolean> {
    const config = this.getConfig();
    
    // Basic validation checks
    if (!config.SUPABASE_URL || !config.SUPABASE_ANON_KEY || !config.SUPABASE_SERVICE_ROLE_KEY) {
      return false;
    }
    
    // Validate URL format
    try {
      new URL(config.SUPABASE_URL);
    } catch {
      return false;
    }
    
    // Validate key formats (basic check)
    if (config.SUPABASE_ANON_KEY.length < 50 || config.SUPABASE_SERVICE_ROLE_KEY.length < 50) {
      return false;
    }
    
    return true;
  }

  /**
   * Get masked configuration for logging (sensitive values redacted)
   */
  getMaskedConfig(): Record<string, any> {
    const config = this.getConfig();
    const masked = { ...config };
    
    // Mask sensitive values
    masked.SUPABASE_ANON_KEY = this.maskKey(masked.SUPABASE_ANON_KEY);
    masked.SUPABASE_SERVICE_ROLE_KEY = this.maskKey(masked.SUPABASE_SERVICE_ROLE_KEY);
    
    return masked;
  }

  /**
   * Process environment values, converting strings to appropriate types
   */
  private processEnvValues(rawConfig: Record<string, string | undefined>): Record<string, any> {
    const processed: Record<string, any> = {};
    
    for (const [key, value] of Object.entries(rawConfig)) {
      if (value === undefined) continue;
      
      // Convert boolean values
      if (key.includes('AUTO_REFRESH') || key.includes('PERSIST') || key.includes('DETECT') || 
          key.includes('DEBUG') || key.includes('LOG') || key.includes('ENABLED')) {
        processed[key] = value.toLowerCase() === 'true';
      }
      // Convert numeric values
      else if (key.includes('TIMEOUT') || key.includes('RETRIES') || key.includes('DELAY') || 
               key.includes('SIZE') || key.includes('LIFETIME') || key.includes('INTERVAL') || 
               key.includes('THRESHOLD') || key.includes('CHUNK')) {
        processed[key] = parseInt(value);
      }
      // Keep string values as-is
      else {
        processed[key] = value;
      }
    }
    
    return processed;
  }

  /**
   * Get required environment variable names for missing fields
   */
  private getRequiredEnvVars(missingFields: string[]): string[] {
    const envVars: string[] = [];
    
    for (const field of missingFields) {
      const envKeys = (SUPABASE_ENV_MAPPING as any)[field];
      if (envKeys && envKeys.length > 0) {
        envVars.push(envKeys[0]); // Suggest the primary env var name
      }
    }
    
    return envVars;
  }

  /**
   * Mask sensitive keys for logging
   */
  private maskKey(key: string): string {
    if (key.length <= 8) return '[REDACTED]';
    return key.substring(0, 4) + '...' + key.substring(key.length - 4);
  }
}

/**
 * Singleton instance of the Supabase connection configuration manager
 */
export const supabaseConnectionConfig = new SupabaseConnectionConfigManager();