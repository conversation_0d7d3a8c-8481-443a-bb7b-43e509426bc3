import { ConfigManager, type ConfigProfile } from './config-manager.js';
import { contextLogger } from '../utils/logger.js';
import { ConfigurationError } from '../utils/errors.js';

/**
 * Configuration factory options
 */
export interface ConfigFactoryOptions {
  profile?: ConfigProfile;
  configFiles?: string[];
  envPrefix?: string;
  cliArgs?: Record<string, unknown>;
  autoLoad?: boolean;
  enableHotReload?: boolean;
}

/**
 * Configuration presets for common scenarios
 */
export const CONFIG_PRESETS = {
  development: {
    profile: 'development' as ConfigProfile,
    configFiles: ['.env.development', '.env.local', '.env'] as string[],
    envPrefix: '',
    enableHotReload: true,
  },

  testing: {
    profile: 'testing' as ConfigProfile,
    configFiles: ['.env.test', '.env.local'] as string[],
    envPrefix: 'TEST_',
    enableHotReload: false,
  },

  production: {
    profile: 'production' as ConfigProfile,
    configFiles: ['.env.production', '.env'] as string[],
    envPrefix: '',
    enableHotReload: false,
  },

  local: {
    profile: 'local' as ConfigProfile,
    configFiles: ['.env.local', '.env'] as string[],
    envPrefix: '',
    enableHotReload: true,
  },
};

/**
 * Configuration factory for creating and managing configuration instances
 */
export class ConfigFactory {
  private static instances = new Map<string, ConfigManager>();
  private static logger = contextLogger.child({ component: 'ConfigFactory' });

  /**
   * Create or get a configuration manager instance
   */
  static async create(
    name: string = 'default',
    options: ConfigFactoryOptions = {}
  ): Promise<ConfigManager> {
    // Check if instance already exists
    if (this.instances.has(name)) {
      this.logger.debug('Returning existing configuration instance', { name });
      return this.instances.get(name)!;
    }

    const {
      profile = this.detectProfile(),
      configFiles = [],
      envPrefix = '',
      cliArgs = {},
      autoLoad = true,
      enableHotReload = false,
    } = options;

    this.logger.info('Creating new configuration instance', { 
      name, 
      profile, 
      configFiles: configFiles.length,
      autoLoad,
      enableHotReload 
    });

    // Create new configuration manager
    const configManager = new ConfigManager(profile);

    // Apply hot reload setting
    if (enableHotReload) {
      configManager.set('ENABLE_HOT_RELOAD', true, 'cli');
    }

    // Auto-load configuration if requested
    if (autoLoad) {
      await configManager.load({
        configFiles,
        envPrefix,
        cliArgs,
      });
    }

    // Store instance
    this.instances.set(name, configManager);

    this.logger.info('Configuration instance created successfully', { name, profile });
    return configManager;
  }

  /**
   * Create configuration from preset
   */
  static async createFromPreset(
    presetName: keyof typeof CONFIG_PRESETS,
    name: string = 'default',
    overrides: Partial<ConfigFactoryOptions> = {}
  ): Promise<ConfigManager> {
    const preset = CONFIG_PRESETS[presetName];
    if (!preset) {
      throw new ConfigurationError(`Unknown configuration preset: ${presetName}`);
    }

    const options: ConfigFactoryOptions = {
      ...preset,
      ...overrides,
      autoLoad: overrides.autoLoad ?? true,
    };

    this.logger.info('Creating configuration from preset', { presetName, name });
    return this.create(name, options);
  }

  /**
   * Get existing configuration instance
   */
  static get(name: string = 'default'): ConfigManager | undefined {
    return this.instances.get(name);
  }

  /**
   * Check if configuration instance exists
   */
  static has(name: string = 'default'): boolean {
    return this.instances.has(name);
  }

  /**
   * Destroy configuration instance
   */
  static destroy(name: string = 'default'): boolean {
    const instance = this.instances.get(name);
    if (instance) {
      instance.destroy();
      this.instances.delete(name);
      this.logger.info('Configuration instance destroyed', { name });
      return true;
    }
    return false;
  }

  /**
   * Destroy all configuration instances
   */
  static destroyAll(): void {
    for (const [name, instance] of this.instances.entries()) {
      instance.destroy();
      this.instances.delete(name);
    }
    this.logger.info('All configuration instances destroyed');
  }

  /**
   * List all configuration instances
   */
  static list(): string[] {
    return Array.from(this.instances.keys());
  }

  /**
   * Auto-detect configuration profile from environment
   */
  private static detectProfile(): ConfigProfile {
    const nodeEnv = process.env.NODE_ENV?.toLowerCase();
    
    switch (nodeEnv) {
      case 'development':
      case 'dev':
        return 'development';
      case 'test':
      case 'testing':
        return 'testing';
      case 'production':
      case 'prod':
        return 'production';
      default:
        // Check for local development indicators
        if (process.env.SUPABASE_URL?.includes('devdb.syncrobit.net') ||
        process.env.SUPABASE_URL?.includes('192.168.1.218')) {
          return 'local';
        }
        return 'development';
    }
  }

  /**
   * Validate configuration across all instances
   */
  static async validateAll(): Promise<{
    valid: boolean;
    results: Record<string, { valid: boolean; errors: number; warnings: number }>;
  }> {
    const results: Record<string, { valid: boolean; errors: number; warnings: number }> = {};
    let allValid = true;

    for (const [name, instance] of this.instances.entries()) {
      const validation = await instance.validate();
      results[name] = {
        valid: validation.valid,
        errors: validation.errors.length,
        warnings: validation.warnings.length,
      };
      
      if (!validation.valid) {
        allValid = false;
      }
    }

    return { valid: allValid, results };
  }

  /**
   * Get configuration summary for all instances
   */
  static getSummary(): Record<string, {
    profile: ConfigProfile;
    keys: number;
    sources: Record<string, number>;
    lastUpdated: Date;
  }> {
    const summary: Record<string, any> = {};

    for (const [name, instance] of this.instances.entries()) {
      const keys = instance.keys();
      const sources = instance.getConfigSources();
      
      // Find most recent update
      let lastUpdated = new Date(0);
      for (const key of keys) {
        const metadata = instance.getWithMetadata(key);
        if (metadata && metadata.lastUpdated > lastUpdated) {
          lastUpdated = metadata.lastUpdated;
        }
      }

      summary[name] = {
        profile: (instance as any).profile,
        keys: keys.length,
        sources,
        lastUpdated,
      };
    }

    return summary;
  }

  /**
   * Export configuration for debugging
   */
  static exportConfig(
    name: string = 'default',
    includeSensitive: boolean = false
  ): Record<string, unknown> | null {
    const instance = this.instances.get(name);
    if (!instance) {
      return null;
    }

    return {
      profile: (instance as any).profile,
      config: instance.toObject(includeSensitive),
      sources: instance.getConfigSources(),
      keys: instance.keys(),
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Clone configuration instance
   */
  static async clone(
    sourceName: string,
    targetName: string,
    overrides: Partial<ConfigFactoryOptions> = {}
  ): Promise<ConfigManager> {
    const source = this.instances.get(sourceName);
    if (!source) {
      throw new ConfigurationError(`Source configuration instance '${sourceName}' not found`);
    }

    if (this.instances.has(targetName)) {
      throw new ConfigurationError(`Target configuration instance '${targetName}' already exists`);
    }

    // Create new instance with same profile
    const profile = (source as any).profile;
    const target = new ConfigManager(profile);

    // Copy all configuration values
    for (const key of source.keys()) {
      const metadata = source.getWithMetadata(key);
      if (metadata) {
        target.set(key, metadata.value, metadata.source);
      }
    }

    // Apply overrides
    if (overrides.cliArgs) {
      for (const [key, value] of Object.entries(overrides.cliArgs)) {
        target.set(key, value, 'cli');
      }
    }

    // Store instance
    this.instances.set(targetName, target);

    this.logger.info('Configuration instance cloned', { 
      sourceName, 
      targetName, 
      profile,
      keys: source.keys().length 
    });

    return target;
  }
}

/**
 * Convenience function to create default configuration
 */
export async function createDefaultConfig(
  options: ConfigFactoryOptions = {}
): Promise<ConfigManager> {
  return ConfigFactory.create('default', options);
}

/**
 * Convenience function to get default configuration
 */
export function getDefaultConfig(): ConfigManager | undefined {
  return ConfigFactory.get('default');
}

/**
 * Convenience function to create configuration from environment
 */
export async function createConfigFromEnv(): Promise<ConfigManager> {
  const profile = process.env.NODE_ENV as ConfigProfile || 'development';
  const presetName = profile in CONFIG_PRESETS ? profile as keyof typeof CONFIG_PRESETS : 'development';
  
  return ConfigFactory.createFromPreset(presetName);
}
