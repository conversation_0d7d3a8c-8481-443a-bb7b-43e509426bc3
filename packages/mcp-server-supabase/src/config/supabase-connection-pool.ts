import { createClient, type SupabaseClient } from '@supabase/supabase-js';
import { EventEmitter } from 'events';
import { contextLogger } from '../utils/logger.js';

import type { SupabaseConnectionConfig } from './supabase-connection-config.js';

/**
 * Supabase client connection interface
 */
export interface SupabaseConnection {
  id: string;
  client: SupabaseClient;
  type: 'anon' | 'service';
  createdAt: Date;
  lastUsed: Date;
  useCount: number;
  isHealthy: boolean;
  isIdle: boolean;
  errors: number;
  maxLifetime: number;
}

/**
 * Connection request interface
 */
export interface ConnectionRequest {
  id: string;
  type: 'anon' | 'service';
  timestamp: Date;
  timeout: NodeJS.Timeout;
  resolve: (connection: SupabaseConnection) => void;
  reject: (error: Error) => void;
}

/**
 * Pool statistics interface
 */
export interface PoolStats {
  totalConnections: number;
  anonConnections: number;
  serviceConnections: number;
  idleConnections: number;
  activeConnections: number;
  waitingRequests: number;
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  healthyConnections: number;
  unhealthyConnections: number;
  averageWaitTime: number;
  averageUseCount: number;
  uptime: number;
}

/**
 * Circuit breaker states
 */
export enum CircuitBreakerState {
  CLOSED = 'closed',
  OPEN = 'open',
  HALF_OPEN = 'half-open',
}

/**
 * Circuit breaker for handling connection failures
 */
class CircuitBreaker {
  private state = CircuitBreakerState.CLOSED;
  private failures = 0;
  private lastFailureTime = 0;
  private successCount = 0;

  constructor(
    private threshold: number,
    private timeout: number,
    private logger: any
  ) {}

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === CircuitBreakerState.OPEN) {
      if (Date.now() - this.lastFailureTime > this.timeout) {
        this.state = CircuitBreakerState.HALF_OPEN;
        this.logger.info('Circuit breaker entering half-open state');
      } else {
        throw new Error('Circuit breaker is open - service unavailable');
      }
    }

    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }

  private onSuccess(): void {
    this.failures = 0;
    if (this.state === CircuitBreakerState.HALF_OPEN) {
      this.successCount++;
      if (this.successCount >= 3) {
        this.state = CircuitBreakerState.CLOSED;
        this.successCount = 0;
        this.logger.info('Circuit breaker closed - service recovered');
      }
    }
  }

  private onFailure(): void {
    this.failures++;
    this.lastFailureTime = Date.now();
    
    if (this.state === CircuitBreakerState.HALF_OPEN) {
      this.state = CircuitBreakerState.OPEN;
      this.successCount = 0;
      this.logger.warn('Circuit breaker opened - service failure detected');
    } else if (this.failures >= this.threshold) {
      this.state = CircuitBreakerState.OPEN;
      this.logger.warn('Circuit breaker opened - failure threshold reached', {
        failures: this.failures,
        threshold: this.threshold,
      });
    }
  }

  getState(): CircuitBreakerState {
    return this.state;
  }

  getFailures(): number {
    return this.failures;
  }
}

/**
 * Supabase client connection pool
 */
export class SupabaseConnectionPool extends EventEmitter {
  private readonly logger = contextLogger.child({ component: 'SupabaseConnectionPool' });
  private readonly config: SupabaseConnectionConfig;
  
  private connections = new Map<string, SupabaseConnection>();
  private idleConnections = new Set<string>();
  private waitingRequests: ConnectionRequest[] = [];
  private healthCheckTimer?: NodeJS.Timeout;
  private cleanupTimer?: NodeJS.Timeout;
  private circuitBreaker?: CircuitBreaker;
  
  private stats: PoolStats = {
    totalConnections: 0,
    anonConnections: 0,
    serviceConnections: 0,
    idleConnections: 0,
    activeConnections: 0,
    waitingRequests: 0,
    totalRequests: 0,
    successfulRequests: 0,
    failedRequests: 0,
    healthyConnections: 0,
    unhealthyConnections: 0,
    averageWaitTime: 0,
    averageUseCount: 0,
    uptime: 0,
  };
  
  private totalWaitTime = 0;
  private isShuttingDown = false;
  private startTime = Date.now();

  constructor(config: SupabaseConnectionConfig) {
    super();
    this.config = config;
    
    if (config.SUPABASE_CIRCUIT_BREAKER_ENABLED) {
      this.circuitBreaker = new CircuitBreaker(
        config.SUPABASE_CIRCUIT_BREAKER_THRESHOLD,
        config.SUPABASE_CIRCUIT_BREAKER_TIMEOUT,
        this.logger
      );
    }

    this.logger.info('Supabase connection pool initialized', {
      url: config.SUPABASE_URL,
      poolSize: config.SUPABASE_POOL_SIZE,
      healthCheckEnabled: config.SUPABASE_HEALTH_CHECK_ENABLED,
      circuitBreakerEnabled: config.SUPABASE_CIRCUIT_BREAKER_ENABLED,
    });
  }

  /**
   * Initialize the connection pool
   */
  async initialize(): Promise<void> {
    try {
      // Create initial pool of connections (half anon, half service)
      const poolSize = this.config.SUPABASE_POOL_SIZE;
      const anonCount = Math.ceil(poolSize / 2);
      const serviceCount = poolSize - anonCount;

      // Create anonymous connections
      for (let i = 0; i < anonCount; i++) {
        await this.createConnection('anon');
      }

      // Create service role connections
      for (let i = 0; i < serviceCount; i++) {
        await this.createConnection('service');
      }

      // Start health check timer
      if (this.config.SUPABASE_HEALTH_CHECK_ENABLED) {
        this.startHealthCheck();
      }

      // Start cleanup timer
      this.startCleanupTimer();

      this.logger.info('Supabase connection pool initialized successfully', {
        totalConnections: this.connections.size,
        anonConnections: this.stats.anonConnections,
        serviceConnections: this.stats.serviceConnections,
      });

      this.emit('initialized');
    } catch (error) {
      this.logger.error('Failed to initialize Supabase connection pool', error instanceof Error ? error : undefined);
      throw error;
    }
  }

  /**
   * Acquire a connection from the pool
   */
  async acquire(type: 'anon' | 'service' = 'anon'): Promise<SupabaseConnection> {
    if (this.isShuttingDown) {
      throw new Error('Connection pool is shutting down');
    }

    const startTime = Date.now();
    this.stats.totalRequests++;

    try {
      // Circuit breaker check
      if (this.circuitBreaker) {
        return await this.circuitBreaker.execute(async () => {
          return await this.doAcquire(type);
        });
      } else {
        return await this.doAcquire(type);
      }
    } catch (error) {
      this.stats.failedRequests++;
      this.logger.error('Failed to acquire Supabase connection', error instanceof Error ? error : undefined, { type });
      throw error;
    } finally {
      const waitTime = Date.now() - startTime;
      this.totalWaitTime += waitTime;
      this.stats.averageWaitTime = this.totalWaitTime / this.stats.totalRequests;
    }
  }

  /**
   * Internal acquire logic
   */
  private async doAcquire(type: 'anon' | 'service'): Promise<SupabaseConnection> {
    // Try to get an idle connection of the requested type
    for (const connectionId of this.idleConnections) {
      const connection = this.connections.get(connectionId);
      if (connection && connection.type === type && connection.isHealthy) {
        this.idleConnections.delete(connectionId);
        connection.isIdle = false;
        connection.lastUsed = new Date();
        connection.useCount++;
        
        this.updateStats();
        this.stats.successfulRequests++;
        
        this.logger.debug('Supabase connection acquired from idle pool', { 
          connectionId: connection.id, 
          type: connection.type 
        });
        return connection;
      }
    }

    // Create new connection if under limit
    const currentTypeCount = Array.from(this.connections.values())
      .filter(conn => conn.type === type).length;
    const maxTypeCount = Math.ceil(this.config.SUPABASE_POOL_SIZE / 2);

    if (currentTypeCount < maxTypeCount) {
      const connection = await this.createConnection(type);
      connection.isIdle = false;
      connection.lastUsed = new Date();
      connection.useCount++;
      
      this.updateStats();
      this.stats.successfulRequests++;
      
      this.logger.debug('New Supabase connection created and acquired', { 
        connectionId: connection.id, 
        type: connection.type 
      });
      return connection;
    }

    // Wait for available connection
    return await this.waitForConnection(type);
  }

  /**
   * Release a connection back to the pool
   */
  async release(connection: SupabaseConnection): Promise<void> {
    try {
      if (!this.connections.has(connection.id)) {
        this.logger.warn('Attempting to release unknown Supabase connection', { connectionId: connection.id });
        return;
      }

      // Check if connection has exceeded max lifetime
      const age = Date.now() - connection.createdAt.getTime();
      if (age > connection.maxLifetime) {
        await this.destroyConnection(connection.id);
        return;
      }

      // Check if there are waiting requests for this connection type
      const waitingRequest = this.waitingRequests.find(req => req.type === connection.type);
      if (waitingRequest) {
        const index = this.waitingRequests.indexOf(waitingRequest);
        this.waitingRequests.splice(index, 1);
        clearTimeout(waitingRequest.timeout);
        
        connection.lastUsed = new Date();
        connection.useCount++;
        
        this.logger.debug('Supabase connection passed to waiting request', { 
          connectionId: connection.id,
          requestId: waitingRequest.id,
          type: connection.type,
        });
        
        waitingRequest.resolve(connection);
        return;
      }

      // Return to idle pool
      connection.isIdle = true;
      this.idleConnections.add(connection.id);
      
      this.updateStats();
      
      this.logger.debug('Supabase connection released to idle pool', { 
        connectionId: connection.id, 
        type: connection.type 
      });
      this.emit('connectionReleased', connection);
    } catch (error) {
      this.logger.error('Failed to release Supabase connection', error instanceof Error ? error : undefined, {
        connectionId: connection.id,
      });
    }
  }

  /**
   * Remove and destroy a connection
   */
  async destroyConnection(connectionId: string): Promise<void> {
    const connection = this.connections.get(connectionId);
    if (!connection) {
      return;
    }

    try {
      // Supabase clients don't need explicit cleanup, but we can clear any timers
      // The HTTP connections will be cleaned up by the garbage collector
      
      this.connections.delete(connectionId);
      this.idleConnections.delete(connectionId);
      
      if (connection.type === 'anon') {
        this.stats.anonConnections--;
      } else {
        this.stats.serviceConnections--;
      }
      
      this.updateStats();
      
      this.logger.debug('Supabase connection destroyed', { connectionId, type: connection.type });
      this.emit('connectionDestroyed', connection);
    } catch (error) {
      this.logger.error('Failed to destroy Supabase connection', error instanceof Error ? error : undefined, {
        connectionId,
      });
    }
  }

  /**
   * Get pool statistics
   */
  getStats(): PoolStats {
    this.updateStats();
    return { ...this.stats };
  }

  /**
   * Get all connections (for debugging)
   */
  getConnections(): SupabaseConnection[] {
    return Array.from(this.connections.values());
  }

  /**
   * Perform health check on a connection
   */
  async healthCheck(connection: SupabaseConnection): Promise<boolean> {
    try {
      // Simple health check: try to access the auth endpoint
      const { error } = await connection.client.auth.getSession();
      
      if (error && error.message !== 'No session') {
        // 'No session' is expected for anon connections
        connection.errors++;
        connection.isHealthy = false;
        return false;
      }
      
      connection.isHealthy = true;
      return true;
    } catch (error) {
      connection.errors++;
      connection.isHealthy = false;
      this.logger.debug('Supabase connection health check failed', {
        connectionId: connection.id,
        type: connection.type,
        error: error instanceof Error ? error.message : String(error),
      });
      return false;
    }
  }

  /**
   * Shutdown the connection pool
   */
  async shutdown(): Promise<void> {
    this.isShuttingDown = true;
    
    // Stop timers
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
      this.healthCheckTimer = undefined;
    }
    
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = undefined;
    }

    // Reject all waiting requests
    for (const request of this.waitingRequests) {
      clearTimeout(request.timeout);
      request.reject(new Error('Connection pool shutting down'));
    }
    this.waitingRequests.length = 0;

    // Destroy all connections
    const connectionIds = Array.from(this.connections.keys());
    await Promise.all(connectionIds.map(id => this.destroyConnection(id)));

    this.removeAllListeners();
    
    this.logger.info('Supabase connection pool shutdown complete');
    this.emit('shutdown');
  }

  /**
   * Create a new Supabase connection
   */
  private async createConnection(type: 'anon' | 'service'): Promise<SupabaseConnection> {
    const connectionId = this.generateConnectionId();
    const key = type === 'anon' ? this.config.SUPABASE_ANON_KEY : this.config.SUPABASE_SERVICE_ROLE_KEY;

    try {
      const client = createClient(this.config.SUPABASE_URL, key, {
        auth: {
          autoRefreshToken: this.config.SUPABASE_AUTO_REFRESH_TOKEN,
          persistSession: this.config.SUPABASE_PERSIST_SESSION,
          detectSessionInUrl: this.config.SUPABASE_DETECT_SESSION_IN_URL,
        },
        global: {
          headers: {
            'X-Client-Info': '@supabase/mcp-server-supabase',
            'User-Agent': '@supabase/mcp-server-supabase',
          },
        },
      });

      const connection: SupabaseConnection = {
        id: connectionId,
        client,
        type,
        createdAt: new Date(),
        lastUsed: new Date(),
        useCount: 0,
        isHealthy: true,
        isIdle: true,
        errors: 0,
        maxLifetime: this.config.SUPABASE_POOL_MAX_LIFETIME,
      };

      this.connections.set(connectionId, connection);
      this.idleConnections.add(connectionId);

      if (type === 'anon') {
        this.stats.anonConnections++;
      } else {
        this.stats.serviceConnections++;
      }

      this.logger.debug('Supabase connection created', { connectionId, type, url: this.config.SUPABASE_URL });
      this.emit('connectionCreated', connection);

      return connection;
    } catch (error) {
      this.logger.error('Failed to create Supabase connection', error instanceof Error ? error : undefined, {
        connectionId,
        type,
        url: this.config.SUPABASE_URL,
      });
      throw error;
    }
  }

  /**
   * Wait for an available connection
   */
  private async waitForConnection(type: 'anon' | 'service'): Promise<SupabaseConnection> {
    return new Promise((resolve, reject) => {
      const requestId = this.generateRequestId();
      const timeout = setTimeout(() => {
        const index = this.waitingRequests.findIndex(req => req.id === requestId);
        if (index !== -1) {
          this.waitingRequests.splice(index, 1);
        }
        reject(new Error(`Supabase connection acquire timeout after ${this.config.SUPABASE_REQUEST_TIMEOUT}ms`));
      }, this.config.SUPABASE_REQUEST_TIMEOUT);

      const request: ConnectionRequest = {
        id: requestId,
        type,
        timestamp: new Date(),
        timeout,
        resolve,
        reject,
      };

      this.waitingRequests.push(request);
      this.updateStats();
    });
  }

  /**
   * Start health check timer
   */
  private startHealthCheck(): void {
    this.healthCheckTimer = setInterval(async () => {
      await this.performHealthCheck();
    }, this.config.SUPABASE_HEALTH_CHECK_INTERVAL);
  }

  /**
   * Start cleanup timer for expired connections
   */
  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(async () => {
      await this.cleanupExpiredConnections();
    }, 60000); // Run every minute
  }

  /**
   * Perform health check on all connections
   */
  private async performHealthCheck(): Promise<void> {
    const connections = Array.from(this.connections.values());
    const healthChecks = connections.map(async (connection) => {
      try {
        await this.healthCheck(connection);
      } catch (error) {
        this.logger.warn('Health check failed for connection', {
          connectionId: connection.id,
          type: connection.type,
          error: error instanceof Error ? error.message : String(error),
        });
      }
    });

    await Promise.allSettled(healthChecks);
    this.updateStats();
  }

  /**
   * Clean up expired connections
   */
  private async cleanupExpiredConnections(): Promise<void> {
    const now = Date.now();
    const connectionsToDestroy: string[] = [];

    for (const [connectionId, connection] of this.connections) {
      const age = now - connection.createdAt.getTime();
      const idleTime = now - connection.lastUsed.getTime();

      // Remove connections that exceed max lifetime or have been idle too long
      if (age > connection.maxLifetime || 
          (connection.isIdle && idleTime > this.config.SUPABASE_POOL_IDLE_TIMEOUT)) {
        connectionsToDestroy.push(connectionId);
      }
    }

    // Destroy expired connections
    for (const connectionId of connectionsToDestroy) {
      await this.destroyConnection(connectionId);
    }

    if (connectionsToDestroy.length > 0) {
      this.logger.debug('Cleaned up expired Supabase connections', {
        count: connectionsToDestroy.length,
      });
    }
  }

  /**
   * Update pool statistics
   */
  private updateStats(): void {
    this.stats.totalConnections = this.connections.size;
    this.stats.idleConnections = this.idleConnections.size;
    this.stats.activeConnections = this.stats.totalConnections - this.stats.idleConnections;
    this.stats.waitingRequests = this.waitingRequests.length;
    
    this.stats.healthyConnections = Array.from(this.connections.values())
      .filter(conn => conn.isHealthy).length;
    this.stats.unhealthyConnections = this.stats.totalConnections - this.stats.healthyConnections;
    
    const totalUseCount = Array.from(this.connections.values())
      .reduce((sum, conn) => sum + conn.useCount, 0);
    this.stats.averageUseCount = this.stats.totalConnections > 0 ? 
      totalUseCount / this.stats.totalConnections : 0;
    
    this.stats.uptime = Date.now() - this.startTime;
  }

  /**
   * Generate unique connection ID
   */
  private generateConnectionId(): string {
    return `conn_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * Generate unique request ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }
}

/**
 * Singleton connection pool instance
 */
let poolInstance: SupabaseConnectionPool | null = null;

/**
 * Get or create the singleton connection pool
 */
export function getSupabaseConnectionPool(config: SupabaseConnectionConfig): SupabaseConnectionPool {
  if (!poolInstance) {
    poolInstance = new SupabaseConnectionPool(config);
  }
  return poolInstance;
}

/**
 * Destroy the singleton connection pool
 */
export async function destroySupabaseConnectionPool(): Promise<void> {
  if (poolInstance) {
    await poolInstance.shutdown();
    poolInstance = null;
  }
}