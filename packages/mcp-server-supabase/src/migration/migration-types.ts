import { z } from 'zod';

/**
 * Migration status enumeration
 */
export enum MigrationStatus {
  PENDING = 'pending',
  APPLIED = 'applied',
  FAILED = 'failed',
  ROLLED_BACK = 'rolled_back'
}

/**
 * Migration direction enumeration
 */
export enum MigrationDirection {
  UP = 'up',
  DOWN = 'down'
}

/**
 * Migration template types
 */
export enum MigrationTemplateType {
  CREATE_TABLE = 'create_table',
  ALTER_TABLE = 'alter_table',
  CREATE_INDEX = 'create_index',
  DATA_MIGRATION = 'data_migration',
  CUSTOM = 'custom'
}

/**
 * Base migration info structure
 */
export interface MigrationInfo {
  id: string;
  name: string;
  timestamp: Date;
  status: MigrationStatus;
  direction?: MigrationDirection;
  error?: string;
  appliedAt?: Date;
  rolledBackAt?: Date;
}

/**
 * Migration file structure
 */
export interface MigrationFile {
  id: string;
  name: string;
  filename: string;
  path: string;
  up: string;
  down?: string;
}

/**
 * Migration configuration
 */
export interface MigrationConfig {
  /**
   * Database connection configuration
   */
  database: {
    host: string;
    port: number;
    database: string;
    user: string;
    password: string;
    ssl?: boolean | object;
  };
  
  /**
   * Migration directory and file settings
   */
  migrations: {
    directory: string;
    tableName: string;
    schemaName: string;
    fileTemplate?: string;
  };
  
  /**
   * Execution settings
   */
  execution: {
    checkOrder?: boolean;
    allowReorderMigrations?: boolean;
    transactionMode?: 'each' | 'all' | 'none';
    lockTable?: boolean;
    dryRun?: boolean;
  };
}

/**
 * Migration operation result
 */
export interface MigrationResult {
  success: boolean;
  migrationsRun: MigrationInfo[];
  error?: string;
  durationMs?: number;
}

/**
 * Migration creation options
 */
export interface CreateMigrationOptions {
  name: string;
  template?: MigrationTemplateType;
  templateData?: Record<string, any>;
  directory?: string;
}

/**
 * Migration execution options
 */
export interface ExecuteMigrationOptions {
  count?: number;
  to?: string;
  force?: boolean;
  dryRun?: boolean;
  lockTable?: boolean;
  transactionMode?: 'each' | 'all' | 'none';
}

/**
 * Zod schemas for validation
 */
export const migrationNameSchema = z
  .string()
  .min(1, 'Migration name cannot be empty')
  .max(100, 'Migration name cannot exceed 100 characters')
  .regex(/^[a-zA-Z][a-zA-Z0-9_-]*$/, 'Migration name must start with a letter and contain only letters, numbers, underscores, and hyphens');

export const migrationIdSchema = z
  .string()
  .regex(/^\d{14}_[a-zA-Z][a-zA-Z0-9_-]*$/, 'Migration ID must be in format YYYYMMDDHHMMSS_name');

export const migrationCountSchema = z
  .number()
  .int()
  .min(1, 'Migration count must be at least 1')
  .max(1000, 'Migration count cannot exceed 1000')
  .optional();

export const migrationTemplateSchema = z
  .nativeEnum(MigrationTemplateType)
  .optional()
  .default(MigrationTemplateType.CUSTOM);

export const migrationDirectionSchema = z.nativeEnum(MigrationDirection);

export const createMigrationSchema = z.object({
  name: migrationNameSchema,
  template: migrationTemplateSchema,
  templateData: z.record(z.any()).optional(),
  directory: z.string().optional(),
});

export const executeMigrationSchema = z.object({
  count: migrationCountSchema,
  to: migrationIdSchema.optional(),
  force: z.boolean().optional().default(false),
  dryRun: z.boolean().optional().default(false),
  lockTable: z.boolean().optional().default(true),
  transactionMode: z.enum(['each', 'all', 'none']).optional().default('each'),
});