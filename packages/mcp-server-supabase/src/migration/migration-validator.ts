/**
 * Migration validation utilities to ensure migrations follow project conventions
 */

import { contextLogger } from '../utils/logger.js';

const logger = contextLogger.child({ component: 'MigrationValidator' });

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  suggestions: string[];
}

export interface ValidationRule {
  name: string;
  description: string;
  check: (content: string, filename: string) => ValidationResult;
}

/**
 * Validation rules for migration files
 */
export const MIGRATION_VALIDATION_RULES: ValidationRule[] = [
  {
    name: 'has_description_comment',
    description: 'Migration should have a description comment at the top',
    check: (content: string) => {
      const hasDescription = content.includes('-- Migration:') || content.includes('-- Description:');
      return {
        isValid: hasDescription,
        errors: hasDescription ? [] : ['Missing migration description comment'],
        warnings: [],
        suggestions: hasDescription ? [] : ['Add a "-- Migration: <description>" comment at the top of the file']
      };
    }
  },

  {
    name: 'has_up_down_sections',
    description: 'Migration should have clearly marked up and down sections',
    check: (content: string) => {
      const hasUpSection = content.includes('-- Up migration') || content.includes('-- UP');
      const hasDownSection = content.includes('-- Down migration') || content.includes('-- DOWN');
      
      const errors: string[] = [];
      const suggestions: string[] = [];
      
      if (!hasUpSection) {
        errors.push('Missing "-- Up migration" section marker');
        suggestions.push('Add "-- Up migration" comment before your SQL statements');
      }
      
      if (!hasDownSection) {
        errors.push('Missing "-- Down migration" section marker');
        suggestions.push('Add "-- Down migration" comment with rollback statements (can be commented out)');
      }
      
      return {
        isValid: hasUpSection && hasDownSection,
        errors,
        warnings: [],
        suggestions
      };
    }
  },

  {
    name: 'uses_if_exists_patterns',
    description: 'Migration should use IF EXISTS/IF NOT EXISTS for idempotent operations',
    check: (content: string) => {
      const hasCreateTable = /CREATE\s+TABLE\s+(?!.*IF\s+NOT\s+EXISTS)/i.test(content);
      const hasDropTable = /DROP\s+TABLE\s+(?!.*IF\s+EXISTS)/i.test(content);
      const hasCreateIndex = /CREATE\s+INDEX\s+(?!.*IF\s+NOT\s+EXISTS)/i.test(content);
      const hasDropIndex = /DROP\s+INDEX\s+(?!.*IF\s+EXISTS)/i.test(content);
      
      const warnings: string[] = [];
      const suggestions: string[] = [];
      
      if (hasCreateTable) {
        warnings.push('CREATE TABLE without IF NOT EXISTS detected');
        suggestions.push('Use "CREATE TABLE IF NOT EXISTS" for idempotent operations');
      }
      
      if (hasDropTable) {
        warnings.push('DROP TABLE without IF EXISTS detected');
        suggestions.push('Use "DROP TABLE IF EXISTS" for safe rollbacks');
      }
      
      if (hasCreateIndex) {
        warnings.push('CREATE INDEX without IF NOT EXISTS detected');
        suggestions.push('Use "CREATE INDEX IF NOT EXISTS" for idempotent operations');
      }
      
      if (hasDropIndex) {
        warnings.push('DROP INDEX without IF EXISTS detected');
        suggestions.push('Use "DROP INDEX IF EXISTS" for safe rollbacks');
      }
      
      return {
        isValid: true, // This is a warning, not an error
        errors: [],
        warnings,
        suggestions
      };
    }
  },

  {
    name: 'uses_concurrent_indexes',
    description: 'Index creation should use CONCURRENTLY for production safety',
    check: (content: string) => {
      const hasCreateIndex = /CREATE\s+INDEX/i.test(content);
      const hasConcurrently = /CREATE\s+INDEX\s+CONCURRENTLY/i.test(content);
      
      if (hasCreateIndex && !hasConcurrently) {
        return {
          isValid: true, // Warning, not error
          errors: [],
          warnings: ['CREATE INDEX without CONCURRENTLY detected'],
          suggestions: ['Use "CREATE INDEX CONCURRENTLY" for production deployments to avoid table locks']
        };
      }
      
      return {
        isValid: true,
        errors: [],
        warnings: [],
        suggestions: []
      };
    }
  },

  {
    name: 'has_audit_fields',
    description: 'Tables should include standard audit fields',
    check: (content: string) => {
      const hasCreateTable = /CREATE\s+TABLE/i.test(content);
      const hasCreatedAt = /created_at.*TIMESTAMP/i.test(content);
      const hasUpdatedAt = /updated_at.*TIMESTAMP/i.test(content);
      
      if (hasCreateTable && (!hasCreatedAt || !hasUpdatedAt)) {
        const suggestions: string[] = [];
        if (!hasCreatedAt) {
          suggestions.push('Consider adding "created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()"');
        }
        if (!hasUpdatedAt) {
          suggestions.push('Consider adding "updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()"');
        }
        
        return {
          isValid: true, // Suggestion, not error
          errors: [],
          warnings: [],
          suggestions
        };
      }
      
      return {
        isValid: true,
        errors: [],
        warnings: [],
        suggestions: []
      };
    }
  },

  {
    name: 'proper_filename_format',
    description: 'Migration filename should follow naming convention',
    check: (content: string, filename: string) => {
      // Check if filename follows pattern: timestamp_description.sql
      const filenamePattern = /^\d{3,}_[a-z0-9_]+\.sql$/;
      const isValidFormat = filenamePattern.test(filename);
      
      if (!isValidFormat) {
        return {
          isValid: false,
          errors: ['Migration filename does not follow naming convention'],
          warnings: [],
          suggestions: ['Use format: "001_descriptive_name.sql" or "20240101120000_descriptive_name.sql"']
        };
      }
      
      return {
        isValid: true,
        errors: [],
        warnings: [],
        suggestions: []
      };
    }
  },

  {
    name: 'no_hardcoded_values',
    description: 'Migration should avoid hardcoded IDs or sensitive values',
    check: (content: string) => {
      // Look for potential hardcoded UUIDs or IDs
      const hasUuidPattern = /[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/i.test(content);
      const hasHardcodedIds = /INSERT.*VALUES.*\d{6,}/i.test(content);
      
      const warnings: string[] = [];
      const suggestions: string[] = [];
      
      if (hasUuidPattern) {
        warnings.push('Potential hardcoded UUID detected');
        suggestions.push('Consider using gen_random_uuid() or variables instead of hardcoded UUIDs');
      }
      
      if (hasHardcodedIds) {
        warnings.push('Potential hardcoded ID values detected');
        suggestions.push('Avoid hardcoded IDs in data migrations - use lookups or variables');
      }
      
      return {
        isValid: true, // Warning, not error
        errors: [],
        warnings,
        suggestions
      };
    }
  }
];

/**
 * Validate a migration file content and filename
 */
export function validateMigration(content: string, filename: string): ValidationResult {
  logger.debug('Validating migration', { filename });
  
  const result: ValidationResult = {
    isValid: true,
    errors: [],
    warnings: [],
    suggestions: []
  };
  
  // Run all validation rules
  for (const rule of MIGRATION_VALIDATION_RULES) {
    try {
      const ruleResult = rule.check(content, filename);
      
      // Merge results
      result.errors.push(...ruleResult.errors);
      result.warnings.push(...ruleResult.warnings);
      result.suggestions.push(...ruleResult.suggestions);
      
      // If any rule fails, mark overall result as invalid
      if (!ruleResult.isValid) {
        result.isValid = false;
      }
      
      logger.debug('Validation rule completed', { 
        rule: rule.name, 
        isValid: ruleResult.isValid,
        errors: ruleResult.errors.length,
        warnings: ruleResult.warnings.length
      });
    } catch (error) {
      logger.error('Validation rule failed', { 
        rule: rule.name, 
        error: error instanceof Error ? error.message : String(error) 
      });
      
      result.errors.push(`Validation rule "${rule.name}" failed: ${error instanceof Error ? error.message : String(error)}`);
      result.isValid = false;
    }
  }
  
  logger.info('Migration validation completed', {
    filename,
    isValid: result.isValid,
    errorCount: result.errors.length,
    warningCount: result.warnings.length,
    suggestionCount: result.suggestions.length
  });
  
  return result;
}

/**
 * Format validation results for display
 */
export function formatValidationResults(results: ValidationResult, filename: string): string {
  const lines: string[] = [];
  
  lines.push(`\n📋 Migration Validation Results: ${filename}`);
  lines.push('='.repeat(50));
  
  if (results.isValid) {
    lines.push('✅ Migration is valid');
  } else {
    lines.push('❌ Migration has validation errors');
  }
  
  if (results.errors.length > 0) {
    lines.push('\n🚨 Errors:');
    results.errors.forEach(error => lines.push(`   • ${error}`));
  }
  
  if (results.warnings.length > 0) {
    lines.push('\n⚠️  Warnings:');
    results.warnings.forEach(warning => lines.push(`   • ${warning}`));
  }
  
  if (results.suggestions.length > 0) {
    lines.push('\n💡 Suggestions:');
    results.suggestions.forEach(suggestion => lines.push(`   • ${suggestion}`));
  }
  
  return lines.join('\n');
}
