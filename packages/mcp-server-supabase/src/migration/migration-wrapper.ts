import fs from 'fs/promises';
import path from 'path';
import { runner as migrate } from 'node-pg-migrate';
import {
  MigrationConfig,
  MigrationInfo,
  MigrationResult,
  CreateMigrationOptions,
  ExecuteMigrationOptions,
  MigrationStatus,
  MigrationDirection,
  MigrationTemplateType
} from './migration-types.js';
import { validateMigration, formatValidationResults } from './migration-validator.js';
import { 
  createMigrationConfig, 
  validateMigrationConfig,
  getNodePgMigrateConfig,
  generateMigrationFilename,
  getMigrationFilePath
} from './migration-config.js';
import type { LocalConfig } from '../config/index.js';
import { contextLogger } from '../utils/logger.js';
import { DatabaseError, ConfigurationError } from '../utils/errors.js';

const logger = contextLogger.child({ component: 'MigrationWrapper' });

/**
 * Migration templates
 */
const MIGRATION_TEMPLATES = {
  [MigrationTemplateType.CREATE_TABLE]: `-- Migration: Create {tableName} table
-- Up migration
CREATE TABLE IF NOT EXISTS {tableName} (
  id SERIAL PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Down migration
-- DROP TABLE IF EXISTS {tableName};`,

  [MigrationTemplateType.ALTER_TABLE]: `-- Migration: Alter {tableName} table
-- Up migration
ALTER TABLE {tableName}
  ADD COLUMN IF NOT EXISTS {columnName} {columnType};

-- Down migration
-- ALTER TABLE {tableName} DROP COLUMN IF EXISTS {columnName};`,

  [MigrationTemplateType.CREATE_INDEX]: `-- Migration: Create index on {tableName}
-- Up migration
CREATE INDEX CONCURRENTLY IF NOT EXISTS {indexName}
  ON {tableName} ({columnName});

-- Down migration
-- DROP INDEX IF EXISTS {indexName};`,

  [MigrationTemplateType.DATA_MIGRATION]: `-- Migration: Data migration for {description}
-- Up migration
-- INSERT, UPDATE, or other data operations here

-- Down migration
-- Reverse data operations here`,

  [MigrationTemplateType.CUSTOM]: `-- Migration: {description}
-- Up migration
-- Add your SQL statements here

-- Down migration
-- Add rollback SQL statements here`
};

/**
 * Main migration wrapper class
 */
export class MigrationWrapper {
  private config: MigrationConfig;
  private nodePgMigrateConfig: any;

  constructor(localConfig: LocalConfig) {
    logger.debug('Initializing migration wrapper');
    
    try {
      this.config = createMigrationConfig(localConfig);
      validateMigrationConfig(this.config);
      this.nodePgMigrateConfig = getNodePgMigrateConfig(this.config);
      
      logger.info('Migration wrapper initialized successfully', {
        host: this.config.database.host,
        database: this.config.database.database,
        migrationsDir: this.config.migrations.directory
      });
    } catch (error) {
      logger.error('Failed to initialize migration wrapper', { 
        error: error instanceof Error ? error.message : String(error) 
      });
      
      if (error instanceof Error && error.message.includes('DATABASE_URL')) {
        throw new ConfigurationError(
          'Invalid database configuration for migrations',
          { cause: error }
        );
      }
      
      throw new DatabaseError(
        `Migration wrapper initialization failed: ${error instanceof Error ? error.message : String(error)}`,
        { cause: error instanceof Error ? error : undefined }
      );
    }
  }

  /**
   * Ensure migrations directory exists
   */
  private async ensureMigrationsDirectory(): Promise<void> {
    try {
      await fs.access(this.config.migrations.directory);
      logger.debug('Migrations directory exists', { directory: this.config.migrations.directory });
    } catch {
      logger.info('Creating migrations directory', { directory: this.config.migrations.directory });
      await fs.mkdir(this.config.migrations.directory, { recursive: true });
    }
  }

  /**
   * Create a new migration file
   */
  async createMigration(options: CreateMigrationOptions): Promise<{ filename: string; path: string; content: string }> {
    logger.info('Creating new migration', { name: options.name, template: options.template });
    
    try {
      await this.ensureMigrationsDirectory();
      
      const filename = generateMigrationFilename(this.config, options.name);
      const filePath = getMigrationFilePath(this.config, filename);
      
      // Generate migration content from template
      let content = '';
      const template = options.template || MigrationTemplateType.CUSTOM;
      
      if (template in MIGRATION_TEMPLATES) {
        content = MIGRATION_TEMPLATES[template];
        
        // Replace template variables
        if (options.templateData) {
          for (const [key, value] of Object.entries(options.templateData)) {
            content = content.replace(new RegExp(`{${key}}`, 'g'), String(value));
          }
        }
        
        // Replace common variables
        content = content.replace(/{description}/g, options.name);
        content = content.replace(/{name}/g, options.name);
      }
      
      // Check if file already exists
      try {
        await fs.access(filePath);
        throw new Error(`Migration file already exists: ${filename}`);
      } catch (error) {
        if ((error as NodeJS.ErrnoException).code !== 'ENOENT') {
          throw error;
        }
      }
      
      // Validate migration content
      const validationResult = validateMigration(content, filename);

      if (!validationResult.isValid) {
        logger.warn('Migration validation failed', {
          filename,
          errors: validationResult.errors,
          warnings: validationResult.warnings
        });

        // Log validation results but don't fail - allow user to decide
        console.log(formatValidationResults(validationResult, filename));
      } else if (validationResult.warnings.length > 0 || validationResult.suggestions.length > 0) {
        // Show warnings and suggestions even for valid migrations
        console.log(formatValidationResults(validationResult, filename));
      }

      // Write migration file
      await fs.writeFile(filePath, content, 'utf8');

      logger.info('Migration file created successfully', {
        filename,
        path: filePath,
        template,
        size: content.length,
        validationPassed: validationResult.isValid
      });

      return {
        filename,
        path: filePath,
        content
      };
    } catch (error) {
      logger.error('Failed to create migration', { 
        name: options.name,
        error: error instanceof Error ? error.message : String(error) 
      });
      
      throw new DatabaseError(
        `Failed to create migration "${options.name}": ${error instanceof Error ? error.message : String(error)}`,
        { cause: error instanceof Error ? error : undefined }
      );
    }
  }

  /**
   * Run pending migrations
   */
  async up(options: ExecuteMigrationOptions = {}): Promise<MigrationResult> {
    logger.info('Running migrations up', options);
    
    const startTime = Date.now();
    
    try {
      const config = { 
        ...this.nodePgMigrateConfig,
        dryRun: options.dryRun || this.nodePgMigrateConfig.dryRun,
        count: options.count,
        to: options.to,
      };
      
      logger.debug('Executing node-pg-migrate up', { config: { ...config, databaseUrl: '[REDACTED]' } });
      
      const result = await migrate({
        ...config,
        direction: 'up',
        noLock: !options.lockTable && !config.lock,
      });
      
      const duration = Date.now() - startTime;
      
      const migrationsRun: MigrationInfo[] = result.map((migration: any) => ({
        id: migration.name,
        name: migration.name,
        timestamp: new Date(migration.timestamp || Date.now()),
        status: MigrationStatus.APPLIED,
        direction: MigrationDirection.UP,
        appliedAt: new Date()
      }));
      
      logger.info('Migrations completed successfully', {
        count: migrationsRun.length,
        durationMs: duration,
        migrations: migrationsRun.map(m => m.name)
      });
      
      return {
        success: true,
        migrationsRun,
        durationMs: duration
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);
      
      logger.error('Migration up failed', { 
        error: errorMessage,
        durationMs: duration,
        options 
      });
      
      return {
        success: false,
        migrationsRun: [],
        error: errorMessage,
        durationMs: duration
      };
    }
  }

  /**
   * Rollback migrations
   */
  async down(options: ExecuteMigrationOptions = {}): Promise<MigrationResult> {
    logger.info('Rolling back migrations', options);
    
    const startTime = Date.now();
    
    try {
      const config = { 
        ...this.nodePgMigrateConfig,
        dryRun: options.dryRun || this.nodePgMigrateConfig.dryRun,
        count: options.count || 1,
        to: options.to,
      };
      
      logger.debug('Executing node-pg-migrate down', { config: { ...config, databaseUrl: '[REDACTED]' } });
      
      const result = await migrate({
        ...config,
        direction: 'down',
        noLock: !options.lockTable && !config.lock,
      });
      
      const duration = Date.now() - startTime;
      
      const migrationsRun: MigrationInfo[] = result.map((migration: any) => ({
        id: migration.name,
        name: migration.name,
        timestamp: new Date(migration.timestamp || Date.now()),
        status: MigrationStatus.ROLLED_BACK,
        direction: MigrationDirection.DOWN,
        rolledBackAt: new Date()
      }));
      
      logger.info('Rollback completed successfully', {
        count: migrationsRun.length,
        durationMs: duration,
        migrations: migrationsRun.map(m => m.name)
      });
      
      return {
        success: true,
        migrationsRun,
        durationMs: duration
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);
      
      logger.error('Migration rollback failed', { 
        error: errorMessage,
        durationMs: duration,
        options 
      });
      
      return {
        success: false,
        migrationsRun: [],
        error: errorMessage,
        durationMs: duration
      };
    }
  }

  /**
   * Get migration status
   */
  async getStatus(): Promise<MigrationInfo[]> {
    logger.debug('Getting migration status');
    
    try {
      // Get applied migrations from database
      const appliedMigrations = await migrate({
        ...this.nodePgMigrateConfig,
        direction: 'up',
        dryRun: true,
        count: 0
      });
      
      // Read migration files from directory
      const files = await fs.readdir(this.config.migrations.directory);
      const migrationFiles = files
        .filter(file => file.endsWith('.sql'))
        .sort();
      
      const status: MigrationInfo[] = [];
      
      for (const file of migrationFiles) {
        const migrationName = path.basename(file, '.sql');
        const isApplied = appliedMigrations.some((m: any) => m.name === migrationName);
        
        status.push({
          id: migrationName,
          name: migrationName,
          timestamp: new Date(), // TODO: Extract from filename or file stats
          status: isApplied ? MigrationStatus.APPLIED : MigrationStatus.PENDING
        });
      }
      
      logger.debug('Migration status retrieved', { 
        total: status.length,
        applied: status.filter(m => m.status === MigrationStatus.APPLIED).length,
        pending: status.filter(m => m.status === MigrationStatus.PENDING).length
      });
      
      return status;
    } catch (error) {
      logger.error('Failed to get migration status', { 
        error: error instanceof Error ? error.message : String(error) 
      });
      
      throw new DatabaseError(
        `Failed to get migration status: ${error instanceof Error ? error.message : String(error)}`,
        { cause: error instanceof Error ? error : undefined }
      );
    }
  }

  /**
   * Test database connection
   */
  async testConnection(): Promise<boolean> {
    logger.debug('Testing database connection for migrations');
    
    try {
      // Try to get migration status as a connection test
      await migrate({
        ...this.nodePgMigrateConfig,
        direction: 'up',
        dryRun: true,
        count: 0
      });
      
      logger.debug('Database connection test successful');
      return true;
    } catch (error) {
      logger.error('Database connection test failed', { 
        error: error instanceof Error ? error.message : String(error) 
      });
      return false;
    }
  }

  /**
   * Get configuration info
   */
  getConfig(): MigrationConfig {
    return { ...this.config };
  }
}