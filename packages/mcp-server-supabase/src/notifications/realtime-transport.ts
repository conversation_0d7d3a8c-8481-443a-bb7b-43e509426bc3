/**
 * Real-time Transport Manager
 * 
 * Unified manager for WebSocket and Server-Sent Events transports,
 * providing a single interface for real-time protocol communication.
 */

import { EventEmitter } from 'events';
import { Server as HttpServer } from 'http';
import { contextLogger } from '../utils/logger.js';
import type { ConfigManager } from '../config/config-manager.js';
import { WebSocketTransport, type WebSocketTransportOptions } from './websocket-transport.js';
import { SSETransport, type SSETransportOptions } from './sse-transport.js';
import type { ProtocolMessage, ClientConnection } from './protocol-communication.js';
import type { NotificationPayload } from './notification-manager.js';

export interface RealTimeTransportOptions {
  enableWebSocket?: boolean;
  enableSSE?: boolean;
  httpServer?: HttpServer;
  websocketOptions?: WebSocketTransportOptions;
  sseOptions?: SSETransportOptions;
}

export interface TransportStats {
  websocket: {
    enabled: boolean;
    isRunning: boolean;
    connectedClients: number;
    port?: number;
    host?: string;
  };
  sse: {
    enabled: boolean;
    isRunning: boolean;
    connectedClients: number;
    path?: string;
  };
  total: {
    connectedClients: number;
    totalTransports: number;
    activeTransports: number;
  };
}

/**
 * Unified real-time transport manager for WebSocket and SSE
 */
export class RealTimeTransport extends EventEmitter {
  private readonly logger = contextLogger.child({ component: 'RealTimeTransport' });
  private readonly config?: ConfigManager;
  private readonly options: Required<RealTimeTransportOptions>;
  
  private websocketTransport?: WebSocketTransport;
  private sseTransport?: SSETransport;
  private httpServer?: HttpServer;
  private isRunning = false;

  constructor(options: RealTimeTransportOptions = {}, config?: ConfigManager) {
    super();
    
    this.config = config;
    this.options = {
      enableWebSocket: options.enableWebSocket ?? config?.get('REALTIME_ENABLE_WEBSOCKET', true) ?? true,
      enableSSE: options.enableSSE ?? config?.get('REALTIME_ENABLE_SSE', true) ?? true,
      httpServer: options.httpServer ?? undefined,
      websocketOptions: options.websocketOptions ?? {},
      sseOptions: options.sseOptions ?? {},
    };

    this.logger.info('Real-time transport manager initialized', {
      enableWebSocket: this.options.enableWebSocket,
      enableSSE: this.options.enableSSE,
    });
  }

  /**
   * Start all enabled transports
   */
  async start(httpServer?: HttpServer): Promise<void> {
    if (this.isRunning) {
      this.logger.warn('Real-time transport already running');
      return;
    }

    try {
      this.httpServer = httpServer || this.options.httpServer;

      // Initialize WebSocket transport
      if (this.options.enableWebSocket) {
        this.websocketTransport = new WebSocketTransport(this.options.websocketOptions, this.config);
        this.setupWebSocketEventHandlers();
        await this.websocketTransport.start();
      }

      // Initialize SSE transport
      if (this.options.enableSSE) {
        this.sseTransport = new SSETransport(this.options.sseOptions, this.config);
        this.setupSSEEventHandlers();
        await this.sseTransport.start();
        
        // Setup HTTP server route for SSE if available
        if (this.httpServer) {
          this.setupSSERoute();
        }
      }

      this.isRunning = true;
      
      this.logger.info('Real-time transport started', {
        websocketEnabled: !!this.websocketTransport,
        sseEnabled: !!this.sseTransport,
        httpServerProvided: !!this.httpServer,
      });

      this.emit('started');
    } catch (error) {
      this.logger.error('Failed to start real-time transport', error instanceof Error ? error : undefined);
      throw error;
    }
  }

  /**
   * Stop all transports
   */
  async stop(): Promise<void> {
    if (!this.isRunning) {
      return;
    }

    try {
      // Stop WebSocket transport
      if (this.websocketTransport) {
        await this.websocketTransport.stop();
        this.websocketTransport = undefined;
      }

      // Stop SSE transport
      if (this.sseTransport) {
        await this.sseTransport.stop();
        this.sseTransport = undefined;
      }

      this.isRunning = false;
      
      this.logger.info('Real-time transport stopped');
      this.emit('stopped');
    } catch (error) {
      this.logger.error('Error stopping real-time transport', error instanceof Error ? error : undefined);
      throw error;
    }
  }

  /**
   * Send a protocol message to all connected clients across all transports
   */
  async sendMessage(message: ProtocolMessage, targetClientId?: string): Promise<void> {
    const promises: Promise<void>[] = [];

    // Send via WebSocket
    if (this.websocketTransport?.isActive()) {
      promises.push(this.websocketTransport.sendMessage(message, targetClientId));
    }

    // Send via SSE
    if (this.sseTransport?.isActive()) {
      promises.push(this.sseTransport.sendMessage(message, targetClientId));
    }

    if (promises.length === 0) {
      this.logger.warn('No active transports available for sending message', {
        messageId: message.id,
        targetClientId,
      });
      return;
    }

    try {
      await Promise.allSettled(promises);
      
      this.logger.debug('Message sent via real-time transports', {
        messageId: message.id,
        type: message.type,
        transports: promises.length,
        targetClientId,
      });
    } catch (error) {
      this.logger.error('Failed to send message via real-time transports', error instanceof Error ? error : undefined, {
        messageId: message.id,
        targetClientId,
      });
      throw error;
    }
  }

  /**
   * Send a notification to all connected clients across all transports
   */
  async sendNotification(notification: NotificationPayload, targetClientId?: string): Promise<void> {
    const promises: Promise<void>[] = [];

    // Send via WebSocket
    if (this.websocketTransport?.isActive()) {
      promises.push(this.websocketTransport.sendNotification(notification, targetClientId));
    }

    // Send via SSE
    if (this.sseTransport?.isActive()) {
      promises.push(this.sseTransport.sendNotification(notification, targetClientId));
    }

    if (promises.length === 0) {
      this.logger.warn('No active transports available for sending notification', {
        notificationId: notification.id,
        type: notification.type,
        targetClientId,
      });
      return;
    }

    try {
      await Promise.allSettled(promises);
      
      this.logger.debug('Notification sent via real-time transports', {
        notificationId: notification.id,
        type: notification.type,
        transports: promises.length,
        targetClientId,
      });
    } catch (error) {
      this.logger.error('Failed to send notification via real-time transports', error instanceof Error ? error : undefined, {
        notificationId: notification.id,
        targetClientId,
      });
      throw error;
    }
  }

  /**
   * Get all connected clients across all transports
   */
  getAllClients(): ClientConnection[] {
    const clients: ClientConnection[] = [];

    if (this.websocketTransport) {
      clients.push(...this.websocketTransport.getClients());
    }

    if (this.sseTransport) {
      clients.push(...this.sseTransport.getClients());
    }

    return clients;
  }

  /**
   * Get client by ID across all transports
   */
  getClient(clientId: string): ClientConnection | undefined {
    if (this.websocketTransport) {
      const client = this.websocketTransport.getClient(clientId);
      if (client) return client;
    }

    if (this.sseTransport) {
      const client = this.sseTransport.getClient(clientId);
      if (client) return client;
    }

    return undefined;
  }

  /**
   * Check if any transport is running
   */
  isActive(): boolean {
    return this.isRunning && (
      this.websocketTransport?.isActive() || 
      this.sseTransport?.isActive()
    ) || false;
  }

  /**
   * Get comprehensive transport statistics
   */
  getStats(): TransportStats {
    const websocketStats = this.websocketTransport?.getStats();
    const sseStats = this.sseTransport?.getStats();

    const totalClients = (websocketStats?.connectedClients || 0) + (sseStats?.connectedClients || 0);
    const totalTransports = (this.options.enableWebSocket ? 1 : 0) + (this.options.enableSSE ? 1 : 0);
    const activeTransports = (websocketStats?.isRunning ? 1 : 0) + (sseStats?.isRunning ? 1 : 0);

    return {
      websocket: {
        enabled: this.options.enableWebSocket,
        isRunning: websocketStats?.isRunning || false,
        connectedClients: websocketStats?.connectedClients || 0,
        port: websocketStats?.port,
        host: websocketStats?.host,
      },
      sse: {
        enabled: this.options.enableSSE,
        isRunning: sseStats?.isRunning || false,
        connectedClients: sseStats?.connectedClients || 0,
        path: sseStats?.path,
      },
      total: {
        connectedClients: totalClients,
        totalTransports,
        activeTransports,
      },
    };
  }

  /**
   * Get WebSocket transport instance
   */
  getWebSocketTransport(): WebSocketTransport | undefined {
    return this.websocketTransport;
  }

  /**
   * Get SSE transport instance
   */
  getSSETransport(): SSETransport | undefined {
    return this.sseTransport;
  }

  /**
   * Setup WebSocket event handlers
   */
  private setupWebSocketEventHandlers(): void {
    if (!this.websocketTransport) return;

    this.websocketTransport.on('clientConnected', (client) => {
      this.logger.debug('WebSocket client connected', { clientId: client.id });
      this.emit('clientConnected', client, 'websocket');
    });

    this.websocketTransport.on('clientDisconnected', (client) => {
      this.logger.debug('WebSocket client disconnected', { clientId: client.id });
      this.emit('clientDisconnected', client, 'websocket');
    });

    this.websocketTransport.on('messageReceived', (client, message) => {
      this.logger.debug('WebSocket message received', { 
        clientId: client.id, 
        messageId: message.id,
        type: message.type,
      });
      this.emit('messageReceived', client, message, 'websocket');
    });

    this.websocketTransport.on('error', (error) => {
      this.logger.error('WebSocket transport error', error);
      this.emit('transportError', 'websocket', error);
    });
  }

  /**
   * Setup SSE event handlers
   */
  private setupSSEEventHandlers(): void {
    if (!this.sseTransport) return;

    this.sseTransport.on('clientConnected', (client) => {
      this.logger.debug('SSE client connected', { clientId: client.id });
      this.emit('clientConnected', client, 'sse');
    });

    this.sseTransport.on('clientDisconnected', (client) => {
      this.logger.debug('SSE client disconnected', { clientId: client.id });
      this.emit('clientDisconnected', client, 'sse');
    });

    this.sseTransport.on('error', (error) => {
      this.logger.error('SSE transport error', error);
      this.emit('transportError', 'sse', error);
    });
  }

  /**
   * Setup SSE route on HTTP server
   */
  private setupSSERoute(): void {
    if (!this.httpServer || !this.sseTransport) {
      return;
    }

    const ssePath = this.options.sseOptions.path || '/events';
    
    this.httpServer.on('request', (req, res) => {
      if (req.url === ssePath && req.method === 'GET') {
        this.sseTransport!.handleConnection(req, res);
      }
    });

    this.logger.info('SSE route setup on HTTP server', { path: ssePath });
  }
}
