/**
 * Notification Manager for MCP Server
 * 
 * Handles real-time notifications and event-driven communication
 * following the MCP protocol specifications.
 */

import { EventEmitter } from 'events';
import { contextLogger } from '../utils/logger.js';
import { ConfigurationError } from '../utils/errors.js';
import type { ConfigManager } from '../config/index.js';

export interface NotificationPayload {
  id: string;
  type: string;
  timestamp: Date;
  source: string;
  data: Record<string, unknown>;
  priority: 'low' | 'medium' | 'high' | 'critical';
  ttl?: number; // Time to live in milliseconds
  targetClient?: string; // Optional client targeting
}

export interface NotificationHandler {
  id: string;
  type: string | string[];
  handler: (payload: NotificationPayload) => Promise<void> | void;
  filter?: (payload: NotificationPayload) => boolean;
  priority?: number; // Handler execution priority (higher = executed first)
}

export interface NotificationQueueItem {
  payload: NotificationPayload;
  attempts: number;
  maxAttempts: number;
  nextRetry: Date;
  createdAt: Date;
}

export interface NotificationStats {
  totalSent: number;
  totalFailed: number;
  totalQueued: number;
  totalHandlers: number;
  averageProcessingTime: number;
  uptime: number;
}

export class NotificationManager extends EventEmitter {
  private readonly logger = contextLogger.child({ component: 'NotificationManager' });
  private handlers = new Map<string, NotificationHandler>();
  private messageQueue: NotificationQueueItem[] = [];
  private isProcessing = false;
  private stats: NotificationStats;
  private readonly startTime = Date.now();
  private processingTimes: number[] = [];
  private maxProcessingTimes = 100; // Keep last 100 processing times for average
  private queueProcessingInterval?: NodeJS.Timeout;
  private config?: ConfigManager;

  // Configuration
  private readonly maxQueueSize: number;
  private readonly maxRetryAttempts: number;
  private readonly retryDelay: number;
  private readonly queueProcessingIntervalMs: number;
  private readonly enableThrottling: boolean;
  private readonly throttleWindowMs: number;
  private readonly maxNotificationsPerWindow: number;

  // Throttling state
  private throttleWindow = new Map<string, { count: number; windowStart: number }>();

  constructor(config?: ConfigManager) {
    super();
    this.config = config;

    // Load configuration
    this.maxQueueSize = config?.get('NOTIFICATION_MAX_QUEUE_SIZE', 1000) ?? 1000;
    this.maxRetryAttempts = config?.get('NOTIFICATION_MAX_RETRY_ATTEMPTS', 3) ?? 3;
    this.retryDelay = config?.get('NOTIFICATION_RETRY_DELAY', 5000) ?? 5000;
    this.queueProcessingIntervalMs = config?.get('NOTIFICATION_QUEUE_INTERVAL', 1000) ?? 1000;
    this.enableThrottling = config?.get('NOTIFICATION_ENABLE_THROTTLING', true) ?? true;
    this.throttleWindowMs = config?.get('NOTIFICATION_THROTTLE_WINDOW', 60000) ?? 60000; // 1 minute
    this.maxNotificationsPerWindow = config?.get('NOTIFICATION_MAX_PER_WINDOW', 100) ?? 100;

    this.stats = {
      totalSent: 0,
      totalFailed: 0,
      totalQueued: 0,
      totalHandlers: 0,
      averageProcessingTime: 0,
      uptime: 0,
    };

    this.startQueueProcessing();
    this.logger.info('Notification manager initialized', {
      maxQueueSize: this.maxQueueSize,
      maxRetryAttempts: this.maxRetryAttempts,
      enableThrottling: this.enableThrottling,
    });
  }

  /**
   * Register a notification handler
   */
  registerHandler(handler: NotificationHandler): void {
    if (this.handlers.has(handler.id)) {
      throw new ConfigurationError(`Handler with id '${handler.id}' already exists`);
    }

    this.handlers.set(handler.id, handler);
    this.stats.totalHandlers = this.handlers.size;
    
    this.logger.debug('Notification handler registered', {
      id: handler.id,
      types: Array.isArray(handler.type) ? handler.type : [handler.type],
      priority: handler.priority ?? 0,
    });

    this.emit('handlerRegistered', handler);
  }

  /**
   * Unregister a notification handler
   */
  unregisterHandler(handlerId: string): boolean {
    const removed = this.handlers.delete(handlerId);
    if (removed) {
      this.stats.totalHandlers = this.handlers.size;
      this.logger.debug('Notification handler unregistered', { id: handlerId });
      this.emit('handlerUnregistered', handlerId);
    }
    return removed;
  }

  /**
   * Send a notification immediately or queue it for processing
   */
  async notify(payload: NotificationPayload): Promise<void> {
    try {
      // Validate payload
      this.validatePayload(payload);

      // Check throttling
      if (this.enableThrottling && this.isThrottled(payload.source)) {
        this.logger.warn('Notification throttled', {
          source: payload.source,
          type: payload.type,
          id: payload.id,
        });
        return;
      }

      // Try immediate processing first
      if (!this.isProcessing && this.messageQueue.length === 0) {
        await this.processNotification(payload);
      } else {
        // Queue for later processing
        this.queueNotification(payload);
      }
    } catch (error) {
      this.logger.error('Failed to send notification', error instanceof Error ? error : undefined, {
        payloadId: payload.id,
        payloadType: payload.type,
      });
      this.stats.totalFailed++;
      throw error;
    }
  }

  /**
   * Send a notification with simple parameters
   */
  async notifySimple(
    type: string,
    data: Record<string, unknown>,
    options: {
      source?: string;
      priority?: NotificationPayload['priority'];
      ttl?: number;
      targetClient?: string;
    } = {}
  ): Promise<void> {
    const payload: NotificationPayload = {
      id: this.generateNotificationId(),
      type,
      timestamp: new Date(),
      source: options.source ?? 'system',
      data,
      priority: options.priority ?? 'medium',
      ttl: options.ttl,
      targetClient: options.targetClient,
    };

    await this.notify(payload);
  }

  /**
   * Get handlers matching a notification type
   */
  getHandlersForType(type: string): NotificationHandler[] {
    const handlers: NotificationHandler[] = [];

    for (const handler of this.handlers.values()) {
      const types = Array.isArray(handler.type) ? handler.type : [handler.type];
      if (types.includes(type) || types.includes('*')) {
        handlers.push(handler);
      }
    }

    // Sort by priority (higher first)
    return handlers.sort((a, b) => (b.priority ?? 0) - (a.priority ?? 0));
  }

  /**
   * Get notification statistics
   */
  getStats(): NotificationStats {
    return {
      ...this.stats,
      uptime: Date.now() - this.startTime,
      averageProcessingTime: this.calculateAverageProcessingTime(),
      totalQueued: this.messageQueue.length,
    };
  }

  /**
   * Clear all handlers
   */
  clearHandlers(): void {
    const count = this.handlers.size;
    this.handlers.clear();
    this.stats.totalHandlers = 0;
    this.logger.info('All notification handlers cleared', { count });
    this.emit('handlersCleared', count);
  }

  /**
   * Shutdown the notification manager
   */
  shutdown(): void {
    if (this.queueProcessingInterval) {
      clearInterval(this.queueProcessingInterval);
      this.queueProcessingInterval = undefined;
    }

    this.clearHandlers();
    this.messageQueue.length = 0;
    this.removeAllListeners();
    
    this.logger.info('Notification manager shutdown complete');
  }

  /**
   * Process a single notification
   */
  private async processNotification(payload: NotificationPayload): Promise<void> {
    const startTime = Date.now();

    try {
      // Check TTL
      if (payload.ttl && Date.now() - payload.timestamp.getTime() > payload.ttl) {
        this.logger.warn('Notification expired, skipping', {
          id: payload.id,
          type: payload.type,
          age: Date.now() - payload.timestamp.getTime(),
          ttl: payload.ttl,
        });
        return;
      }

      const handlers = this.getHandlersForType(payload.type);
      
      if (handlers.length === 0) {
        this.logger.debug('No handlers found for notification type', {
          type: payload.type,
          id: payload.id,
        });
        return;
      }

      // Execute handlers
      const promises = handlers.map(async (handler) => {
        try {
          // Apply filter if present
          if (handler.filter && !handler.filter(payload)) {
            this.logger.debug('Notification filtered out by handler', {
              handlerId: handler.id,
              notificationId: payload.id,
            });
            return;
          }

          await handler.handler(payload);
          
          this.logger.debug('Notification handler executed successfully', {
            handlerId: handler.id,
            notificationId: payload.id,
          });
        } catch (error) {
          this.logger.error('Notification handler failed', error instanceof Error ? error : undefined, {
            handlerId: handler.id,
            notificationId: payload.id,
          });
          throw error;
        }
      });

      await Promise.allSettled(promises);
      this.stats.totalSent++;

    } catch (error) {
      this.logger.error('Failed to process notification', error instanceof Error ? error : undefined, {
        id: payload.id,
        type: payload.type,
      });
      this.stats.totalFailed++;
      throw error;
    } finally {
      const processingTime = Date.now() - startTime;
      this.recordProcessingTime(processingTime);
    }
  }

  /**
   * Queue a notification for later processing
   */
  private queueNotification(payload: NotificationPayload): void {
    if (this.messageQueue.length >= this.maxQueueSize) {
      // Remove oldest item to make room
      const removed = this.messageQueue.shift();
      this.logger.warn('Message queue full, removing oldest notification', {
        removedId: removed?.payload.id,
        queueSize: this.messageQueue.length,
      });
    }

    const queueItem: NotificationQueueItem = {
      payload,
      attempts: 0,
      maxAttempts: this.maxRetryAttempts,
      nextRetry: new Date(),
      createdAt: new Date(),
    };

    this.messageQueue.push(queueItem);
    this.logger.debug('Notification queued', {
      id: payload.id,
      type: payload.type,
      queueSize: this.messageQueue.length,
    });
  }

  /**
   * Start queue processing
   */
  private startQueueProcessing(): void {
    this.queueProcessingInterval = setInterval(async () => {
      await this.processMessageQueue();
    }, this.queueProcessingIntervalMs);
  }

  /**
   * Process queued messages
   */
  private async processMessageQueue(): Promise<void> {
    if (this.isProcessing || this.messageQueue.length === 0) {
      return;
    }

    this.isProcessing = true;

    try {
      const now = new Date();
      const itemsToProcess = this.messageQueue.filter(item => item.nextRetry <= now);

      for (const item of itemsToProcess) {
        try {
          await this.processNotification(item.payload);
          
          // Remove successful item from queue
          const index = this.messageQueue.indexOf(item);
          if (index !== -1) {
            this.messageQueue.splice(index, 1);
          }
        } catch (error) {
          item.attempts++;
          
          if (item.attempts >= item.maxAttempts) {
            // Remove failed item from queue
            const index = this.messageQueue.indexOf(item);
            if (index !== -1) {
              this.messageQueue.splice(index, 1);
            }
            
            this.logger.error('Notification failed after max attempts', error instanceof Error ? error : undefined, {
              id: item.payload.id,
              attempts: item.attempts,
            });
          } else {
            // Schedule retry
            item.nextRetry = new Date(now.getTime() + this.retryDelay * item.attempts);
            
            this.logger.warn('Notification failed, scheduling retry', {
              id: item.payload.id,
              attempt: item.attempts,
              nextRetry: item.nextRetry,
            });
          }
        }
      }
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Validate notification payload
   */
  private validatePayload(payload: NotificationPayload): void {
    if (!payload.id || typeof payload.id !== 'string') {
      throw new ConfigurationError('Notification payload must have a valid id');
    }
    
    if (!payload.type || typeof payload.type !== 'string') {
      throw new ConfigurationError('Notification payload must have a valid type');
    }
    
    if (!payload.timestamp || !(payload.timestamp instanceof Date)) {
      throw new ConfigurationError('Notification payload must have a valid timestamp');
    }
    
    if (!payload.source || typeof payload.source !== 'string') {
      throw new ConfigurationError('Notification payload must have a valid source');
    }
    
    if (!payload.data || typeof payload.data !== 'object') {
      throw new ConfigurationError('Notification payload must have valid data object');
    }
  }

  /**
   * Check if source is throttled
   */
  private isThrottled(source: string): boolean {
    const now = Date.now();
    const window = this.throttleWindow.get(source);

    if (!window) {
      this.throttleWindow.set(source, { count: 1, windowStart: now });
      return false;
    }

    // Reset window if expired
    if (now - window.windowStart > this.throttleWindowMs) {
      this.throttleWindow.set(source, { count: 1, windowStart: now });
      return false;
    }

    // Check if throttled
    if (window.count >= this.maxNotificationsPerWindow) {
      return true;
    }

    // Increment count
    window.count++;
    return false;
  }

  /**
   * Generate unique notification ID
   */
  private generateNotificationId(): string {
    return `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Record processing time for statistics
   */
  private recordProcessingTime(time: number): void {
    this.processingTimes.push(time);
    if (this.processingTimes.length > this.maxProcessingTimes) {
      this.processingTimes.shift();
    }
  }

  /**
   * Calculate average processing time
   */
  private calculateAverageProcessingTime(): number {
    if (this.processingTimes.length === 0) return 0;
    const sum = this.processingTimes.reduce((a, b) => a + b, 0);
    return sum / this.processingTimes.length;
  }
}

/**
 * Default notification types for the MCP server
 */
export const NotificationTypes = {
  // Configuration changes
  CONFIG_CHANGED: 'config.changed',
  CONFIG_VALIDATION_FAILED: 'config.validation.failed',
  
  // Database events
  DATABASE_CONNECTED: 'database.connected',
  DATABASE_DISCONNECTED: 'database.disconnected',
  DATABASE_ERROR: 'database.error',
  DATABASE_QUERY_SLOW: 'database.query.slow',
  
  // Tool events
  TOOL_REGISTERED: 'tool.registered',
  TOOL_UNREGISTERED: 'tool.unregistered',
  TOOL_EXECUTION_STARTED: 'tool.execution.started',
  TOOL_EXECUTION_COMPLETED: 'tool.execution.completed',
  TOOL_EXECUTION_FAILED: 'tool.execution.failed',
  
  // Server events
  SERVER_STARTED: 'server.started',
  SERVER_STOPPING: 'server.stopping',
  SERVER_ERROR: 'server.error',
  
  // Health events
  HEALTH_CHECK_PASSED: 'health.check.passed',
  HEALTH_CHECK_FAILED: 'health.check.failed',
  PERFORMANCE_THRESHOLD_EXCEEDED: 'performance.threshold.exceeded',
} as const;

export type NotificationType = typeof NotificationTypes[keyof typeof NotificationTypes];