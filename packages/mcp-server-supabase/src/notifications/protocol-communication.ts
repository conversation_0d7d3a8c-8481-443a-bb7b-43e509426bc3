/**
 * Protocol Communication Layer for MCP Server
 * 
 * Handles MCP protocol-specific communication with notifications and event handling.
 * Provides real-time updates via WebSockets or Server-Sent Events.
 */

import { EventEmitter } from 'events';
import type { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { contextLogger } from '../utils/logger.js';
import { NotificationManager, type NotificationPayload, NotificationTypes } from './notification-manager.js';
import type { ConfigManager } from '../config/index.js';
import { RealTimeTransport, type RealTimeTransportOptions } from './realtime-transport.js';

export interface ProtocolMessage {
  id: string;
  type: 'request' | 'response' | 'notification' | 'error';
  method?: string;
  params?: Record<string, unknown>;
  result?: unknown;
  error?: {
    code: number;
    message: string;
    data?: unknown;
  };
  timestamp: Date;
}

export interface ClientConnection {
  id: string;
  clientInfo: {
    name: string;
    version: string;
  };
  capabilities: Record<string, unknown>;
  connectedAt: Date;
  lastActivity: Date;
  isActive: boolean;
}

export interface ProtocolCommunicationOptions {
  enableNotifications?: boolean;
  enableRealTimeUpdates?: boolean;
  messageTimeout?: number;
  maxPendingMessages?: number;
  heartbeatInterval?: number;
  realTimeTransportOptions?: RealTimeTransportOptions;
}

export class ProtocolCommunication extends EventEmitter {
  private readonly logger = contextLogger.child({ component: 'ProtocolCommunication' });
  private readonly notificationManager: NotificationManager;
  private readonly config?: ConfigManager;
  private readonly options: Required<ProtocolCommunicationOptions>;
  
  // Connection management
  private connections = new Map<string, ClientConnection>();
  private pendingMessages = new Map<string, ProtocolMessage>();
  private messageHandlers = new Map<string, (message: ProtocolMessage) => Promise<void>>();
  
  // Real-time communication
  private heartbeatInterval?: NodeJS.Timeout;
  private messageQueue: ProtocolMessage[] = [];
  private isProcessingQueue = false;
  private realTimeTransport?: RealTimeTransport;

  constructor(
    notificationManager: NotificationManager,
    options: ProtocolCommunicationOptions = {},
    config?: ConfigManager
  ) {
    super();
    
    this.notificationManager = notificationManager;
    this.config = config;
    this.options = {
      enableNotifications: options.enableNotifications ?? true,
      enableRealTimeUpdates: options.enableRealTimeUpdates ?? true,
      messageTimeout: options.messageTimeout ?? 30000, // 30 seconds
      maxPendingMessages: options.maxPendingMessages ?? 1000,
      heartbeatInterval: options.heartbeatInterval ?? 60000, // 1 minute
      realTimeTransportOptions: options.realTimeTransportOptions ?? {},
    };

    this.setupNotificationHandlers();
    this.startHeartbeat();
    
    this.logger.info('Protocol communication initialized', {
      enableNotifications: this.options.enableNotifications,
      enableRealTimeUpdates: this.options.enableRealTimeUpdates,
    });
  }

  /**
   * Initialize protocol communication with MCP server
   */
  initializeWithServer(server: Server<any, any, any>): void {
    this.logger.info('Integrating with MCP server');

    // Add notification support to server
    if (this.options.enableNotifications) {
      this.addNotificationSupportToServer(server);
    }

    // Add real-time update support
    if (this.options.enableRealTimeUpdates) {
      this.addRealTimeSupport(server);
    }

    this.emit('serverIntegrated', server);
  }

  /**
   * Register a client connection
   */
  registerClient(clientInfo: ClientConnection['clientInfo'], capabilities: Record<string, unknown>): string {
    const clientId = this.generateClientId();
    const connection: ClientConnection = {
      id: clientId,
      clientInfo,
      capabilities,
      connectedAt: new Date(),
      lastActivity: new Date(),
      isActive: true,
    };

    this.connections.set(clientId, connection);
    
    this.logger.info('Client registered', {
      clientId,
      clientName: clientInfo.name,
      clientVersion: clientInfo.version,
    });

    // Notify about new client connection
    this.notificationManager.notifySimple(
      NotificationTypes.SERVER_STARTED,
      {
        clientId,
        clientInfo,
        capabilities,
      },
      { source: 'protocol-communication' }
    );

    this.emit('clientRegistered', connection);
    return clientId;
  }

  /**
   * Unregister a client connection
   */
  unregisterClient(clientId: string): boolean {
    const connection = this.connections.get(clientId);
    if (!connection) {
      return false;
    }

    connection.isActive = false;
    this.connections.delete(clientId);

    this.logger.info('Client unregistered', { clientId });

    // Clean up pending messages for this client
    this.cleanupClientMessages(clientId);

    this.emit('clientUnregistered', connection);
    return true;
  }

  /**
   * Send a protocol message
   */
  async sendMessage(message: ProtocolMessage, targetClientId?: string): Promise<void> {
    try {
      this.validateMessage(message);

      // Update client activity if targeted
      if (targetClientId) {
        const client = this.connections.get(targetClientId);
        if (client) {
          client.lastActivity = new Date();
        }
      }

      // Add to pending messages if it's a request
      if (message.type === 'request') {
        this.addPendingMessage(message);
      }

      // Queue message for processing
      this.messageQueue.push(message);
      await this.processMessageQueue();

      this.logger.debug('Protocol message sent', {
        messageId: message.id,
        type: message.type,
        method: message.method,
        targetClientId,
      });

    } catch (error) {
      this.logger.error('Failed to send protocol message', error instanceof Error ? error : undefined, {
        messageId: message.id,
        type: message.type,
      });
      throw error;
    }
  }

  /**
   * Send a notification to all clients or a specific client
   */
  async sendNotification(
    method: string,
    params: Record<string, unknown>,
    targetClientId?: string
  ): Promise<void> {
    const message: ProtocolMessage = {
      id: this.generateMessageId(),
      type: 'notification',
      method,
      params,
      timestamp: new Date(),
    };

    await this.sendMessage(message, targetClientId);
  }

  /**
   * Broadcast notification to all active clients
   */
  async broadcastNotification(method: string, params: Record<string, unknown>): Promise<void> {
    const activeClients = Array.from(this.connections.values())
      .filter(client => client.isActive);

    const promises = activeClients.map(client =>
      this.sendNotification(method, params, client.id)
    );

    await Promise.allSettled(promises);
    
    this.logger.debug('Notification broadcasted', {
      method,
      clientCount: activeClients.length,
    });
  }

  /**
   * Handle incoming protocol message
   */
  async handleMessage(message: ProtocolMessage): Promise<void> {
    try {
      this.validateMessage(message);

      // Remove from pending if it's a response
      if (message.type === 'response') {
        this.removePendingMessage(message.id);
      }

      // Find and execute handler
      const handler = this.messageHandlers.get(message.method ?? message.type);
      if (handler) {
        await handler(message);
      } else {
        this.logger.warn('No handler found for message', {
          messageId: message.id,
          type: message.type,
          method: message.method,
        });
      }

      this.emit('messageHandled', message);

    } catch (error) {
      this.logger.error('Failed to handle protocol message', error instanceof Error ? error : undefined, {
        messageId: message.id,
        type: message.type,
      });

      // Send error response if it was a request
      if (message.type === 'request') {
        await this.sendErrorResponse(message.id, {
          code: -32603,
          message: 'Internal error',
          data: error instanceof Error ? error.message : String(error),
        });
      }
    }
  }

  /**
   * Register a message handler
   */
  registerMessageHandler(
    methodOrType: string,
    handler: (message: ProtocolMessage) => Promise<void>
  ): void {
    this.messageHandlers.set(methodOrType, handler);
    this.logger.debug('Message handler registered', { methodOrType });
  }

  /**
   * Get connection statistics
   */
  getConnectionStats() {
    const activeConnections = Array.from(this.connections.values())
      .filter(client => client.isActive);

    return {
      totalConnections: this.connections.size,
      activeConnections: activeConnections.length,
      pendingMessages: this.pendingMessages.size,
      queuedMessages: this.messageQueue.length,
      uptime: Date.now() - (activeConnections[0]?.connectedAt.getTime() ?? Date.now()),
    };
  }

  /**
   * Shutdown protocol communication
   */
  async shutdown(): Promise<void> {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = undefined;
    }

    // Stop real-time transport
    if (this.realTimeTransport) {
      try {
        await this.realTimeTransport.stop();
        this.realTimeTransport = undefined;
      } catch (error) {
        this.logger.error('Error stopping real-time transport during shutdown', error instanceof Error ? error : undefined);
      }
    }

    // Disconnect all clients
    for (const clientId of this.connections.keys()) {
      this.unregisterClient(clientId);
    }

    this.connections.clear();
    this.pendingMessages.clear();
    this.messageHandlers.clear();
    this.messageQueue.length = 0;
    this.removeAllListeners();

    this.logger.info('Protocol communication shutdown complete');
  }

  /**
   * Setup notification handlers for protocol communication
   */
  private setupNotificationHandlers(): void {
    // Handle configuration changes
    this.notificationManager.registerHandler({
      id: 'protocol-config-change',
      type: NotificationTypes.CONFIG_CHANGED,
      handler: async (payload: NotificationPayload) => {
        await this.broadcastNotification('config/changed', {
          key: payload.data.key,
          newValue: payload.data.newValue,
          timestamp: payload.timestamp,
        });
      },
    });

    // Handle database events
    this.notificationManager.registerHandler({
      id: 'protocol-database-events',
      type: [
        NotificationTypes.DATABASE_CONNECTED,
        NotificationTypes.DATABASE_DISCONNECTED,
        NotificationTypes.DATABASE_ERROR,
      ],
      handler: async (payload: NotificationPayload) => {
        await this.broadcastNotification('database/status', {
          status: payload.type.split('.')[1], // connected, disconnected, error
          data: payload.data,
          timestamp: payload.timestamp,
        });
      },
    });

    // Handle tool events
    this.notificationManager.registerHandler({
      id: 'protocol-tool-events',
      type: [
        NotificationTypes.TOOL_EXECUTION_STARTED,
        NotificationTypes.TOOL_EXECUTION_COMPLETED,
        NotificationTypes.TOOL_EXECUTION_FAILED,
      ],
      handler: async (payload: NotificationPayload) => {
        await this.broadcastNotification('tool/execution', {
          toolName: payload.data.toolName,
          status: payload.type.split('.')[2], // started, completed, failed
          data: payload.data,
          timestamp: payload.timestamp,
        });
      },
    });
  }

  /**
   * Add notification support to MCP server
   */
  private addNotificationSupportToServer(server: Server<any, any, any>): void {
    // Note: This would integrate with the MCP server's notification system
    // For now, we'll emit events that can be handled by the server
    this.logger.debug('Adding notification support to MCP server');
    
    // The actual implementation would depend on the MCP SDK's notification support
    // This is a placeholder for the integration
  }

  /**
   * Add real-time support
   */
  private async addRealTimeSupport(server: Server<any, any, any>): Promise<void> {
    this.logger.debug('Adding real-time support to MCP server');

    try {
      // Initialize real-time transport
      this.realTimeTransport = new RealTimeTransport(this.options.realTimeTransportOptions, this.config);

      // Setup event handlers
      this.setupRealTimeTransportHandlers();

      // Start the transport
      await this.realTimeTransport.start();

      this.logger.info('Real-time transport initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize real-time transport', error instanceof Error ? error : undefined);
      // Continue without real-time transport
    }
  }

  /**
   * Start heartbeat mechanism
   */
  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      this.performHeartbeat();
    }, this.options.heartbeatInterval);
  }

  /**
   * Perform heartbeat check
   */
  private performHeartbeat(): void {
    const now = new Date();
    const staleThreshold = this.options.heartbeatInterval * 2; // 2x heartbeat interval

    for (const [clientId, connection] of this.connections.entries()) {
      const timeSinceLastActivity = now.getTime() - connection.lastActivity.getTime();
      
      if (timeSinceLastActivity > staleThreshold) {
        this.logger.warn('Client connection appears stale', {
          clientId,
          timeSinceLastActivity,
          threshold: staleThreshold,
        });
        
        connection.isActive = false;
        this.emit('clientStale', connection);
      }
    }

    // Clean up old pending messages
    this.cleanupPendingMessages();
  }

  /**
   * Process message queue
   */
  private async processMessageQueue(): Promise<void> {
    if (this.isProcessingQueue || this.messageQueue.length === 0) {
      return;
    }

    this.isProcessingQueue = true;

    try {
      while (this.messageQueue.length > 0) {
        const message = this.messageQueue.shift();
        if (message) {
          await this.processMessage(message);
        }
      }
    } finally {
      this.isProcessingQueue = false;
    }
  }

  /**
   * Process a single message
   */
  private async processMessage(message: ProtocolMessage): Promise<void> {
    try {
      // Send via real-time transport if available
      if (this.realTimeTransport?.isActive()) {
        await this.realTimeTransport.sendMessage(message);
      }

      // Emit event for other handlers
      this.emit('messageSent', message);
    } catch (error) {
      this.logger.error('Failed to process message', error instanceof Error ? error : undefined, {
        messageId: message.id,
        type: message.type,
      });
      throw error;
    }
  }

  /**
   * Send error response
   */
  private async sendErrorResponse(
    requestId: string,
    error: { code: number; message: string; data?: unknown }
  ): Promise<void> {
    const response: ProtocolMessage = {
      id: requestId,
      type: 'response',
      error,
      timestamp: new Date(),
    };

    await this.sendMessage(response);
  }

  /**
   * Validate protocol message
   */
  private validateMessage(message: ProtocolMessage): void {
    if (!message.id || typeof message.id !== 'string') {
      throw new Error('Message must have a valid id');
    }
    
    if (!message.type || !['request', 'response', 'notification', 'error'].includes(message.type)) {
      throw new Error('Message must have a valid type');
    }
    
    if (!message.timestamp || !(message.timestamp instanceof Date)) {
      throw new Error('Message must have a valid timestamp');
    }
  }

  /**
   * Add pending message
   */
  private addPendingMessage(message: ProtocolMessage): void {
    if (this.pendingMessages.size >= this.options.maxPendingMessages) {
      // Remove oldest pending message
      const oldestId = this.pendingMessages.keys().next().value;
      if (oldestId) {
        this.pendingMessages.delete(oldestId);
        this.logger.warn('Pending message limit reached, removing oldest', { oldestId });
      }
    }

    this.pendingMessages.set(message.id, message);

    // Set timeout for cleanup
    setTimeout(() => {
      if (this.pendingMessages.has(message.id)) {
        this.pendingMessages.delete(message.id);
        this.logger.warn('Pending message timed out', { messageId: message.id });
      }
    }, this.options.messageTimeout);
  }

  /**
   * Remove pending message
   */
  private removePendingMessage(messageId: string): boolean {
    return this.pendingMessages.delete(messageId);
  }

  /**
   * Clean up pending messages
   */
  private cleanupPendingMessages(): void {
    const now = Date.now();
    const timeout = this.options.messageTimeout;

    for (const [messageId, message] of this.pendingMessages.entries()) {
      if (now - message.timestamp.getTime() > timeout) {
        this.pendingMessages.delete(messageId);
        this.logger.debug('Cleaned up expired pending message', { messageId });
      }
    }
  }

  /**
   * Clean up messages for a specific client
   */
  private cleanupClientMessages(clientId: string): void {
    // This would remove any client-specific pending messages
    // Implementation depends on how we track client-message relationships
    this.logger.debug('Cleaned up messages for client', { clientId });
  }

  /**
   * Setup real-time transport event handlers
   */
  private setupRealTimeTransportHandlers(): void {
    if (!this.realTimeTransport) {
      return;
    }

    this.realTimeTransport.on('clientConnected', (client, transportType) => {
      this.logger.debug('Real-time client connected', {
        clientId: client.id,
        transportType,
      });
      this.emit('realTimeClientConnected', client, transportType);
    });

    this.realTimeTransport.on('clientDisconnected', (client, transportType) => {
      this.logger.debug('Real-time client disconnected', {
        clientId: client.id,
        transportType,
      });
      this.emit('realTimeClientDisconnected', client, transportType);
    });

    this.realTimeTransport.on('messageReceived', (client, message, transportType) => {
      this.logger.debug('Real-time message received', {
        clientId: client.id,
        messageId: message.id,
        transportType,
      });
      // Handle the received message
      this.handleMessage(message).catch(error => {
        this.logger.error('Failed to handle real-time message', error instanceof Error ? error : undefined);
      });
    });

    this.realTimeTransport.on('transportError', (transportType, error) => {
      this.logger.error('Real-time transport error', error, { transportType });
      this.emit('realTimeTransportError', transportType, error);
    });
  }

  /**
   * Generate unique client ID
   */
  private generateClientId(): string {
    return `client_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * Generate unique message ID
   */
  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }
}