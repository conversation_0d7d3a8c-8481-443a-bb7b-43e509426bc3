import { describe, expect, test, beforeEach, afterEach, vi } from 'vitest';
import { WebSocket } from 'ws';
import { WebSocketTransport } from './websocket-transport.js';
import { ConfigManager } from '../config/config-manager.js';
import type { ProtocolMessage, NotificationPayload } from './index.js';

// Mock WebSocket
vi.mock('ws', () => ({
  WebSocketServer: vi.fn(),
  WebSocket: {
    OPEN: 1,
    CLOSED: 3,
  },
}));

describe('WebSocketTransport', () => {
  let transport: WebSocketTransport;
  let mockConfig: ConfigManager;

  beforeEach(() => {
    mockConfig = {
      get: vi.fn((key: string, defaultValue?: any) => {
        const config: Record<string, any> = {
          WEBSOCKET_PORT: 8080,
          WEBSOCKET_HOST: 'localhost',
          WEBSOCKET_PATH: '/ws',
          WEBSOCKET_MAX_CONNECTIONS: 100,
          WEBSOCKET_HEARTBEAT_INTERVAL: 30000,
          WEBSOCKET_MESSAGE_TIMEOUT: 10000,
          WEBSOCKET_ENABLE_COMPRESSION: true,
          WEBSOCKET_ENABLE_AUTH: false,
        };
        return config[key] ?? defaultValue;
      }),
    } as any;

    transport = new WebSocketTransport({}, mockConfig);
  });

  afterEach(async () => {
    if (transport.isActive()) {
      await transport.stop();
    }
  });

  describe('Initialization', () => {
    test('should initialize with default options', () => {
      const defaultTransport = new WebSocketTransport();
      expect(defaultTransport).toBeDefined();
    });

    test('should initialize with custom options', () => {
      const customTransport = new WebSocketTransport({
        port: 9090,
        host: '0.0.0.0',
        path: '/custom-ws',
        maxConnections: 50,
      });
      expect(customTransport).toBeDefined();
    });

    test('should use config values when provided', () => {
      expect(mockConfig.get).toHaveBeenCalledWith('WEBSOCKET_PORT', 8080);
      expect(mockConfig.get).toHaveBeenCalledWith('WEBSOCKET_HOST', 'localhost');
    });
  });

  describe('Server Lifecycle', () => {
    test('should start server successfully', async () => {
      const mockServer = {
        on: vi.fn(),
        close: vi.fn((callback) => callback()),
      };
      
      const { WebSocketServer } = await import('ws');
      (WebSocketServer as any).mockImplementation(() => mockServer);

      await transport.start();
      expect(transport.isActive()).toBe(true);
      expect(mockServer.on).toHaveBeenCalledWith('connection', expect.any(Function));
      expect(mockServer.on).toHaveBeenCalledWith('error', expect.any(Function));
      expect(mockServer.on).toHaveBeenCalledWith('listening', expect.any(Function));
    });

    test('should not start if already running', async () => {
      const mockServer = {
        on: vi.fn(),
        close: vi.fn((callback) => callback()),
      };
      
      const { WebSocketServer } = await import('ws');
      (WebSocketServer as any).mockImplementation(() => mockServer);

      await transport.start();
      const firstStart = transport.isActive();
      
      // Try to start again
      await transport.start();
      expect(firstStart).toBe(true);
      expect(transport.isActive()).toBe(true);
    });

    test('should stop server successfully', async () => {
      const mockServer = {
        on: vi.fn(),
        close: vi.fn((callback) => callback()),
      };
      
      const { WebSocketServer } = await import('ws');
      (WebSocketServer as any).mockImplementation(() => mockServer);

      await transport.start();
      expect(transport.isActive()).toBe(true);
      
      await transport.stop();
      expect(transport.isActive()).toBe(false);
      expect(mockServer.close).toHaveBeenCalled();
    });

    test('should handle start errors gracefully', async () => {
      const { WebSocketServer } = await import('ws');
      (WebSocketServer as any).mockImplementation(() => {
        throw new Error('Port already in use');
      });

      await expect(transport.start()).rejects.toThrow('Port already in use');
      expect(transport.isActive()).toBe(false);
    });
  });

  describe('Message Handling', () => {
    let mockClient: any;
    let mockSocket: any;

    beforeEach(() => {
      mockSocket = {
        readyState: WebSocket.OPEN,
        send: vi.fn(),
        close: vi.fn(),
        on: vi.fn(),
        ping: vi.fn(),
        terminate: vi.fn(),
      };

      mockClient = {
        id: 'test-client-1',
        socket: mockSocket,
        isActive: true,
        connectedAt: new Date(),
        lastActivity: new Date(),
        subscriptions: new Set(),
        metadata: {
          userAgent: 'test-agent',
          remoteAddress: '127.0.0.1',
        },
      };

      // Mock the clients map
      (transport as any).clients.set('test-client-1', mockClient);
    });

    test('should send message to specific client', async () => {
      const message: ProtocolMessage = {
        id: 'msg-1',
        type: 'notification',
        method: 'test',
        params: { data: 'test' },
        timestamp: new Date(),
      };

      await transport.sendMessage(message, 'test-client-1');
      
      expect(mockSocket.send).toHaveBeenCalledWith(JSON.stringify(message));
    });

    test('should broadcast message to all clients', async () => {
      const message: ProtocolMessage = {
        id: 'msg-2',
        type: 'notification',
        method: 'broadcast',
        params: { data: 'broadcast' },
        timestamp: new Date(),
      };

      await transport.sendMessage(message);
      
      expect(mockSocket.send).toHaveBeenCalledWith(JSON.stringify(message));
    });

    test('should send notification', async () => {
      const notification: NotificationPayload = {
        id: 'notif-1',
        type: 'test.notification',
        timestamp: new Date(),
        source: 'test',
        data: { message: 'test notification' },
        priority: 'medium',
      };

      await transport.sendNotification(notification, 'test-client-1');
      
      expect(mockSocket.send).toHaveBeenCalled();
      const sentData = JSON.parse(mockSocket.send.mock.calls[0][0]);
      expect(sentData.type).toBe('notification');
      expect(sentData.params.type).toBe('test.notification');
    });

    test('should handle send errors gracefully', async () => {
      mockSocket.send.mockImplementation(() => {
        throw new Error('Connection closed');
      });

      const message: ProtocolMessage = {
        id: 'msg-error',
        type: 'notification',
        method: 'test',
        params: {},
        timestamp: new Date(),
      };

      await expect(transport.sendMessage(message, 'test-client-1')).rejects.toThrow('Connection closed');
    });

    test('should skip sending to disconnected clients', async () => {
      mockSocket.readyState = WebSocket.CLOSED;

      const message: ProtocolMessage = {
        id: 'msg-closed',
        type: 'notification',
        method: 'test',
        params: {},
        timestamp: new Date(),
      };

      await transport.sendMessage(message, 'test-client-1');
      
      expect(mockSocket.send).not.toHaveBeenCalled();
    });
  });

  describe('Client Management', () => {
    test('should get all clients', () => {
      const mockClient = {
        id: 'client-1',
        socket: {} as any,
        isActive: true,
        connectedAt: new Date(),
        lastActivity: new Date(),
        subscriptions: new Set(),
        metadata: {},
      };

      (transport as any).clients.set('client-1', mockClient);
      
      const clients = transport.getClients();
      expect(clients).toHaveLength(1);
      expect(clients[0].id).toBe('client-1');
    });

    test('should get client by ID', () => {
      const mockClient = {
        id: 'client-2',
        socket: {} as any,
        isActive: true,
        connectedAt: new Date(),
        lastActivity: new Date(),
        subscriptions: new Set(),
        metadata: {},
      };

      (transport as any).clients.set('client-2', mockClient);
      
      const client = transport.getClient('client-2');
      expect(client).toBeDefined();
      expect(client?.id).toBe('client-2');
    });

    test('should return undefined for non-existent client', () => {
      const client = transport.getClient('non-existent');
      expect(client).toBeUndefined();
    });
  });

  describe('Statistics', () => {
    test('should return correct stats', () => {
      const stats = transport.getStats();
      
      expect(stats).toEqual({
        isRunning: false,
        connectedClients: 0,
        totalConnections: 0,
        port: 8080,
        host: 'localhost',
      });
    });

    test('should update stats when clients connect', () => {
      const mockClient = {
        id: 'stats-client',
        socket: {} as any,
        isActive: true,
        connectedAt: new Date(),
        lastActivity: new Date(),
        subscriptions: new Set(),
        metadata: {},
      };

      (transport as any).clients.set('stats-client', mockClient);
      
      const stats = transport.getStats();
      expect(stats.connectedClients).toBe(1);
    });
  });

  describe('Event Emission', () => {
    test('should emit started event when server starts', async () => {
      const mockServer = {
        on: vi.fn(),
        close: vi.fn((callback) => callback()),
      };
      
      const { WebSocketServer } = await import('ws');
      (WebSocketServer as any).mockImplementation(() => mockServer);

      const startedHandler = vi.fn();
      transport.on('started', startedHandler);

      await transport.start();
      
      expect(startedHandler).toHaveBeenCalled();
    });

    test('should emit stopped event when server stops', async () => {
      const mockServer = {
        on: vi.fn(),
        close: vi.fn((callback) => callback()),
      };
      
      const { WebSocketServer } = await import('ws');
      (WebSocketServer as any).mockImplementation(() => mockServer);

      const stoppedHandler = vi.fn();
      transport.on('stopped', stoppedHandler);

      await transport.start();
      await transport.stop();
      
      expect(stoppedHandler).toHaveBeenCalled();
    });
  });
});
