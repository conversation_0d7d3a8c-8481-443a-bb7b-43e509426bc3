/**
 * Server-Sent Events (SSE) Transport Layer for Real-time Protocol Communication
 * 
 * Provides SSE-based real-time communication for MCP protocol messages,
 * notifications, and events. Useful for clients that cannot use WebSockets.
 */

import { EventEmitter } from 'events';
import { IncomingMessage, ServerResponse } from 'http';
import { contextLogger } from '../utils/logger.js';
import type { ConfigManager } from '../config/config-manager.js';
import type { ProtocolMessage, ClientConnection } from './protocol-communication.js';
import type { NotificationPayload } from './notification-manager.js';

export interface SSETransportOptions {
  path?: string;
  maxConnections?: number;
  heartbeatInterval?: number;
  connectionTimeout?: number;
  enableCors?: boolean;
  corsOrigins?: string[];
  enableAuth?: boolean;
}

export interface SSEClient extends ClientConnection {
  response: ServerResponse;
  request: IncomingMessage;
  subscriptions: Set<string>;
  lastHeartbeat?: Date;
}

/**
 * Server-Sent Events transport for real-time MCP protocol communication
 */
export class SSETransport extends EventEmitter {
  private readonly logger = contextLogger.child({ component: 'SSETransport' });
  private readonly config?: ConfigManager;
  private readonly options: Required<SSETransportOptions>;
  
  private clients = new Map<string, SSEClient>();
  private heartbeatInterval?: NodeJS.Timeout;
  private isRunning = false;

  constructor(options: SSETransportOptions = {}, config?: ConfigManager) {
    super();
    
    this.config = config;
    this.options = {
      path: options.path ?? config?.get('SSE_PATH', '/events') ?? '/events',
      maxConnections: options.maxConnections ?? config?.get('SSE_MAX_CONNECTIONS', 100) ?? 100,
      heartbeatInterval: options.heartbeatInterval ?? config?.get('SSE_HEARTBEAT_INTERVAL', 30000) ?? 30000,
      connectionTimeout: options.connectionTimeout ?? config?.get('SSE_CONNECTION_TIMEOUT', 300000) ?? 300000, // 5 minutes
      enableCors: options.enableCors ?? config?.get('SSE_ENABLE_CORS', true) ?? true,
      corsOrigins: options.corsOrigins ?? config?.get('SSE_CORS_ORIGINS', ['*']) ?? ['*'],
      enableAuth: options.enableAuth ?? config?.get('SSE_ENABLE_AUTH', false) ?? false,
    };

    this.logger.info('SSE transport initialized', {
      path: this.options.path,
      maxConnections: this.options.maxConnections,
      enableCors: this.options.enableCors,
    });
  }

  /**
   * Start the SSE transport
   */
  async start(): Promise<void> {
    if (this.isRunning) {
      this.logger.warn('SSE transport already running');
      return;
    }

    this.startHeartbeat();
    this.isRunning = true;
    
    this.logger.info('SSE transport started', {
      path: this.options.path,
    });

    this.emit('started');
  }

  /**
   * Stop the SSE transport
   */
  async stop(): Promise<void> {
    if (!this.isRunning) {
      return;
    }

    try {
      // Stop heartbeat
      if (this.heartbeatInterval) {
        clearInterval(this.heartbeatInterval);
        this.heartbeatInterval = undefined;
      }

      // Close all client connections
      for (const client of this.clients.values()) {
        this.closeClient(client, 'Server shutting down');
      }
      this.clients.clear();

      this.isRunning = false;
      
      this.logger.info('SSE transport stopped');
      this.emit('stopped');
    } catch (error) {
      this.logger.error('Error stopping SSE transport', error instanceof Error ? error : undefined);
      throw error;
    }
  }

  /**
   * Handle incoming SSE connection request
   */
  handleConnection(request: IncomingMessage, response: ServerResponse): void {
    if (!this.isRunning) {
      response.writeHead(503, { 'Content-Type': 'text/plain' });
      response.end('SSE transport not running');
      return;
    }

    // Check connection limit
    if (this.clients.size >= this.options.maxConnections) {
      this.logger.warn('SSE connection limit reached, rejecting new connection');
      response.writeHead(503, { 'Content-Type': 'text/plain' });
      response.end('Server overloaded');
      return;
    }

    // Setup SSE headers
    const headers: Record<string, string> = {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'X-Accel-Buffering': 'no', // Disable nginx buffering
    };

    // Add CORS headers if enabled
    if (this.options.enableCors) {
      const origin = request.headers.origin;
      if (this.options.corsOrigins.includes('*') || 
          (origin && this.options.corsOrigins.includes(origin))) {
        headers['Access-Control-Allow-Origin'] = origin || '*';
        headers['Access-Control-Allow-Credentials'] = 'true';
        headers['Access-Control-Allow-Headers'] = 'Cache-Control';
      }
    }

    response.writeHead(200, headers);

    const clientId = this.generateClientId();
    const client: SSEClient = {
      id: clientId,
      response,
      request,
      isActive: true,
      connectedAt: new Date(),
      lastActivity: new Date(),
      subscriptions: new Set(),
      metadata: {
        userAgent: request.headers['user-agent'],
        remoteAddress: request.socket.remoteAddress,
      },
    };

    this.clients.set(clientId, client);
    
    this.logger.info('New SSE client connected', {
      clientId,
      remoteAddress: client.metadata.remoteAddress,
      totalClients: this.clients.size,
    });

    this.setupClientEventHandlers(client);
    
    // Send initial connection event
    this.sendEvent(client, 'connected', {
      clientId,
      timestamp: new Date().toISOString(),
    });

    this.emit('clientConnected', client);
  }

  /**
   * Send a protocol message to a specific client or all clients
   */
  async sendMessage(message: ProtocolMessage, targetClientId?: string): Promise<void> {
    try {
      if (targetClientId) {
        const client = this.clients.get(targetClientId);
        if (client && client.isActive) {
          this.sendEvent(client, 'message', message);
          client.lastActivity = new Date();
          
          this.logger.debug('Message sent to SSE client', {
            clientId: targetClientId,
            messageId: message.id,
            type: message.type,
          });
        } else {
          this.logger.warn('Target SSE client not found or not connected', { targetClientId });
        }
      } else {
        // Broadcast to all connected clients
        let sentCount = 0;
        for (const [clientId, client] of this.clients.entries()) {
          if (client.isActive) {
            this.sendEvent(client, 'message', message);
            client.lastActivity = new Date();
            sentCount++;
          }
        }
        
        this.logger.debug('Message broadcast to SSE clients', {
          messageId: message.id,
          type: message.type,
          clientCount: sentCount,
        });
      }
    } catch (error) {
      this.logger.error('Failed to send SSE message', error instanceof Error ? error : undefined, {
        messageId: message.id,
        targetClientId,
      });
      throw error;
    }
  }

  /**
   * Send a notification to subscribed clients
   */
  async sendNotification(notification: NotificationPayload, targetClientId?: string): Promise<void> {
    const message: ProtocolMessage = {
      id: this.generateMessageId(),
      type: 'notification',
      method: 'notification',
      params: {
        type: notification.type,
        data: notification.data,
        timestamp: notification.timestamp,
        source: notification.source,
        priority: notification.priority,
      },
      timestamp: new Date(),
    };

    await this.sendMessage(message, targetClientId);
  }

  /**
   * Get connected client information
   */
  getClients(): SSEClient[] {
    return Array.from(this.clients.values());
  }

  /**
   * Get client by ID
   */
  getClient(clientId: string): SSEClient | undefined {
    return this.clients.get(clientId);
  }

  /**
   * Check if transport is running
   */
  isActive(): boolean {
    return this.isRunning;
  }

  /**
   * Get connection statistics
   */
  getStats(): {
    isRunning: boolean;
    connectedClients: number;
    totalConnections: number;
    path: string;
  } {
    return {
      isRunning: this.isRunning,
      connectedClients: this.clients.size,
      totalConnections: this.clients.size, // Could track historical total
      path: this.options.path,
    };
  }

  /**
   * Send an SSE event to a client
   */
  private sendEvent(client: SSEClient, event: string, data: any): void {
    if (!client.isActive || client.response.destroyed) {
      return;
    }

    try {
      const eventData = typeof data === 'string' ? data : JSON.stringify(data);
      const eventId = this.generateEventId();
      
      client.response.write(`id: ${eventId}\n`);
      client.response.write(`event: ${event}\n`);
      client.response.write(`data: ${eventData}\n\n`);
      
      client.lastActivity = new Date();
    } catch (error) {
      this.logger.error('Failed to send SSE event', error instanceof Error ? error : undefined, {
        clientId: client.id,
        event,
      });
      this.closeClient(client, 'Write error');
    }
  }

  /**
   * Setup event handlers for an SSE client
   */
  private setupClientEventHandlers(client: SSEClient): void {
    const { request, response, id: clientId } = client;

    // Handle client disconnect
    request.on('close', () => {
      this.handleClientDisconnect(client, 'Client closed connection');
    });

    request.on('error', (error) => {
      this.logger.error('SSE client request error', error, { clientId });
      this.handleClientDisconnect(client, 'Request error');
    });

    response.on('error', (error) => {
      this.logger.error('SSE client response error', error, { clientId });
      this.handleClientDisconnect(client, 'Response error');
    });

    response.on('close', () => {
      this.handleClientDisconnect(client, 'Response closed');
    });

    // Set connection timeout
    setTimeout(() => {
      if (client.isActive) {
        this.logger.info('SSE client connection timeout', { clientId });
        this.closeClient(client, 'Connection timeout');
      }
    }, this.options.connectionTimeout);
  }

  /**
   * Handle client disconnect
   */
  private handleClientDisconnect(client: SSEClient, reason: string): void {
    if (!client.isActive) {
      return; // Already handled
    }

    this.clients.delete(client.id);
    client.isActive = false;
    
    this.logger.info('SSE client disconnected', {
      clientId: client.id,
      reason,
      totalClients: this.clients.size,
    });

    this.emit('clientDisconnected', client, reason);
  }

  /**
   * Close a client connection
   */
  private closeClient(client: SSEClient, reason: string): void {
    if (!client.isActive) {
      return;
    }

    try {
      // Send close event
      this.sendEvent(client, 'close', { reason });
      
      // End the response
      client.response.end();
    } catch (error) {
      // Ignore errors when closing
    }

    this.handleClientDisconnect(client, reason);
  }

  /**
   * Start heartbeat mechanism
   */
  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      this.performHeartbeat();
    }, this.options.heartbeatInterval);
  }

  /**
   * Perform heartbeat check
   */
  private performHeartbeat(): void {
    const now = new Date();
    const staleThreshold = this.options.heartbeatInterval * 2;

    for (const [clientId, client] of this.clients.entries()) {
      if (client.isActive) {
        const timeSinceLastActivity = now.getTime() - client.lastActivity.getTime();
        
        if (timeSinceLastActivity > staleThreshold) {
          this.logger.warn('SSE client connection stale, closing', {
            clientId,
            timeSinceLastActivity,
          });
          this.closeClient(client, 'Stale connection');
        } else {
          // Send heartbeat
          this.sendEvent(client, 'heartbeat', {
            timestamp: now.toISOString(),
          });
          client.lastHeartbeat = now;
        }
      }
    }
  }

  /**
   * Generate unique client ID
   */
  private generateClientId(): string {
    return `sse_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Generate unique message ID
   */
  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Generate unique event ID
   */
  private generateEventId(): string {
    return `evt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
