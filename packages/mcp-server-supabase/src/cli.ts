#!/usr/bin/env node

import { parseArgs } from 'node:util';
import { contextLogger } from './utils/logger.js';
import { MigrationWrapper } from './migration/migration-wrapper.js';
import { MigrationTemplateType } from './migration/migration-types.js';
import { getLocalConfig } from './config/local-config.js';
import { validateMigration, formatValidationResults } from './migration/migration-validator.js';
import { promises as fs } from 'fs';
import path from 'path';
import packageJson from '../package.json' with { type: 'json' };

const { version } = packageJson;
const logger = contextLogger.child({ component: 'CLI' });

/**
 * MCP Supabase CLI for managing database migrations and server operations
 */
export class SupabaseMcpCLI {
  private migrationWrapper?: MigrationWrapper;

  /**
   * Initialize migration wrapper if needed
   */
  private async initMigrationWrapper(): Promise<MigrationWrapper> {
    if (!this.migrationWrapper) {
      const config = getLocalConfig();
      this.migrationWrapper = new MigrationWrapper(config);
    }
    return this.migrationWrapper;
  }

  /**
   * Create a new migration
   */
  async createMigration(name: string, options: {
    template?: string;
    tableName?: string;
    columnName?: string;
    columnType?: string;
    indexName?: string;
    description?: string;
  } = {}): Promise<void> {
    logger.info('Creating migration', { name, options });

    try {
      const wrapper = await this.initMigrationWrapper();
      
      // Map template string to enum
      let template = MigrationTemplateType.CUSTOM;
      if (options.template) {
        switch (options.template.toLowerCase()) {
          case 'create_table':
          case 'table':
            template = MigrationTemplateType.CREATE_TABLE;
            break;
          case 'alter_table':
          case 'alter':
            template = MigrationTemplateType.ALTER_TABLE;
            break;
          case 'create_index':
          case 'index':
            template = MigrationTemplateType.CREATE_INDEX;
            break;
          case 'data_migration':
          case 'data':
            template = MigrationTemplateType.DATA_MIGRATION;
            break;
          default:
            template = MigrationTemplateType.CUSTOM;
        }
      }

      const result = await wrapper.createMigration({
        name,
        template,
        templateData: {
          tableName: options.tableName,
          columnName: options.columnName,
          columnType: options.columnType,
          indexName: options.indexName,
          description: options.description || name,
        },
      });

      console.log(`✅ Migration created successfully:`);
      console.log(`   File: ${result.filename}`);
      console.log(`   Path: ${result.path}`);
      console.log(`   Template: ${template}`);
    } catch (error) {
      console.error('❌ Failed to create migration:', error instanceof Error ? error.message : String(error));
      process.exit(1);
    }
  }

  /**
   * Run pending migrations
   */
  async runMigrations(options: {
    count?: number;
    to?: string;
    dryRun?: boolean;
    lockTable?: boolean;
  } = {}): Promise<void> {
    logger.info('Running migrations', options);

    try {
      const wrapper = await this.initMigrationWrapper();
      const result = await wrapper.up(options);

      if (result.success) {
        console.log(`✅ Migrations completed successfully:`);
        console.log(`   Migrations run: ${result.migrationsRun.length}`);
        console.log(`   Duration: ${result.durationMs}ms`);
        
        if (result.migrationsRun.length > 0) {
          console.log(`   Applied migrations:`);
          result.migrationsRun.forEach(migration => {
            console.log(`     - ${migration.name}`);
          });
        }
      } else {
        console.error(`❌ Migration failed: ${result.error}`);
        process.exit(1);
      }
    } catch (error) {
      console.error('❌ Failed to run migrations:', error instanceof Error ? error.message : String(error));
      process.exit(1);
    }
  }

  /**
   * Rollback migrations
   */
  async rollbackMigrations(options: {
    count?: number;
    to?: string;
    dryRun?: boolean;
    lockTable?: boolean;
  } = {}): Promise<void> {
    logger.info('Rolling back migrations', options);

    try {
      const wrapper = await this.initMigrationWrapper();
      const result = await wrapper.down(options);

      if (result.success) {
        console.log(`✅ Rollback completed successfully:`);
        console.log(`   Migrations rolled back: ${result.migrationsRun.length}`);
        console.log(`   Duration: ${result.durationMs}ms`);
        
        if (result.migrationsRun.length > 0) {
          console.log(`   Rolled back migrations:`);
          result.migrationsRun.forEach(migration => {
            console.log(`     - ${migration.name}`);
          });
        }
      } else {
        console.error(`❌ Rollback failed: ${result.error}`);
        process.exit(1);
      }
    } catch (error) {
      console.error('❌ Failed to rollback migrations:', error instanceof Error ? error.message : String(error));
      process.exit(1);
    }
  }

  /**
   * Show migration status
   */
  async showMigrationStatus(): Promise<void> {
    logger.info('Getting migration status');

    try {
      const wrapper = await this.initMigrationWrapper();
      const status = await wrapper.getStatus();

      console.log(`\n📊 Migration Status:`);
      console.log(`   Total migrations: ${status.length}`);
      
      const applied = status.filter(m => m.status === 'applied');
      const pending = status.filter(m => m.status === 'pending');
      
      console.log(`   Applied: ${applied.length}`);
      console.log(`   Pending: ${pending.length}`);

      if (status.length > 0) {
        console.log(`\n📋 Migration List:`);
        status.forEach(migration => {
          const statusIcon = migration.status === 'applied' ? '✅' : '⏳';
          console.log(`   ${statusIcon} ${migration.name} (${migration.status})`);
        });
      } else {
        console.log(`\n   No migrations found.`);
      }
    } catch (error) {
      console.error('❌ Failed to get migration status:', error instanceof Error ? error.message : String(error));
      process.exit(1);
    }
  }

  /**
   * Test database connection
   */
  async testConnection(): Promise<void> {
    logger.info('Testing database connection');

    try {
      const wrapper = await this.initMigrationWrapper();
      const isConnected = await wrapper.testConnection();

      if (isConnected) {
        console.log('✅ Database connection successful');
      } else {
        console.error('❌ Database connection failed');
        process.exit(1);
      }
    } catch (error) {
      console.error('❌ Failed to test connection:', error instanceof Error ? error.message : String(error));
      process.exit(1);
    }
  }

  /**
   * Show help information
   */
  showHelp(): void {
    console.log(`
Supabase MCP Server CLI v${version}

Usage:
  mcp-supabase <command> [options]

Migration Commands:
  migrate:create <name> [options]    Create a new migration
  migrate:up [options]               Run pending migrations
  migrate:down [options]             Rollback migrations
  migrate:status                     Show migration status
  migrate:test                       Test database connection

Migration Options:
  --template <type>                  Migration template (table, alter, index, data, custom)
  --table-name <name>                Table name for templates
  --column-name <name>               Column name for templates
  --column-type <type>               Column type for templates
  --index-name <name>                Index name for templates
  --description <text>               Migration description
  --count <number>                   Number of migrations to run/rollback
  --to <migration>                   Target migration
  --dry-run                          Show what would be done without executing
  --no-lock                          Disable table locking

General Commands:
  version                            Show version information
  help                               Show this help message

Examples:
  mcp-supabase migrate:create add_users_table --template table --table-name users
  mcp-supabase migrate:create add_email_column --template alter --table-name users --column-name email --column-type VARCHAR(255)
  mcp-supabase migrate:up
  mcp-supabase migrate:down --count 1
  mcp-supabase migrate:status
  mcp-supabase migrate:test

Environment Variables:
  DATABASE_URL                       PostgreSQL connection string
  SUPABASE_URL                       Supabase project URL
  SUPABASE_ANON_KEY                  Supabase anonymous key
  SUPABASE_SERVICE_ROLE_KEY          Supabase service role key
`);
  }
}

/**
 * Parse CLI arguments and execute commands
 */
async function runCLI(): Promise<void> {
  const cli = new SupabaseMcpCLI();
  const args = process.argv.slice(2);

  if (args.length === 0) {
    cli.showHelp();
    return;
  }

  const command = args[0];

  try {
    switch (command) {
      case 'migrate:create': {
        if (args.length < 2) {
          console.error('Usage: mcp-supabase migrate:create <name> [options]');
          process.exit(1);
        }

        const { values } = parseArgs({
          args: args.slice(2),
          options: {
            template: { type: 'string' },
            'table-name': { type: 'string' },
            'column-name': { type: 'string' },
            'column-type': { type: 'string' },
            'index-name': { type: 'string' },
            description: { type: 'string' },
          },
          allowPositionals: false,
        });

        await cli.createMigration(args[1]!, {
          template: values.template,
          tableName: values['table-name'],
          columnName: values['column-name'],
          columnType: values['column-type'],
          indexName: values['index-name'],
          description: values.description,
        });
        break;
      }

      case 'migrate:up': {
        const { values } = parseArgs({
          args: args.slice(1),
          options: {
            count: { type: 'string' },
            to: { type: 'string' },
            'dry-run': { type: 'boolean' },
            'no-lock': { type: 'boolean' },
          },
          allowPositionals: false,
        });

        await cli.runMigrations({
          count: values.count ? parseInt(values.count) : undefined,
          to: values.to,
          dryRun: values['dry-run'],
          lockTable: !values['no-lock'],
        });
        break;
      }

      case 'migrate:down': {
        const { values } = parseArgs({
          args: args.slice(1),
          options: {
            count: { type: 'string' },
            to: { type: 'string' },
            'dry-run': { type: 'boolean' },
            'no-lock': { type: 'boolean' },
          },
          allowPositionals: false,
        });

        await cli.rollbackMigrations({
          count: values.count ? parseInt(values.count) : undefined,
          to: values.to,
          dryRun: values['dry-run'],
          lockTable: !values['no-lock'],
        });
        break;
      }

      case 'migrate:status':
        await cli.showMigrationStatus();
        break;

      case 'migrate:test':
        await cli.testConnection();
        break;

      case 'version':
        console.log(version);
        break;

      case 'help':
      default:
        cli.showHelp();
        break;
    }
  } catch (error) {
    console.error('❌ Command failed:', error instanceof Error ? error.message : String(error));
    process.exit(1);
  }
}

// Run CLI if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runCLI().catch(error => {
    console.error('❌ CLI execution failed:', error);
    process.exit(1);
  });
}
