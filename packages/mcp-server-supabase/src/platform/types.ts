import { z } from 'zod';
import type { InitData } from '@supabase/mcp-utils';

// Simplified project schema for local instances
export const projectSchema = z.object({
  id: z.string(),
  organization_id: z.string(),
  name: z.string(),
  status: z.string(),
  created_at: z.string(),
  region: z.string(),
});

// Simplified edge function schema for local instances
export const edgeFunctionSchema = z.object({
  id: z.string(),
  slug: z.string(),
  name: z.string(),
  status: z.string(),
  version: z.number(),
  created_at: z.number().optional(),
  updated_at: z.number().optional(),
  verify_jwt: z.boolean().optional(),
  import_map: z.boolean().optional(),
  import_map_path: z.string().optional(),
  entrypoint_path: z.string().optional(),
  files: z.array(
    z.object({
      name: z.string(),
      content: z.string(),
    })
  ),
});

// Keep these for compatibility but they won't be used in local mode
export const createProjectOptionsSchema = z.object({
  name: z.string(),
  organization_id: z.string(),
  region: z.string().optional(),
  db_pass: z.string().optional(),
});

export const createBranchOptionsSchema = z.object({
  name: z.string(),
});

export const resetBranchOptionsSchema = z.object({
  migration_version: z.string().optional(),
});

export const deployEdgeFunctionOptionsSchema = z.object({
  name: z.string(),
  entrypoint_path: z.string(),
  import_map_path: z.string().optional(),
  files: z.array(
    z.object({
      name: z.string(),
      content: z.string(),
    })
  ),
});

export const executeSqlOptionsSchema = z.object({
  query: z.string(),
  read_only: z.boolean().optional(),
});

export const applyMigrationOptionsSchema = z.object({
  name: z.string(),
  query: z.string(),
});

export const migrationSchema = z.object({
  version: z.string(),
  name: z.string().optional(),
});

export const getLogsOptionsSchema = z.object({
  sql: z.string(),
  iso_timestamp_start: z.string().optional(),
  iso_timestamp_end: z.string().optional(),
});

export const generateTypescriptTypesResultSchema = z.object({
  types: z.string(),
});

export type Project = z.infer<typeof projectSchema>;
export type EdgeFunction = z.infer<typeof edgeFunctionSchema>;

export type CreateProjectOptions = z.infer<typeof createProjectOptionsSchema>;
export type CreateBranchOptions = z.infer<typeof createBranchOptionsSchema>;
export type ResetBranchOptions = z.infer<typeof resetBranchOptionsSchema>;
export type DeployEdgeFunctionOptions = z.infer<
  typeof deployEdgeFunctionOptionsSchema
>;

export type ExecuteSqlOptions = z.infer<typeof executeSqlOptionsSchema>;
export type ApplyMigrationOptions = z.infer<typeof applyMigrationOptionsSchema>;
export type Migration = z.infer<typeof migrationSchema>;
export type ListMigrationsResult = z.infer<typeof migrationSchema>;

export type GetLogsOptions = z.infer<typeof getLogsOptionsSchema>;
export type GenerateTypescriptTypesResult = z.infer<
  typeof generateTypescriptTypesResultSchema
>;

export type SupabasePlatform = {
  init?(info: InitData): Promise<void>;

  // Database operations
  executeSql<T>(projectId: string, options: ExecuteSqlOptions): Promise<T[]>;
  listMigrations(projectId: string): Promise<Migration[]>;
  applyMigration<T>(
    projectId: string,
    options: ApplyMigrationOptions
  ): Promise<T[]>;

  // Project management (throws errors for local instances)
  listOrganizations(): Promise<Array<{ id: string; name: string }>>;
  getOrganization(organizationId: string): Promise<any>;
  listProjects(): Promise<Project[]>;
  getProject(projectId: string): Promise<Project>;
  createProject(options: CreateProjectOptions): Promise<Project>;
  pauseProject(projectId: string): Promise<void>;
  restoreProject(projectId: string): Promise<void>;

  // Edge functions (simplified for local)
  listEdgeFunctions(projectId: string): Promise<EdgeFunction[]>;
  getEdgeFunction(
    projectId: string,
    functionSlug: string
  ): Promise<EdgeFunction>;
  deployEdgeFunction(
    projectId: string,
    options: DeployEdgeFunctionOptions
  ): Promise<Omit<EdgeFunction, 'files'>>;

  // Debugging (not available for local)
  getLogs(projectId: string, options: GetLogsOptions): Promise<unknown>;
  getSecurityAdvisors(projectId: string): Promise<unknown>;
  getPerformanceAdvisors(projectId: string): Promise<unknown>;

  // Development
  getProjectUrl(projectId: string): Promise<string>;
  getAnonKey(projectId: string): Promise<string>;
  generateTypescriptTypes(
    projectId: string
  ): Promise<GenerateTypescriptTypesResult>;

  // Branching (not available for local)
  listBranches(projectId: string): Promise<any[]>;
  createBranch(
    projectId: string,
    options: CreateBranchOptions
  ): Promise<any>;
  deleteBranch(branchId: string): Promise<void>;
  mergeBranch(branchId: string): Promise<void>;
  resetBranch(branchId: string, options: ResetBranchOptions): Promise<void>;
  rebaseBranch(branchId: string): Promise<void>;
};
