import { createClient, type SupabaseClient } from '@supabase/supabase-js';
import type { InitData } from '@supabase/mcp-utils';
import type { LocalConfig } from '../config/local-config.js';
import {
  type ApplyMigrationOptions,
  type CreateBranchOptions,
  type CreateProjectOptions,
  type DeployEdgeFunctionOptions,
  type EdgeFunction,
  type ExecuteSqlOptions,
  type GetLogsOptions,
  type Migration,
  type ResetBranchOptions,
  type SupabasePlatform,
  type GenerateTypescriptTypesResult,
} from './types.js';

export type LocalSupabasePlatformOptions = LocalConfig;

/**
 * Creates a Supabase platform implementation for local self-hosted instances.
 */
export function createLocalSupabasePlatform(
  options: LocalSupabasePlatformOptions
): SupabasePlatform {
  const { SUPABASE_URL, SUPABASE_ANON_KEY, SUPABASE_SERVICE_ROLE_KEY, DEBUG_SQL } = options;

  // Regular client with anon key for read operations
  const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
  
  // Admin client with service role key for privileged operations
  const adminSupabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY, {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  });

  const platform: SupabasePlatform = {
    async init(info: InitData) {
      // For local instances, we don't need complex initialization
      // Just verify we can connect to the instance
      try {
        const { data, error } = await supabase
          .from('information_schema.tables')
          .select('count')
          .limit(1);
        
        if (error) {
          throw new Error(`Failed to connect to local Supabase instance: ${error.message}`);
        }
        
        if (DEBUG_SQL) {
          console.log('Successfully connected to local Supabase instance');
        }
      } catch (error) {
        throw new Error(`Failed to initialize local Supabase platform: ${error}`);
      }
    },

    async executeSql<T>(projectId: string, options: ExecuteSqlOptions): Promise<T[]> {
      const { query, read_only } = options;
      
      if (DEBUG_SQL) {
        console.log('Executing SQL:', query);
      }

      // Use admin client for write operations, regular client for read-only
      const client = read_only ? supabase : adminSupabase;
      
      try {
        // For direct SQL execution, we need to use the RPC function
        const { data, error } = await client.rpc('exec_sql', { query });
        
        if (error) {
          throw new Error(`SQL execution failed: ${error.message}`);
        }
        
        // The exec_sql function returns data in a nested format
        // We need to extract the actual result from the response
        if (Array.isArray(data) && data.length > 0 && data[0] && typeof data[0] === 'object') {
          // Check if it's wrapped in a result property
          if ('result' in data[0] && Array.isArray(data[0].result)) {
            return data[0].result as T[];
          }
          // Check if it's the exec_sql response format
          if ('exec_sql' in data[0] && Array.isArray(data[0].exec_sql)) {
            return data[0].exec_sql as T[];
          }
        }
        
        return data as T[];
      } catch (error) {
        // Fallback: try using the REST API for simple queries
        if (query.toLowerCase().trim().startsWith('select')) {
          // Parse table name from simple SELECT queries
          const tableMatch = query.match(/from\s+([a-zA-Z_][a-zA-Z0-9_]*)/i);
          if (tableMatch && tableMatch[1]) {
            const tableName = tableMatch[1];
            const { data, error } = await client.from(tableName).select('*');
            if (error) {
              throw new Error(`Query failed: ${error.message}`);
            }
            return data as T[];
          }
        }
        
        throw new Error(`SQL execution failed: ${String(error)}`);
      }
    },

    async listMigrations(projectId: string): Promise<Migration[]> {
      // For local instances, we can check the migrations table if it exists
      try {
        const { data, error } = await adminSupabase
          .from('supabase_migrations.schema_migrations')
          .select('version, name')
          .order('version', { ascending: true });
        
        if (error) {
          // If migrations table doesn't exist, return empty array
          if (error.code === 'PGRST116') {
            return [];
          }
          throw error;
        }
        
        return data || [];
      } catch (error) {
        // Return empty array if we can't access migrations
        console.warn('Could not list migrations:', error);
        return [];
      }
    },

    async applyMigration<T>(projectId: string, options: ApplyMigrationOptions): Promise<T[]> {
      const { name, query } = options;
      
      if (DEBUG_SQL) {
        console.log('Applying migration:', name);
      }

      // Execute the migration query
      const result = await platform.executeSql<T>(projectId, { query, read_only: false });
      
      // Record the migration in the migrations table
      try {
        await adminSupabase
          .from('supabase_migrations.schema_migrations')
          .insert({ version: Date.now().toString(), name });
      } catch (error) {
        console.warn('Could not record migration:', error);
      }
      
      return result;
    },

    // Local instances don't have organizations - these throw errors
    async listOrganizations() {
      throw new Error('Organization management is not available for local Supabase instances');
    },

    async getOrganization(organizationId: string) {
      throw new Error('Organization management is not available for local Supabase instances');
    },

    // Local instances don't have project management - these throw errors  
    async listProjects() {
      throw new Error('Project management is not available for local Supabase instances. You are connected to a single local instance.');
    },

    async getProject(projectId: string) {
      // Return mock project data for the local instance
      return {
        id: 'local',
        organization_id: 'local',
        name: 'Local Supabase Instance',
        status: 'ACTIVE_HEALTHY',
        created_at: new Date().toISOString(),
        region: 'devdb.syncrobit.net',
      };
    },

    async createProject(options: CreateProjectOptions) {
      throw new Error('Project creation is not available for local Supabase instances');
    },

    async pauseProject(projectId: string) {
      throw new Error('Project management is not available for local Supabase instances');
    },

    async restoreProject(projectId: string) {
      throw new Error('Project management is not available for local Supabase instances');
    },

    // Edge functions - simplified for local development
    async listEdgeFunctions(projectId: string): Promise<EdgeFunction[]> {
      try {
        // Try to list functions via the functions endpoint
        const response = await fetch(`${SUPABASE_URL}/functions/v1/`, {
          headers: {
            'Authorization': `Bearer ${SUPABASE_SERVICE_ROLE_KEY}`,
            'apikey': SUPABASE_ANON_KEY,
          },
        });
        
        if (!response.ok) {
          return [];
        }
        
        const functions = await response.json();
        return Array.isArray(functions) ? functions : [];
      } catch (error) {
        console.warn('Could not list edge functions:', error);
        return [];
      }
    },

    async getEdgeFunction(projectId: string, functionSlug: string): Promise<EdgeFunction> {
      const functions = await platform.listEdgeFunctions(projectId);
      const func = functions.find(f => f.slug === functionSlug);
      
      if (!func) {
        throw new Error(`Edge function '${functionSlug}' not found`);
      }
      
      return func;
    },

    async deployEdgeFunction(projectId: string, options: DeployEdgeFunctionOptions) {
      throw new Error(
        'Edge function deployment for local instances should be done using the Supabase CLI:\n' +
        `supabase functions deploy ${options.name}`
      );
    },

    // Debugging - not available for local instances
    async getLogs(projectId: string, options: GetLogsOptions) {
      throw new Error(
        'Cloud-style logs are not available for local instances. Use Docker logs instead:\n' +
        'docker compose logs supabase-db\n' +
        'docker compose logs supabase-auth\n' +
        'docker compose logs supabase-rest'
      );
    },

    async getSecurityAdvisors(projectId: string) {
      throw new Error('Security advisors are not available for local Supabase instances');
    },

    async getPerformanceAdvisors(projectId: string) {
      throw new Error('Performance advisors are not available for local Supabase instances');
    },

    // Development helpers
    async getProjectUrl(projectId: string): Promise<string> {
      return SUPABASE_URL;
    },

    async getAnonKey(projectId: string): Promise<string> {
      return SUPABASE_ANON_KEY;
    },

    async generateTypescriptTypes(projectId: string): Promise<GenerateTypescriptTypesResult> {
      return {
        types: `
// To generate TypeScript types for your local Supabase instance, run:
// npx supabase gen types typescript --local > types/database.ts

// Or if you have the CLI installed globally:
// supabase gen types typescript --local > types/database.ts

// This will generate types based on your current database schema.
`.trim(),
      };
    },

    // Branching is not available for local instances
    async listBranches(projectId: string) {
      throw new Error('Branching is not available for local Supabase instances');
    },

    async createBranch(projectId: string, options: CreateBranchOptions) {
      throw new Error('Branching is not available for local Supabase instances');
    },

    async deleteBranch(branchId: string) {
      throw new Error('Branching is not available for local Supabase instances');
    },

    async mergeBranch(branchId: string) {
      throw new Error('Branching is not available for local Supabase instances');
    },

    async resetBranch(branchId: string, options: ResetBranchOptions) {
      throw new Error('Branching is not available for local Supabase instances');
    },

    async rebaseBranch(branchId: string) {
      throw new Error('Branching is not available for local Supabase instances');
    },
  };

  return platform;
}