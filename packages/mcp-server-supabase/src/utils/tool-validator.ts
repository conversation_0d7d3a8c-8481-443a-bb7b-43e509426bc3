import { z } from 'zod';
import type { Tool } from '@supabase/mcp-utils';
import type { EnhancedTool, ValidationResult } from './tool-registry.js';
import { contextLogger } from './logger.js';
import { ValidationError } from './errors.js';

const Ajv = require('ajv');
const addFormats = require('ajv-formats');
const zodToJsonSchema = require('zod-to-json-schema');

/**
 * JSON Schema validation result
 */
export interface JsonSchemaValidationResult {
  valid: boolean;
  errors: Array<{
    instancePath: string;
    schemaPath: string;
    keyword: string;
    message: string;
    data?: unknown;
  }>;
}

/**
 * Tool compliance check result
 */
export interface ToolComplianceResult {
  compliant: boolean;
  issues: Array<{
    severity: 'error' | 'warning' | 'info';
    category: 'naming' | 'schema' | 'metadata' | 'performance' | 'security';
    message: string;
    suggestion?: string;
  }>;
  score: number; // 0-100
}

/**
 * Tool performance analysis result
 */
export interface ToolPerformanceAnalysis {
  estimatedComplexity: 'low' | 'medium' | 'high';
  potentialBottlenecks: string[];
  recommendations: string[];
  cacheability: 'none' | 'short' | 'long';
}

/**
 * Enhanced tool validator with comprehensive validation capabilities
 */
export class ToolValidator {
  private ajv: any;
  private logger = contextLogger.child({ component: 'ToolValidator' });

  constructor() {
    this.ajv = new Ajv({
      allErrors: true,
      verbose: true,
      strict: false,
    });
    addFormats(this.ajv);
  }

  /**
   * Validate a tool's JSON schema
   */
  validateJsonSchema(schema: unknown): JsonSchemaValidationResult {
    try {
      // Create a meta-schema validator
      const metaSchema = {
        type: 'object',
        properties: {
          type: { type: 'string' },
          properties: { type: 'object' },
          required: { type: 'array', items: { type: 'string' } },
          additionalProperties: { type: 'boolean' },
        },
        required: ['type'],
        additionalProperties: true,
      };

      const validate = this.ajv.compile(metaSchema);
      const valid = validate(schema);

      if (!valid && validate.errors) {
        return {
          valid: false,
          errors: validate.errors.map((error: any) => ({
            instancePath: error.instancePath || '',
            schemaPath: error.schemaPath || '',
            keyword: error.keyword || '',
            message: error.message || 'Unknown error',
            data: error.data,
          })),
        };
      }

      return { valid: true, errors: [] };
    } catch (error) {
      return {
        valid: false,
        errors: [
          {
            instancePath: '',
            schemaPath: '',
            keyword: 'exception',
            message: error instanceof Error ? error.message : String(error),
          },
        ],
      };
    }
  }

  /**
   * Validate tool parameters against schema
   */
  async validateParameters<T extends z.ZodObject<any>>(
    schema: T,
    params: unknown
  ): Promise<ValidationResult> {
    const result: ValidationResult = {
      valid: true,
      errors: [],
      warnings: [],
    };

    try {
      // Validate with Zod
      schema.parse(params);
    } catch (error) {
      if (error instanceof z.ZodError) {
        result.valid = false;
        result.errors = error.errors.map(err => ({
          field: err.path.join('.'),
          message: err.message,
          code: err.code,
        }));
      } else {
        result.valid = false;
        result.errors.push({
          field: 'root',
          message: error instanceof Error ? error.message : String(error),
          code: 'UNKNOWN_ERROR',
        });
      }
    }

    // Additional validation checks
    if (result.valid && typeof params === 'object' && params !== null) {
      // Check for potentially dangerous patterns
      const dangerousPatterns = this.checkForDangerousPatterns(params as Record<string, unknown>);
      result.warnings.push(...dangerousPatterns);

      // Check for performance concerns
      const performanceWarnings = this.checkForPerformanceConcerns(params as Record<string, unknown>);
      result.warnings.push(...performanceWarnings);
    }

    return result;
  }

  /**
   * Perform comprehensive tool compliance check
   */
  checkToolCompliance(tool: Tool | EnhancedTool): ToolComplianceResult {
    const issues: ToolComplianceResult['issues'] = [];
    let score = 100;

    // Check naming conventions
    const namingIssues = this.checkNamingConventions(tool);
    issues.push(...namingIssues);
    score -= namingIssues.filter(i => i.severity === 'error').length * 20;
    score -= namingIssues.filter(i => i.severity === 'warning').length * 5;

    // Check schema quality
    const schemaIssues = this.checkSchemaQuality(tool);
    issues.push(...schemaIssues);
    score -= schemaIssues.filter(i => i.severity === 'error').length * 15;
    score -= schemaIssues.filter(i => i.severity === 'warning').length * 3;

    // Check metadata quality (for enhanced tools)
    if ('metadata' in tool) {
      const metadataIssues = this.checkMetadataQuality(tool as EnhancedTool);
      issues.push(...metadataIssues);
      score -= metadataIssues.filter(i => i.severity === 'error').length * 10;
      score -= metadataIssues.filter(i => i.severity === 'warning').length * 2;
    }

    // Check security considerations
    const securityIssues = this.checkSecurityConsiderations(tool);
    issues.push(...securityIssues);
    score -= securityIssues.filter(i => i.severity === 'error').length * 25;
    score -= securityIssues.filter(i => i.severity === 'warning').length * 10;

    score = Math.max(0, Math.min(100, score));

    return {
      compliant: issues.filter(i => i.severity === 'error').length === 0,
      issues,
      score,
    };
  }

  /**
   * Analyze tool performance characteristics
   */
  analyzePerformance(tool: Tool | EnhancedTool): ToolPerformanceAnalysis {
    const potentialBottlenecks: string[] = [];
    const recommendations: string[] = [];

    // Analyze parameter schema complexity
    const jsonSchema = zodToJsonSchema.default ? zodToJsonSchema.default(tool.parameters) : zodToJsonSchema(tool.parameters);
    const schemaComplexity = this.calculateSchemaComplexity(jsonSchema);

    let estimatedComplexity: 'low' | 'medium' | 'high' = 'low';
    let cacheability: 'none' | 'short' | 'long' = 'short';

    if (schemaComplexity > 10) {
      estimatedComplexity = 'high';
      potentialBottlenecks.push('Complex parameter schema may slow validation');
      recommendations.push('Consider simplifying parameter structure');
    } else if (schemaComplexity > 5) {
      estimatedComplexity = 'medium';
    }

    // Check for database operations
    if (tool.description.toLowerCase().includes('sql') || 
        tool.description.toLowerCase().includes('database') ||
        tool.description.toLowerCase().includes('query')) {
      estimatedComplexity = 'high';
      cacheability = 'none';
      potentialBottlenecks.push('Database operations can be slow');
      recommendations.push('Consider implementing query optimization');
      recommendations.push('Add appropriate database indexes');
    }

    // Check for network operations
    if (tool.description.toLowerCase().includes('api') ||
        tool.description.toLowerCase().includes('http') ||
        tool.description.toLowerCase().includes('fetch')) {
      potentialBottlenecks.push('Network operations may have variable latency');
      recommendations.push('Implement proper timeout handling');
      recommendations.push('Consider caching responses when appropriate');
    }

    // Check for file operations
    if (tool.description.toLowerCase().includes('file') ||
        tool.description.toLowerCase().includes('read') ||
        tool.description.toLowerCase().includes('write')) {
      potentialBottlenecks.push('File I/O operations can be slow');
      recommendations.push('Consider streaming for large files');
      recommendations.push('Implement proper error handling for file operations');
    }

    return {
      estimatedComplexity,
      potentialBottlenecks,
      recommendations,
      cacheability,
    };
  }

  /**
   * Check naming conventions
   */
  private checkNamingConventions(tool: Tool | EnhancedTool): ToolComplianceResult['issues'] {
    const issues: ToolComplianceResult['issues'] = [];
    const toolName = 'metadata' in tool ? tool.metadata.name : 'unknown';

    // Check snake_case convention
    if (!/^[a-z][a-z0-9_]*$/.test(toolName)) {
      issues.push({
        severity: 'error',
        category: 'naming',
        message: `Tool name '${toolName}' should follow snake_case convention`,
        suggestion: 'Use lowercase letters, numbers, and underscores only',
      });
    }

    // Check for overly long names
    if (toolName.length > 50) {
      issues.push({
        severity: 'warning',
        category: 'naming',
        message: `Tool name '${toolName}' is very long (${toolName.length} characters)`,
        suggestion: 'Consider using a shorter, more concise name',
      });
    }

    // Check for meaningful names
    const genericNames = ['tool', 'function', 'method', 'action', 'do', 'run', 'execute'];
    if (genericNames.some(generic => toolName.includes(generic))) {
      issues.push({
        severity: 'warning',
        category: 'naming',
        message: `Tool name '${toolName}' contains generic terms`,
        suggestion: 'Use more specific, descriptive names',
      });
    }

    return issues;
  }

  /**
   * Check schema quality
   */
  private checkSchemaQuality(tool: Tool | EnhancedTool): ToolComplianceResult['issues'] {
    const issues: ToolComplianceResult['issues'] = [];

    try {
      const jsonSchema = zodToJsonSchema.default ? zodToJsonSchema.default(tool.parameters) : zodToJsonSchema(tool.parameters);
      const validation = this.validateJsonSchema(jsonSchema);

      if (!validation.valid) {
        issues.push({
          severity: 'error',
          category: 'schema',
          message: 'Tool schema is invalid',
          suggestion: 'Fix schema validation errors',
        });
      }

      // Check for missing descriptions
      if (jsonSchema && typeof jsonSchema === 'object' && 'properties' in jsonSchema) {
        const properties = jsonSchema.properties as Record<string, any>;
        for (const [propName, propSchema] of Object.entries(properties)) {
          if (!propSchema.description) {
            issues.push({
              severity: 'warning',
              category: 'schema',
              message: `Parameter '${propName}' lacks description`,
              suggestion: 'Add descriptions to all parameters for better usability',
            });
          }
        }
      }

      // Check schema complexity
      const complexity = this.calculateSchemaComplexity(jsonSchema);
      if (complexity > 15) {
        issues.push({
          severity: 'warning',
          category: 'schema',
          message: 'Tool schema is very complex',
          suggestion: 'Consider simplifying the parameter structure',
        });
      }
    } catch (error) {
      issues.push({
        severity: 'error',
        category: 'schema',
        message: `Failed to analyze schema: ${error instanceof Error ? error.message : String(error)}`,
      });
    }

    return issues;
  }

  /**
   * Check metadata quality for enhanced tools
   */
  private checkMetadataQuality(tool: EnhancedTool): ToolComplianceResult['issues'] {
    const issues: ToolComplianceResult['issues'] = [];
    const metadata = tool.metadata;

    // Check description length
    if (metadata.description.length < 20) {
      issues.push({
        severity: 'warning',
        category: 'metadata',
        message: 'Tool description is too short',
        suggestion: 'Provide a more detailed description (at least 20 characters)',
      });
    }

    // Check for examples
    if (!metadata.examples || metadata.examples.length === 0) {
      issues.push({
        severity: 'info',
        category: 'metadata',
        message: 'Tool lacks usage examples',
        suggestion: 'Add examples to improve developer experience',
      });
    }

    // Check version format
    if (!/^\d+\.\d+\.\d+/.test(metadata.version)) {
      issues.push({
        severity: 'warning',
        category: 'metadata',
        message: 'Tool version should follow semantic versioning',
        suggestion: 'Use format: major.minor.patch (e.g., 1.0.0)',
      });
    }

    // Check for tags
    if (metadata.tags.length === 0) {
      issues.push({
        severity: 'info',
        category: 'metadata',
        message: 'Tool has no tags',
        suggestion: 'Add relevant tags for better discoverability',
      });
    }

    return issues;
  }

  /**
   * Check security considerations
   */
  private checkSecurityConsiderations(tool: Tool | EnhancedTool): ToolComplianceResult['issues'] {
    const issues: ToolComplianceResult['issues'] = [];

    // Check for SQL injection risks
    if (tool.description.toLowerCase().includes('sql') ||
        tool.description.toLowerCase().includes('query')) {
      issues.push({
        severity: 'warning',
        category: 'security',
        message: 'Tool performs SQL operations - ensure proper sanitization',
        suggestion: 'Use parameterized queries and input validation',
      });
    }

    // Check for file system access
    if (tool.description.toLowerCase().includes('file') ||
        tool.description.toLowerCase().includes('path')) {
      issues.push({
        severity: 'warning',
        category: 'security',
        message: 'Tool accesses file system - validate paths carefully',
        suggestion: 'Implement path traversal protection and access controls',
      });
    }

    // Check for network operations
    if (tool.description.toLowerCase().includes('http') ||
        tool.description.toLowerCase().includes('api') ||
        tool.description.toLowerCase().includes('url')) {
      issues.push({
        severity: 'info',
        category: 'security',
        message: 'Tool makes network requests - validate URLs and implement timeouts',
        suggestion: 'Use allowlists for external services and implement proper error handling',
      });
    }

    return issues;
  }

  /**
   * Check for dangerous patterns in parameters
   */
  private checkForDangerousPatterns(params: Record<string, unknown>): ValidationResult['warnings'] {
    const warnings: ValidationResult['warnings'] = [];

    for (const [key, value] of Object.entries(params)) {
      if (typeof value === 'string') {
        // Check for potential SQL injection patterns
        if (/(?:union|select|insert|update|delete|drop|create|alter)\s/i.test(value)) {
          warnings.push({
            field: key,
            message: 'Parameter contains SQL keywords - ensure proper sanitization',
            code: 'POTENTIAL_SQL_INJECTION',
          });
        }

        // Check for potential path traversal
        if (/\.\.\/|\.\.\\/.test(value)) {
          warnings.push({
            field: key,
            message: 'Parameter contains path traversal patterns',
            code: 'POTENTIAL_PATH_TRAVERSAL',
          });
        }

        // Check for potential script injection
        if (/<script|javascript:|data:/i.test(value)) {
          warnings.push({
            field: key,
            message: 'Parameter contains potential script injection patterns',
            code: 'POTENTIAL_SCRIPT_INJECTION',
          });
        }
      }
    }

    return warnings;
  }

  /**
   * Check for performance concerns in parameters
   */
  private checkForPerformanceConcerns(params: Record<string, unknown>): ValidationResult['warnings'] {
    const warnings: ValidationResult['warnings'] = [];

    for (const [key, value] of Object.entries(params)) {
      // Check for very large strings
      if (typeof value === 'string' && value.length > 10000) {
        warnings.push({
          field: key,
          message: `Parameter is very large (${value.length} characters)`,
          code: 'LARGE_PARAMETER',
        });
      }

      // Check for very large arrays
      if (Array.isArray(value) && value.length > 1000) {
        warnings.push({
          field: key,
          message: `Parameter array is very large (${value.length} items)`,
          code: 'LARGE_ARRAY',
        });
      }

      // Check for deeply nested objects
      if (typeof value === 'object' && value !== null) {
        const depth = this.getObjectDepth(value);
        if (depth > 10) {
          warnings.push({
            field: key,
            message: `Parameter object is deeply nested (depth: ${depth})`,
            code: 'DEEP_NESTING',
          });
        }
      }
    }

    return warnings;
  }

  /**
   * Calculate schema complexity score
   */
  private calculateSchemaComplexity(schema: unknown): number {
    if (!schema || typeof schema !== 'object') return 0;

    let complexity = 0;
    const obj = schema as Record<string, unknown>;

    // Count properties
    if (obj.properties && typeof obj.properties === 'object') {
      complexity += Object.keys(obj.properties).length;
    }

    // Add complexity for nested objects
    if (obj.properties) {
      for (const prop of Object.values(obj.properties as Record<string, unknown>)) {
        if (typeof prop === 'object' && prop !== null) {
          complexity += this.calculateSchemaComplexity(prop) * 0.5;
        }
      }
    }

    // Add complexity for arrays
    if (obj.items) {
      complexity += this.calculateSchemaComplexity(obj.items) * 0.3;
    }

    return complexity;
  }

  /**
   * Get object nesting depth
   */
  private getObjectDepth(obj: unknown): number {
    if (typeof obj !== 'object' || obj === null) return 0;

    let maxDepth = 0;
    for (const value of Object.values(obj as Record<string, unknown>)) {
      if (typeof value === 'object' && value !== null) {
        maxDepth = Math.max(maxDepth, this.getObjectDepth(value));
      }
    }

    return maxDepth + 1;
  }
}

/**
 * Global tool validator instance
 */
export const toolValidator = new ToolValidator();
