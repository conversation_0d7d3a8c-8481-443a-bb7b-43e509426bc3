import { describe, expect, test, beforeEach, afterEach, vi } from 'vitest';
import { 
  createLogger, 
  RequestTracker, 
  ContextualLogger,
  type LogContext 
} from './logger.js';

// Mock pino for testing
vi.mock('pino', () => ({
  default: vi.fn(() => ({
    trace: vi.fn(),
    debug: vi.fn(),
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
    fatal: vi.fn(),
  })),
  stdTimeFunctions: {
    isoTime: vi.fn(),
  },
  stdSerializers: {
    err: vi.fn(),
    req: vi.fn(),
    res: vi.fn(),
  },
  transport: vi.fn(),
}));

describe('Logger Creation', () => {
  test('createLogger should create logger with default config', () => {
    const logger = createLogger();
    expect(logger).toBeDefined();
  });

  test('createLogger should accept custom config', () => {
    const config = {
      level: 'debug' as const,
      pretty: true,
      redact: ['customField'],
    };
    
    const logger = createLogger(config);
    expect(logger).toBeDefined();
  });
});

describe('RequestTracker', () => {
  beforeEach(() => {
    // Clear any existing contexts
    RequestTracker.cleanup();
  });

  test('generateRequestId should return unique IDs', () => {
    const id1 = RequestTracker.generateRequestId();
    const id2 = RequestTracker.generateRequestId();
    
    expect(id1).toBeDefined();
    expect(id2).toBeDefined();
    expect(id1).not.toBe(id2);
    expect(typeof id1).toBe('string');
    expect(typeof id2).toBe('string');
  });

  test('setContext and getContext should work correctly', () => {
    const requestId = RequestTracker.generateRequestId();
    const context: LogContext = {
      requestId,
      toolName: 'test-tool',
      userId: 'user-123',
    };

    RequestTracker.setContext(requestId, context);
    const retrieved = RequestTracker.getContext(requestId);
    
    expect(retrieved).toEqual(context);
  });

  test('updateContext should merge contexts', () => {
    const requestId = RequestTracker.generateRequestId();
    const initialContext: LogContext = {
      requestId,
      toolName: 'test-tool',
    };

    RequestTracker.setContext(requestId, initialContext);
    RequestTracker.updateContext(requestId, { userId: 'user-123', operation: 'test' });
    
    const updated = RequestTracker.getContext(requestId);
    expect(updated).toEqual({
      requestId,
      toolName: 'test-tool',
      userId: 'user-123',
      operation: 'test',
    });
  });

  test('clearContext should remove context', () => {
    const requestId = RequestTracker.generateRequestId();
    const context: LogContext = { requestId, toolName: 'test-tool' };

    RequestTracker.setContext(requestId, context);
    expect(RequestTracker.getContext(requestId)).toEqual(context);
    
    RequestTracker.clearContext(requestId);
    expect(RequestTracker.getContext(requestId)).toBeUndefined();
  });

  test('cleanup should remove old contexts', () => {
    const requestId = RequestTracker.generateRequestId();
    const oldTimestamp = Date.now() - 2 * 60 * 60 * 1000; // 2 hours ago
    
    RequestTracker.setContext(requestId, { 
      requestId, 
      timestamp: oldTimestamp 
    });
    
    RequestTracker.cleanup();
    expect(RequestTracker.getContext(requestId)).toBeUndefined();
  });
});

describe('ContextualLogger', () => {
  let mockBaseLogger: any;
  let contextualLogger: ContextualLogger;

  beforeEach(() => {
    mockBaseLogger = {
      trace: vi.fn(),
      debug: vi.fn(),
      info: vi.fn(),
      warn: vi.fn(),
      error: vi.fn(),
      fatal: vi.fn(),
    };
    
    contextualLogger = new ContextualLogger(mockBaseLogger, { 
      requestId: 'test-request',
      component: 'test-component' 
    });
  });

  test('should log with context', () => {
    contextualLogger.info('Test message', { extra: 'data' });
    
    expect(mockBaseLogger.info).toHaveBeenCalledWith(
      expect.objectContaining({
        requestId: 'test-request',
        component: 'test-component',
        extra: 'data',
        timestamp: expect.any(Number),
      }),
      'Test message'
    );
  });

  test('should create child logger with merged context', () => {
    const child = contextualLogger.child({ toolName: 'test-tool' });
    
    child.debug('Debug message');
    
    expect(mockBaseLogger.debug).toHaveBeenCalledWith(
      expect.objectContaining({
        requestId: 'test-request',
        component: 'test-component',
        toolName: 'test-tool',
        timestamp: expect.any(Number),
      }),
      'Debug message'
    );
  });

  test('should handle error logging with error object', () => {
    const error = new Error('Test error');
    contextualLogger.error('Error occurred', error, { context: 'test' });
    
    expect(mockBaseLogger.error).toHaveBeenCalledWith(
      expect.objectContaining({
        requestId: 'test-request',
        component: 'test-component',
        context: 'test',
        error,
        timestamp: expect.any(Number),
      }),
      'Error occurred'
    );
  });

  test('startTimer should return function that logs duration', () => {
    const endTimer = contextualLogger.startTimer('test-operation');
    
    // Simulate some time passing
    setTimeout(() => {
      endTimer();
      
      expect(mockBaseLogger.info).toHaveBeenCalledWith(
        expect.objectContaining({
          operation: 'test-operation',
          duration: expect.any(Number),
        }),
        'Operation completed: test-operation'
      );
    }, 10);
  });

  test('logRequest should log request start', () => {
    contextualLogger.logRequest('GET', '/api/test', 'req-123');
    
    expect(mockBaseLogger.info).toHaveBeenCalledWith(
      expect.objectContaining({
        method: 'GET',
        path: '/api/test',
        requestId: 'req-123',
        type: 'request_start',
      }),
      'Request started: GET /api/test'
    );
  });

  test('logResponse should log request completion', () => {
    contextualLogger.logResponse('GET', '/api/test', 200, 150, 'req-123');
    
    expect(mockBaseLogger.info).toHaveBeenCalledWith(
      expect.objectContaining({
        method: 'GET',
        path: '/api/test',
        statusCode: 200,
        duration: 150,
        requestId: 'req-123',
        type: 'request_end',
      }),
      'Request completed: GET /api/test'
    );
  });

  test('logToolExecution should log successful tool execution', () => {
    contextualLogger.logToolExecution('test-tool', 'req-123', true, 100);
    
    expect(mockBaseLogger.info).toHaveBeenCalledWith(
      expect.objectContaining({
        toolName: 'test-tool',
        requestId: 'req-123',
        success: true,
        duration: 100,
        type: 'tool_execution',
      }),
      'Tool execution completed: test-tool'
    );
  });

  test('logToolExecution should log failed tool execution', () => {
    const error = new Error('Tool failed');
    contextualLogger.logToolExecution('test-tool', 'req-123', false, 50, error);
    
    expect(mockBaseLogger.error).toHaveBeenCalledWith(
      expect.objectContaining({
        toolName: 'test-tool',
        requestId: 'req-123',
        success: false,
        duration: 50,
        error,
        type: 'tool_execution',
      }),
      'Tool execution failed: test-tool'
    );
  });

  test('withRequestId should create logger with request context', () => {
    const requestId = 'new-request-id';
    RequestTracker.setContext(requestId, { 
      requestId, 
      userId: 'user-456' 
    });
    
    const requestLogger = contextualLogger.withRequestId(requestId);
    requestLogger.info('Test with request context');
    
    expect(mockBaseLogger.info).toHaveBeenCalledWith(
      expect.objectContaining({
        requestId: 'new-request-id',
        userId: 'user-456',
        component: 'test-component',
      }),
      'Test with request context'
    );
  });
});
