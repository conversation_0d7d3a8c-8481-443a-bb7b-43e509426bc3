import { describe, expect, test, beforeEach } from 'vitest';
import { z } from 'zod';
import { tool } from '@supabase/mcp-utils';
import { ToolRegistry, type EnhancedTool, type ToolMetadata } from './tool-registry.js';
import { ValidationError, ToolError } from './errors.js';

describe('ToolRegistry', () => {
  let registry: ToolRegistry;

  beforeEach(() => {
    registry = new ToolRegistry();
  });

  describe('Tool Registration', () => {
    test('should register a tool successfully', () => {
      const metadata: ToolMetadata = {
        name: 'test_tool',
        description: 'A test tool for validation',
        category: 'testing',
        version: '1.0.0',
        tags: ['test'],
        permissions: { readOnly: true, requiresAuth: false },
        performance: { complexity: 'low', cacheability: 'long' },
      };

      const testTool: EnhancedTool = {
        description: 'A test tool',
        parameters: z.object({
          input: z.string().describe('Test input'),
        }),
        execute: async (params) => ({ result: params.input }),
        metadata,
      };

      registry.register('test_tool', testTool);

      expect(registry.has('test_tool')).toBe(true);
      expect(registry.get('test_tool')).toBe(testTool);
    });

    test('should throw error for duplicate tool names', () => {
      const metadata: ToolMetadata = {
        name: 'duplicate_tool',
        description: 'A duplicate tool for testing',
        category: 'testing',
        version: '1.0.0',
        tags: ['test'],
        permissions: { readOnly: true, requiresAuth: false },
      };

      const testTool: EnhancedTool = {
        description: 'A test tool',
        parameters: z.object({}),
        execute: async () => ({}),
        metadata,
      };

      registry.register('duplicate_tool', testTool);

      expect(() => {
        registry.register('duplicate_tool', testTool);
      }).toThrow(ValidationError);
    });

    test('should validate tool metadata', () => {
      const invalidMetadata: ToolMetadata = {
        name: 'invalid-name', // Should be snake_case
        description: 'Short', // Too short
        category: '',
        version: 'invalid',
        tags: ['test'],
        permissions: { readOnly: true, requiresAuth: false },
      };

      const testTool: EnhancedTool = {
        description: 'A test tool',
        parameters: z.object({}),
        execute: async () => ({}),
        metadata: invalidMetadata,
      };

      expect(() => {
        registry.register('invalid-name', testTool);
      }).toThrow(ValidationError);
    });
  });

  describe('Tool Group Registration', () => {
    test('should register multiple tools from a group', () => {
      const tools = {
        tool_one: tool({
          description: 'First test tool',
          parameters: z.object({ param1: z.string() }),
          execute: async (params) => params,
        }),
        tool_two: tool({
          description: 'Second test tool',
          parameters: z.object({ param2: z.number() }),
          execute: async (params) => params,
        }),
      };

      const defaultMetadata = {
        category: 'test-group',
        version: '1.0.0',
        tags: ['group', 'test'],
      };

      registry.registerGroup('test-group', tools, defaultMetadata);

      expect(registry.has('tool_one')).toBe(true);
      expect(registry.has('tool_two')).toBe(true);
      expect(registry.getByCategory('test-group')).toHaveLength(2);
    });

    test('should handle partial failures in group registration', () => {
      const tools = {
        valid_tool: tool({
          description: 'A valid tool for testing',
          parameters: z.object({ param: z.string() }),
          execute: async (params) => params,
        }),
        'invalid-tool': tool({
          description: 'Invalid tool name',
          parameters: z.object({}),
          execute: async () => ({}),
        }),
      };

      expect(() => {
        registry.registerGroup('mixed-group', tools);
      }).toThrow(ToolError);

      // Valid tool should not be registered if group registration fails
      expect(registry.has('valid_tool')).toBe(false);
    });
  });

  describe('Tool Discovery', () => {
    beforeEach(() => {
      // Register test tools
      const tools = [
        {
          name: 'database_query',
          metadata: {
            name: 'database_query',
            description: 'Execute database queries',
            category: 'database',
            version: '1.0.0',
            tags: ['sql', 'database'],
            permissions: { readOnly: false, requiresAuth: true },
          },
        },
        {
          name: 'file_read',
          metadata: {
            name: 'file_read',
            description: 'Read files from filesystem',
            category: 'filesystem',
            version: '1.1.0',
            tags: ['file', 'read'],
            permissions: { readOnly: true, requiresAuth: false },
          },
        },
        {
          name: 'deprecated_tool',
          metadata: {
            name: 'deprecated_tool',
            description: 'An old deprecated tool',
            category: 'legacy',
            version: '0.9.0',
            tags: ['legacy'],
            deprecated: true,
            deprecationMessage: 'Use new_tool instead',
            permissions: { readOnly: true, requiresAuth: false },
          },
        },
      ];

      for (const toolDef of tools) {
        const enhancedTool: EnhancedTool = {
          description: toolDef.metadata.description,
          parameters: z.object({}),
          execute: async () => ({}),
          metadata: toolDef.metadata,
        };
        registry.register(toolDef.name, enhancedTool);
      }
    });

    test('should discover tools by category', () => {
      const databaseTools = registry.discover({ category: 'database' });
      expect(databaseTools).toHaveLength(1);
      expect(databaseTools[0].metadata.name).toBe('database_query');
    });

    test('should discover tools by tags', () => {
      const sqlTools = registry.discover({ tags: ['sql'] });
      expect(sqlTools).toHaveLength(1);
      expect(sqlTools[0].metadata.name).toBe('database_query');
    });

    test('should discover tools by deprecated status', () => {
      const deprecatedTools = registry.discover({ deprecated: true });
      expect(deprecatedTools).toHaveLength(1);
      expect(deprecatedTools[0].metadata.name).toBe('deprecated_tool');

      const activeTools = registry.discover({ deprecated: false });
      expect(activeTools).toHaveLength(2);
    });

    test('should discover tools by read-only permission', () => {
      const readOnlyTools = registry.discover({ readOnly: true });
      expect(readOnlyTools).toHaveLength(2);

      const writeTools = registry.discover({ readOnly: false });
      expect(writeTools).toHaveLength(1);
      expect(writeTools[0].metadata.name).toBe('database_query');
    });

    test('should discover tools by search term', () => {
      const searchResults = registry.discover({ search: 'database' });
      expect(searchResults).toHaveLength(1);
      expect(searchResults[0].metadata.name).toBe('database_query');
    });

    test('should discover tools by version', () => {
      const v1Tools = registry.discover({ version: '1.0.0' });
      expect(v1Tools).toHaveLength(1);
      expect(v1Tools[0].metadata.name).toBe('database_query');
    });

    test('should combine multiple filters', () => {
      const results = registry.discover({
        category: 'filesystem',
        readOnly: true,
        tags: ['file'],
      });
      expect(results).toHaveLength(1);
      expect(results[0].metadata.name).toBe('file_read');
    });
  });

  describe('Tool Validation', () => {
    test('should validate tool parameters successfully', async () => {
      const testTool: EnhancedTool = {
        description: 'A test tool with validation',
        parameters: z.object({
          name: z.string().min(1),
          age: z.number().min(0),
        }),
        execute: async (params) => params,
        metadata: {
          name: 'validation_tool',
          description: 'A tool for testing validation',
          category: 'testing',
          version: '1.0.0',
          tags: ['test'],
          permissions: { readOnly: true, requiresAuth: false },
        },
      };

      registry.register('validation_tool', testTool);

      const validParams = { name: 'John', age: 30 };
      const result = await registry.validateToolParams('validation_tool', validParams);

      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    test('should detect validation errors', async () => {
      const testTool: EnhancedTool = {
        description: 'A test tool with validation',
        parameters: z.object({
          name: z.string().min(1),
          age: z.number().min(0),
        }),
        execute: async (params) => params,
        metadata: {
          name: 'validation_tool',
          description: 'A tool for testing validation',
          category: 'testing',
          version: '1.0.0',
          tags: ['test'],
          permissions: { readOnly: true, requiresAuth: false },
        },
      };

      registry.register('validation_tool', testTool);

      const invalidParams = { name: '', age: -1 };
      const result = await registry.validateToolParams('validation_tool', invalidParams);

      expect(result.valid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });

    test('should handle non-existent tool validation', async () => {
      const result = await registry.validateToolParams('non_existent', {});

      expect(result.valid).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].code).toBe('TOOL_NOT_FOUND');
    });
  });

  describe('Tool Execution', () => {
    test('should execute tool successfully', async () => {
      const testTool: EnhancedTool = {
        description: 'A test tool for execution',
        parameters: z.object({
          input: z.string(),
        }),
        execute: async (params) => ({ output: params.input.toUpperCase() }),
        metadata: {
          name: 'execution_tool',
          description: 'A tool for testing execution',
          category: 'testing',
          version: '1.0.0',
          tags: ['test'],
          permissions: { readOnly: true, requiresAuth: false },
        },
      };

      registry.register('execution_tool', testTool);

      const result = await registry.executeTool('execution_tool', { input: 'hello' });

      expect(result).toEqual({ output: 'HELLO' });
    });

    test('should handle execution errors', async () => {
      const testTool: EnhancedTool = {
        description: 'A test tool that throws errors',
        parameters: z.object({}),
        execute: async () => {
          throw new Error('Execution failed');
        },
        metadata: {
          name: 'error_tool',
          description: 'A tool that throws errors',
          category: 'testing',
          version: '1.0.0',
          tags: ['test'],
          permissions: { readOnly: true, requiresAuth: false },
        },
      };

      registry.register('error_tool', testTool);

      await expect(registry.executeTool('error_tool', {})).rejects.toThrow('Execution failed');
    });
  });

  describe('Statistics and Utilities', () => {
    test('should provide accurate statistics', () => {
      const tools = [
        {
          name: 'tool_1',
          category: 'cat1',
          version: '1.0.0',
          deprecated: false,
          readOnly: true,
        },
        {
          name: 'tool_2',
          category: 'cat1',
          version: '1.0.0',
          deprecated: true,
          readOnly: false,
        },
        {
          name: 'tool_3',
          category: 'cat2',
          version: '2.0.0',
          deprecated: false,
          readOnly: true,
        },
      ];

      for (const toolDef of tools) {
        const enhancedTool: EnhancedTool = {
          description: `Tool ${toolDef.name}`,
          parameters: z.object({}),
          execute: async () => ({}),
          metadata: {
            name: toolDef.name,
            description: `Tool ${toolDef.name}`,
            category: toolDef.category,
            version: toolDef.version,
            tags: [],
            deprecated: toolDef.deprecated,
            permissions: { readOnly: toolDef.readOnly, requiresAuth: false },
          },
        };
        registry.register(toolDef.name, enhancedTool);
      }

      const stats = registry.getStatistics();

      expect(stats.totalTools).toBe(3);
      expect(stats.categories).toEqual({ cat1: 2, cat2: 1 });
      expect(stats.deprecated).toBe(1);
      expect(stats.readOnly).toBe(2);
      expect(stats.versions).toEqual({ '1.0.0': 2, '2.0.0': 1 });
    });

    test('should convert to MCP format correctly', () => {
      const testTool: EnhancedTool = {
        description: 'A test tool',
        parameters: z.object({
          param: z.string().describe('A parameter'),
        }),
        execute: async (params) => params,
        metadata: {
          name: 'mcp_tool',
          description: 'A tool for MCP format testing',
          category: 'testing',
          version: '1.0.0',
          tags: ['test'],
          permissions: { readOnly: true, requiresAuth: false },
        },
      };

      registry.register('mcp_tool', testTool);

      const mcpFormat = registry.toMcpFormat();

      expect(mcpFormat).toHaveLength(1);
      expect(mcpFormat[0]).toHaveProperty('name', 'mcp_tool');
      expect(mcpFormat[0]).toHaveProperty('description', 'A test tool');
      expect(mcpFormat[0]).toHaveProperty('inputSchema');
    });

    test('should clear registry', () => {
      const testTool: EnhancedTool = {
        description: 'A test tool',
        parameters: z.object({}),
        execute: async () => ({}),
        metadata: {
          name: 'clear_test',
          description: 'A tool for testing clear',
          category: 'testing',
          version: '1.0.0',
          tags: ['test'],
          permissions: { readOnly: true, requiresAuth: false },
        },
      };

      registry.register('clear_test', testTool);
      expect(registry.has('clear_test')).toBe(true);

      registry.clear();
      expect(registry.has('clear_test')).toBe(false);
      expect(registry.getStatistics().totalTools).toBe(0);
    });
  });
});
