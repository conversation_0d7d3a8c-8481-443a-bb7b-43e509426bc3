/**
 * Circuit Breaker Pattern Implementation for External Dependencies
 * 
 * Prevents cascading failures by monitoring external service health
 * and temporarily blocking requests when services are unhealthy.
 */

import { EventEmitter } from 'events';
import { contextLogger } from '../utils/logger.js';
import type { ConfigManager } from '../config/config-manager.js';

export type CircuitState = 'CLOSED' | 'OPEN' | 'HALF_OPEN';

export interface CircuitBreakerOptions {
  failureThreshold?: number;
  successThreshold?: number;
  timeout?: number;
  resetTimeout?: number;
  monitoringPeriod?: number;
  enableMetrics?: boolean;
  name?: string;
}

export interface CircuitBreakerStats {
  state: CircuitState;
  failures: number;
  successes: number;
  requests: number;
  lastFailureTime?: Date;
  lastSuccessTime?: Date;
  nextAttemptTime?: Date;
  failureRate: number;
  successRate: number;
  uptime: number;
}

export interface ExecutionResult<T> {
  success: boolean;
  result?: T;
  error?: Error;
  duration: number;
  fromCache?: boolean;
}

/**
 * Circuit breaker for protecting against cascading failures
 */
export class CircuitBreaker<T = any> extends EventEmitter {
  private readonly logger = contextLogger.child({ component: 'CircuitBreaker' });
  private readonly config?: ConfigManager;
  private readonly options: Required<CircuitBreakerOptions>;
  
  private state: CircuitState = 'CLOSED';
  private failures = 0;
  private successes = 0;
  private requests = 0;
  private lastFailureTime?: Date;
  private lastSuccessTime?: Date;
  private nextAttemptTime?: Date;
  private resetTimer?: NodeJS.Timeout;
  private monitoringTimer?: NodeJS.Timeout;
  private startTime = new Date();

  constructor(options: CircuitBreakerOptions = {}, config?: ConfigManager) {
    super();
    
    this.config = config;
    this.options = {
      failureThreshold: options.failureThreshold ?? config?.get('CIRCUIT_FAILURE_THRESHOLD', 5) ?? 5,
      successThreshold: options.successThreshold ?? config?.get('CIRCUIT_SUCCESS_THRESHOLD', 3) ?? 3,
      timeout: options.timeout ?? config?.get('CIRCUIT_TIMEOUT', 10000) ?? 10000,
      resetTimeout: options.resetTimeout ?? config?.get('CIRCUIT_RESET_TIMEOUT', 60000) ?? 60000,
      monitoringPeriod: options.monitoringPeriod ?? config?.get('CIRCUIT_MONITORING_PERIOD', 300000) ?? 300000, // 5 minutes
      enableMetrics: options.enableMetrics ?? config?.get('CIRCUIT_ENABLE_METRICS', true) ?? true,
      name: options.name ?? 'default',
    };

    this.startMonitoring();
    
    this.logger.info('Circuit breaker initialized', {
      name: this.options.name,
      failureThreshold: this.options.failureThreshold,
      resetTimeout: this.options.resetTimeout,
    });
  }

  /**
   * Execute a function with circuit breaker protection
   */
  async execute<R = T>(fn: () => Promise<R>): Promise<R> {
    this.requests++;

    // Check if circuit is open
    if (this.state === 'OPEN') {
      if (this.nextAttemptTime && Date.now() < this.nextAttemptTime.getTime()) {
        const error = new Error(`Circuit breaker is OPEN for ${this.options.name}. Next attempt at ${this.nextAttemptTime.toISOString()}`);
        this.logger.debug('Request blocked by circuit breaker', {
          name: this.options.name,
          state: this.state,
          nextAttemptTime: this.nextAttemptTime,
        });
        this.emit('requestBlocked', error);
        throw error;
      } else {
        // Transition to half-open
        this.transitionTo('HALF_OPEN');
      }
    }

    const startTime = Date.now();
    
    try {
      // Execute with timeout
      const result = await this.executeWithTimeout(fn);
      const duration = Date.now() - startTime;
      
      this.onSuccess(duration);
      
      this.logger.debug('Circuit breaker execution succeeded', {
        name: this.options.name,
        duration,
        state: this.state,
      });
      
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      
      this.onFailure(error as Error, duration);
      
      this.logger.warn('Circuit breaker execution failed', error instanceof Error ? error : undefined, {
        name: this.options.name,
        duration,
        state: this.state,
      });
      
      throw error;
    }
  }

  /**
   * Execute function with fallback
   */
  async executeWithFallback<R = T>(
    fn: () => Promise<R>,
    fallback: () => Promise<R> | R
  ): Promise<R> {
    try {
      return await this.execute(fn);
    } catch (error) {
      this.logger.info('Executing fallback due to circuit breaker', {
        name: this.options.name,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      
      const fallbackResult = await fallback();
      this.emit('fallbackExecuted', fallbackResult);
      return fallbackResult;
    }
  }

  /**
   * Get current circuit breaker statistics
   */
  getStats(): CircuitBreakerStats {
    const now = Date.now();
    const uptime = now - this.startTime.getTime();
    
    return {
      state: this.state,
      failures: this.failures,
      successes: this.successes,
      requests: this.requests,
      lastFailureTime: this.lastFailureTime,
      lastSuccessTime: this.lastSuccessTime,
      nextAttemptTime: this.nextAttemptTime,
      failureRate: this.requests > 0 ? this.failures / this.requests : 0,
      successRate: this.requests > 0 ? this.successes / this.requests : 0,
      uptime,
    };
  }

  /**
   * Get current state
   */
  getState(): CircuitState {
    return this.state;
  }

  /**
   * Manually reset the circuit breaker
   */
  reset(): void {
    this.transitionTo('CLOSED');
    this.failures = 0;
    this.successes = 0;
    this.lastFailureTime = undefined;
    this.nextAttemptTime = undefined;
    
    if (this.resetTimer) {
      clearTimeout(this.resetTimer);
      this.resetTimer = undefined;
    }
    
    this.logger.info('Circuit breaker manually reset', { name: this.options.name });
    this.emit('reset');
  }

  /**
   * Shutdown the circuit breaker
   */
  shutdown(): void {
    if (this.resetTimer) {
      clearTimeout(this.resetTimer);
      this.resetTimer = undefined;
    }
    
    if (this.monitoringTimer) {
      clearInterval(this.monitoringTimer);
      this.monitoringTimer = undefined;
    }
    
    this.removeAllListeners();
    
    this.logger.info('Circuit breaker shutdown', { name: this.options.name });
  }

  /**
   * Execute function with timeout
   */
  private async executeWithTimeout<R>(fn: () => Promise<R>): Promise<R> {
    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => {
        reject(new Error(`Circuit breaker timeout after ${this.options.timeout}ms`));
      }, this.options.timeout);

      fn()
        .then((result) => {
          clearTimeout(timer);
          resolve(result);
        })
        .catch((error) => {
          clearTimeout(timer);
          reject(error);
        });
    });
  }

  /**
   * Handle successful execution
   */
  private onSuccess(duration: number): void {
    this.successes++;
    this.lastSuccessTime = new Date();
    
    if (this.state === 'HALF_OPEN') {
      if (this.successes >= this.options.successThreshold) {
        this.transitionTo('CLOSED');
        this.failures = 0; // Reset failure count
      }
    }
    
    this.emit('success', { duration, state: this.state });
  }

  /**
   * Handle failed execution
   */
  private onFailure(error: Error, duration: number): void {
    this.failures++;
    this.lastFailureTime = new Date();
    
    if (this.state === 'CLOSED' || this.state === 'HALF_OPEN') {
      if (this.failures >= this.options.failureThreshold) {
        this.transitionTo('OPEN');
        this.scheduleReset();
      }
    }
    
    this.emit('failure', { error, duration, state: this.state });
  }

  /**
   * Transition to a new state
   */
  private transitionTo(newState: CircuitState): void {
    const oldState = this.state;
    this.state = newState;
    
    this.logger.info('Circuit breaker state transition', {
      name: this.options.name,
      from: oldState,
      to: newState,
      failures: this.failures,
      successes: this.successes,
    });
    
    this.emit('stateChange', { from: oldState, to: newState });
    
    // Reset success count when transitioning to half-open
    if (newState === 'HALF_OPEN') {
      this.successes = 0;
    }
  }

  /**
   * Schedule circuit reset attempt
   */
  private scheduleReset(): void {
    this.nextAttemptTime = new Date(Date.now() + this.options.resetTimeout);
    
    if (this.resetTimer) {
      clearTimeout(this.resetTimer);
    }
    
    this.resetTimer = setTimeout(() => {
      if (this.state === 'OPEN') {
        this.transitionTo('HALF_OPEN');
      }
      this.resetTimer = undefined;
      this.nextAttemptTime = undefined;
    }, this.options.resetTimeout);
    
    this.logger.info('Circuit breaker reset scheduled', {
      name: this.options.name,
      nextAttemptTime: this.nextAttemptTime,
    });
  }

  /**
   * Start monitoring and periodic reporting
   */
  private startMonitoring(): void {
    if (!this.options.enableMetrics) {
      return;
    }
    
    this.monitoringTimer = setInterval(() => {
      const stats = this.getStats();
      
      this.logger.debug('Circuit breaker metrics', {
        name: this.options.name,
        ...stats,
      });
      
      this.emit('metrics', stats);
    }, this.options.monitoringPeriod);
  }
}

/**
 * Circuit breaker registry for managing multiple circuit breakers
 */
export class CircuitBreakerRegistry {
  private static breakers = new Map<string, CircuitBreaker>();
  private static logger = contextLogger.child({ component: 'CircuitBreakerRegistry' });

  /**
   * Get or create a circuit breaker
   */
  static getOrCreate(name: string, options?: CircuitBreakerOptions, config?: ConfigManager): CircuitBreaker {
    if (!this.breakers.has(name)) {
      const breaker = new CircuitBreaker({ ...options, name }, config);
      this.breakers.set(name, breaker);
      
      this.logger.info('Circuit breaker registered', { name });
    }
    
    return this.breakers.get(name)!;
  }

  /**
   * Get circuit breaker by name
   */
  static get(name: string): CircuitBreaker | undefined {
    return this.breakers.get(name);
  }

  /**
   * Get all circuit breakers
   */
  static getAll(): Map<string, CircuitBreaker> {
    return new Map(this.breakers);
  }

  /**
   * Get statistics for all circuit breakers
   */
  static getAllStats(): Record<string, CircuitBreakerStats> {
    const stats: Record<string, CircuitBreakerStats> = {};
    
    for (const [name, breaker] of this.breakers.entries()) {
      stats[name] = breaker.getStats();
    }
    
    return stats;
  }

  /**
   * Remove circuit breaker
   */
  static remove(name: string): boolean {
    const breaker = this.breakers.get(name);
    if (breaker) {
      breaker.shutdown();
      this.breakers.delete(name);
      this.logger.info('Circuit breaker removed', { name });
      return true;
    }
    return false;
  }

  /**
   * Shutdown all circuit breakers
   */
  static shutdown(): void {
    for (const [name, breaker] of this.breakers.entries()) {
      breaker.shutdown();
    }
    this.breakers.clear();
    this.logger.info('All circuit breakers shutdown');
  }
}
