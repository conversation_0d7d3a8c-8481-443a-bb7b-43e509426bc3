/**
 * Compression and Streaming Middleware for Performance Optimization
 * 
 * Provides request/response compression, streaming support, and
 * HTTP caching headers for optimal performance.
 */

import { EventEmitter } from 'events';
import { createGzip, createDeflate, createBrotliCompress } from 'zlib';
import { pipeline } from 'stream/promises';
import { contextLogger } from '../utils/logger.js';
import type { ConfigManager } from '../config/config-manager.js';

export type CompressionAlgorithm = 'gzip' | 'deflate' | 'br' | 'none';

export interface CompressionOptions {
  threshold?: number;
  level?: number;
  algorithms?: CompressionAlgorithm[];
  enableStreaming?: boolean;
  chunkSize?: number;
  enableCaching?: boolean;
  cacheMaxAge?: number;
  enableETag?: boolean;
}

export interface StreamingOptions {
  highWaterMark?: number;
  objectMode?: boolean;
  enableBackpressure?: boolean;
  maxConcurrency?: number;
}

export interface CacheHeaders {
  'Cache-Control'?: string;
  'ETag'?: string;
  'Last-Modified'?: string;
  'Expires'?: string;
  'Vary'?: string;
}

export interface CompressionStats {
  totalRequests: number;
  compressedRequests: number;
  totalOriginalSize: number;
  totalCompressedSize: number;
  compressionRatio: number;
  averageCompressionTime: number;
  algorithmUsage: Record<CompressionAlgorithm, number>;
}

/**
 * Advanced compression and streaming middleware
 */
export class CompressionMiddleware extends EventEmitter {
  private readonly logger = contextLogger.child({ component: 'CompressionMiddleware' });
  private readonly config?: ConfigManager;
  private readonly options: Required<CompressionOptions>;
  
  private stats: CompressionStats = {
    totalRequests: 0,
    compressedRequests: 0,
    totalOriginalSize: 0,
    totalCompressedSize: 0,
    compressionRatio: 0,
    averageCompressionTime: 0,
    algorithmUsage: {
      gzip: 0,
      deflate: 0,
      br: 0,
      none: 0,
    },
  };
  
  private totalCompressionTime = 0;

  constructor(options: CompressionOptions = {}, config?: ConfigManager) {
    super();
    
    this.config = config;
    this.options = {
      threshold: options.threshold ?? config?.get('COMPRESSION_THRESHOLD', 1024) ?? 1024, // 1KB
      level: options.level ?? config?.get('COMPRESSION_LEVEL', 6) ?? 6,
      algorithms: options.algorithms ?? config?.get('COMPRESSION_ALGORITHMS', ['br', 'gzip', 'deflate']) ?? ['br', 'gzip', 'deflate'],
      enableStreaming: options.enableStreaming ?? config?.get('COMPRESSION_ENABLE_STREAMING', true) ?? true,
      chunkSize: options.chunkSize ?? config?.get('COMPRESSION_CHUNK_SIZE', 16384) ?? 16384, // 16KB
      enableCaching: options.enableCaching ?? config?.get('COMPRESSION_ENABLE_CACHING', true) ?? true,
      cacheMaxAge: options.cacheMaxAge ?? config?.get('COMPRESSION_CACHE_MAX_AGE', 3600) ?? 3600, // 1 hour
      enableETag: options.enableETag ?? config?.get('COMPRESSION_ENABLE_ETAG', true) ?? true,
    };

    this.logger.info('Compression middleware initialized', {
      threshold: this.options.threshold,
      level: this.options.level,
      algorithms: this.options.algorithms,
      enableStreaming: this.options.enableStreaming,
    });
  }

  /**
   * Compress data based on accepted encodings
   */
  async compress(
    data: string | Buffer,
    acceptedEncodings: string[] = ['gzip']
  ): Promise<{
    compressed: Buffer;
    algorithm: CompressionAlgorithm;
    originalSize: number;
    compressedSize: number;
    compressionTime: number;
  }> {
    const startTime = Date.now();
    this.stats.totalRequests++;
    
    const originalData = Buffer.isBuffer(data) ? data : Buffer.from(data, 'utf8');
    const originalSize = originalData.length;
    
    // Check if compression is worthwhile
    if (originalSize < this.options.threshold) {
      this.stats.algorithmUsage.none++;
      return {
        compressed: originalData,
        algorithm: 'none',
        originalSize,
        compressedSize: originalSize,
        compressionTime: 0,
      };
    }

    // Determine best compression algorithm
    const algorithm = this.selectCompressionAlgorithm(acceptedEncodings);
    
    try {
      const compressed = await this.compressWithAlgorithm(originalData, algorithm);
      const compressedSize = compressed.length;
      const compressionTime = Date.now() - startTime;
      
      // Update statistics
      this.stats.compressedRequests++;
      this.stats.totalOriginalSize += originalSize;
      this.stats.totalCompressedSize += compressedSize;
      this.stats.algorithmUsage[algorithm]++;
      this.totalCompressionTime += compressionTime;
      
      this.updateCompressionStats();
      
      this.logger.debug('Data compressed', {
        algorithm,
        originalSize,
        compressedSize,
        ratio: (1 - compressedSize / originalSize) * 100,
        compressionTime,
      });
      
      this.emit('compressed', {
        algorithm,
        originalSize,
        compressedSize,
        compressionTime,
      });
      
      return {
        compressed,
        algorithm,
        originalSize,
        compressedSize,
        compressionTime,
      };
    } catch (error) {
      this.logger.error('Compression failed', error instanceof Error ? error : undefined, {
        algorithm,
        originalSize,
      });
      
      // Fallback to uncompressed
      this.stats.algorithmUsage.none++;
      return {
        compressed: originalData,
        algorithm: 'none',
        originalSize,
        compressedSize: originalSize,
        compressionTime: Date.now() - startTime,
      };
    }
  }

  /**
   * Create streaming compression transform
   */
  createCompressionStream(algorithm: CompressionAlgorithm, options?: StreamingOptions) {
    const streamOptions = {
      level: this.options.level,
      chunkSize: this.options.chunkSize,
      highWaterMark: options?.highWaterMark,
    };

    switch (algorithm) {
      case 'gzip':
        return createGzip(streamOptions);
      case 'deflate':
        return createDeflate(streamOptions);
      case 'br':
        return createBrotliCompress(streamOptions);
      default:
        throw new Error(`Unsupported compression algorithm: ${algorithm}`);
    }
  }

  /**
   * Generate appropriate cache headers
   */
  generateCacheHeaders(
    data: string | Buffer,
    options: {
      maxAge?: number;
      etag?: boolean;
      lastModified?: Date;
      vary?: string[];
    } = {}
  ): CacheHeaders {
    const headers: CacheHeaders = {};

    if (this.options.enableCaching) {
      const maxAge = options.maxAge ?? this.options.cacheMaxAge;
      headers['Cache-Control'] = `public, max-age=${maxAge}`;
    }

    if (this.options.enableETag && (options.etag ?? true)) {
      headers['ETag'] = this.generateETag(data);
    }

    if (options.lastModified) {
      headers['Last-Modified'] = options.lastModified.toUTCString();
    }

    if (options.vary && options.vary.length > 0) {
      headers['Vary'] = options.vary.join(', ');
    }

    // Always vary on Accept-Encoding for compressed responses
    if (!headers['Vary']) {
      headers['Vary'] = 'Accept-Encoding';
    } else if (!headers['Vary'].includes('Accept-Encoding')) {
      headers['Vary'] += ', Accept-Encoding';
    }

    return headers;
  }

  /**
   * Check if request supports compression
   */
  supportsCompression(acceptEncoding?: string): boolean {
    if (!acceptEncoding) {
      return false;
    }

    const supported = this.options.algorithms.some(algorithm => 
      algorithm !== 'none' && acceptEncoding.includes(algorithm)
    );

    return supported;
  }

  /**
   * Get compression statistics
   */
  getStats(): CompressionStats {
    return { ...this.stats };
  }

  /**
   * Reset statistics
   */
  resetStats(): void {
    this.stats = {
      totalRequests: 0,
      compressedRequests: 0,
      totalOriginalSize: 0,
      totalCompressedSize: 0,
      compressionRatio: 0,
      averageCompressionTime: 0,
      algorithmUsage: {
        gzip: 0,
        deflate: 0,
        br: 0,
        none: 0,
      },
    };
    this.totalCompressionTime = 0;
  }

  /**
   * Select best compression algorithm based on accepted encodings
   */
  private selectCompressionAlgorithm(acceptedEncodings: string[]): CompressionAlgorithm {
    // Priority order: Brotli > Gzip > Deflate
    for (const algorithm of this.options.algorithms) {
      if (algorithm !== 'none' && acceptedEncodings.some(encoding => encoding.includes(algorithm))) {
        return algorithm;
      }
    }
    
    return 'none';
  }

  /**
   * Compress data with specific algorithm
   */
  private async compressWithAlgorithm(data: Buffer, algorithm: CompressionAlgorithm): Promise<Buffer> {
    if (algorithm === 'none') {
      return data;
    }

    return new Promise((resolve, reject) => {
      const chunks: Buffer[] = [];
      const stream = this.createCompressionStream(algorithm);
      
      stream.on('data', (chunk) => {
        chunks.push(chunk);
      });
      
      stream.on('end', () => {
        resolve(Buffer.concat(chunks));
      });
      
      stream.on('error', reject);
      
      stream.write(data);
      stream.end();
    });
  }

  /**
   * Generate ETag for data
   */
  private generateETag(data: string | Buffer): string {
    const crypto = require('crypto');
    const hash = crypto.createHash('md5');
    hash.update(data);
    return `"${hash.digest('hex')}"`;
  }

  /**
   * Update compression statistics
   */
  private updateCompressionStats(): void {
    if (this.stats.totalOriginalSize > 0) {
      this.stats.compressionRatio = 1 - (this.stats.totalCompressedSize / this.stats.totalOriginalSize);
    }
    
    if (this.stats.compressedRequests > 0) {
      this.stats.averageCompressionTime = this.totalCompressionTime / this.stats.compressedRequests;
    }
  }
}

/**
 * HTTP response optimization utilities
 */
export class ResponseOptimizer {
  private static compressionMiddleware?: CompressionMiddleware;
  private static logger = contextLogger.child({ component: 'ResponseOptimizer' });

  /**
   * Initialize response optimizer
   */
  static initialize(options?: CompressionOptions, config?: ConfigManager): void {
    this.compressionMiddleware = new CompressionMiddleware(options, config);
    this.logger.info('Response optimizer initialized');
  }

  /**
   * Optimize response with compression and caching
   */
  static async optimizeResponse(
    data: any,
    options: {
      acceptEncoding?: string;
      ifNoneMatch?: string;
      ifModifiedSince?: string;
      cacheOptions?: {
        maxAge?: number;
        etag?: boolean;
        lastModified?: Date;
        vary?: string[];
      };
    } = {}
  ): Promise<{
    data: Buffer;
    headers: Record<string, string>;
    statusCode: number;
  }> {
    if (!this.compressionMiddleware) {
      throw new Error('Response optimizer not initialized');
    }

    const serializedData = typeof data === 'string' ? data : JSON.stringify(data);
    const buffer = Buffer.from(serializedData, 'utf8');

    // Generate cache headers
    const cacheHeaders = this.compressionMiddleware.generateCacheHeaders(buffer, options.cacheOptions);
    
    // Check if client has cached version
    if (options.ifNoneMatch && cacheHeaders['ETag'] === options.ifNoneMatch) {
      return {
        data: Buffer.alloc(0),
        headers: cacheHeaders,
        statusCode: 304, // Not Modified
      };
    }

    // Compress if supported
    let responseData = buffer;
    let contentEncoding: string | undefined;
    
    if (this.compressionMiddleware.supportsCompression(options.acceptEncoding)) {
      const acceptedEncodings = options.acceptEncoding?.split(',').map(e => e.trim()) || [];
      const compressionResult = await this.compressionMiddleware.compress(buffer, acceptedEncodings);
      
      if (compressionResult.algorithm !== 'none') {
        responseData = compressionResult.compressed;
        contentEncoding = compressionResult.algorithm;
      }
    }

    const headers: Record<string, string> = {
      ...cacheHeaders,
      'Content-Length': responseData.length.toString(),
      'Content-Type': 'application/json; charset=utf-8',
    };

    if (contentEncoding) {
      headers['Content-Encoding'] = contentEncoding;
    }

    return {
      data: responseData,
      headers,
      statusCode: 200,
    };
  }

  /**
   * Get compression statistics
   */
  static getStats(): CompressionStats | undefined {
    return this.compressionMiddleware?.getStats();
  }
}
