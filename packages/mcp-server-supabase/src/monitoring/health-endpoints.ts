import { Request, Response } from 'express';
import { health<PERSON><PERSON><PERSON>, PerformanceMonitor } from '../utils/middleware.js';
import { configHealthMonitor } from '../config/config-health.js';
import { contextLogger } from '../utils/logger.js';
import { PerformanceManager } from '../performance/index.js';

/**
 * Health check endpoint handlers
 */
export class HealthEndpoints {
  private logger = contextLogger.child({ component: 'health-endpoints' });

  /**
   * Basic health check endpoint
   * GET /health
   */
  async basicHealth(req: Request, res: Response): Promise<void> {
    try {
      const health = await healthChecker.getHealth();
      
      const statusCode = health.status === 'healthy' ? 200 : 
                        health.status === 'degraded' ? 200 : 503;
      
      res.status(statusCode).json({
        status: health.status,
        timestamp: health.timestamp,
        uptime: health.uptime,
        version: health.version,
      });
    } catch (error) {
      this.logger.error('Health check failed', error instanceof Error ? error : undefined);
      res.status(503).json({
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  /**
   * Detailed health check endpoint
   * GET /health/detailed
   */
  async detailedHealth(req: Request, res: Response): Promise<void> {
    try {
      const health = await healthChecker.getHealth();
      const configHealth = await configHealthMonitor.checkHealth();
      const performanceMetrics = await performanceManager.getMetrics();
      
      const statusCode = health.status === 'healthy' ? 200 : 
                        health.status === 'degraded' ? 200 : 503;
      
      res.status(statusCode).json({
        status: health.status,
        timestamp: health.timestamp,
        uptime: health.uptime,
        version: health.version,
        checks: health.checks,
        metrics: health.metrics,
        configuration: {
          healthy: configHealth.healthy,
          instanceCount: configHealth.instanceCount,
          issues: configHealth.issues,
          performanceMetrics: configHealth.performanceMetrics,
        },
        performance: performanceMetrics,
      });
    } catch (error) {
      this.logger.error('Detailed health check failed', error instanceof Error ? error : undefined);
      res.status(503).json({
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  /**
   * Readiness probe endpoint
   * GET /health/ready
   */
  async readiness(req: Request, res: Response): Promise<void> {
    try {
      const health = await healthChecker.getHealth();
      
      // Check if all critical systems are operational
      const criticalChecks = ['database'];
      const criticalFailures = criticalChecks.filter(check => 
        health.checks[check]?.status === 'fail'
      );
      
      if (criticalFailures.length > 0) {
        res.status(503).json({
          ready: false,
          timestamp: new Date().toISOString(),
          failedChecks: criticalFailures,
        });
        return;
      }
      
      res.status(200).json({
        ready: true,
        timestamp: new Date().toISOString(),
        checks: health.checks,
      });
    } catch (error) {
      this.logger.error('Readiness check failed', error instanceof Error ? error : undefined);
      res.status(503).json({
        ready: false,
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  /**
   * Liveness probe endpoint
   * GET /health/live
   */
  async liveness(req: Request, res: Response): Promise<void> {
    try {
      // Simple liveness check - just verify the process is running
      const uptime = Date.now() - (process.uptime() * 1000);
      
      res.status(200).json({
        alive: true,
        timestamp: new Date().toISOString(),
        uptime,
        pid: process.pid,
      });
    } catch (error) {
      this.logger.error('Liveness check failed', error instanceof Error ? error : undefined);
      res.status(503).json({
        alive: false,
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  /**
   * Startup probe endpoint
   * GET /health/startup
   */
  async startup(req: Request, res: Response): Promise<void> {
    try {
      const health = await healthChecker.getHealth();
      const configHealth = configHealthMonitor.getLastHealthCheck();
      
      // Check if all startup dependencies are ready
      const startupReady = health.status !== 'unhealthy' && 
                          configHealth?.healthy !== false;
      
      if (!startupReady) {
        res.status(503).json({
          started: false,
          timestamp: new Date().toISOString(),
          status: health.status,
          configHealthy: configHealth?.healthy,
        });
        return;
      }
      
      res.status(200).json({
        started: true,
        timestamp: new Date().toISOString(),
        status: health.status,
        uptime: health.uptime,
      });
    } catch (error) {
      this.logger.error('Startup check failed', error instanceof Error ? error : undefined);
      res.status(503).json({
        started: false,
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
}

/**
 * Global health endpoints instance
 */
export const healthEndpoints = new HealthEndpoints();

/**
 * Express router setup function
 */
export function setupHealthRoutes(app: any): void {
  // Basic health check
  app.get('/health', (req: Request, res: Response) => 
    healthEndpoints.basicHealth(req, res)
  );
  
  // Detailed health check
  app.get('/health/detailed', (req: Request, res: Response) => 
    healthEndpoints.detailedHealth(req, res)
  );
  
  // Kubernetes-style probes
  app.get('/health/ready', (req: Request, res: Response) => 
    healthEndpoints.readiness(req, res)
  );
  
  app.get('/health/live', (req: Request, res: Response) => 
    healthEndpoints.liveness(req, res)
  );
  
  app.get('/health/startup', (req: Request, res: Response) => 
    healthEndpoints.startup(req, res)
  );
}
