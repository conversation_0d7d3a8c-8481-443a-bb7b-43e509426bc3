import { Request, Response } from 'express';
import { PerformanceMonitor } from '../utils/middleware.js';
import { configHealthMonitor } from '../config/config-health.js';
import { contextLogger } from '../utils/logger.js';
import { PerformanceManager } from '../performance/index.js';
import { EventSystem } from '../notifications/event-system.js';

/**
 * Metrics collection endpoint handlers
 */
export class MetricsEndpoints {
  private logger = contextLogger.child({ component: 'metrics-endpoints' });

  /**
   * Get all metrics in Prometheus format
   * GET /metrics
   */
  async prometheusMetrics(req: Request, res: Response): Promise<void> {
    try {
      const metrics = await this.collectAllMetrics();
      const prometheusFormat = this.formatPrometheusMetrics(metrics);
      
      res.setHeader('Content-Type', 'text/plain; version=0.0.4; charset=utf-8');
      res.status(200).send(prometheusFormat);
    } catch (error) {
      this.logger.error('Failed to collect Prometheus metrics', error instanceof Error ? error : undefined);
      res.status(500).json({
        error: 'Failed to collect metrics',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  /**
   * Get metrics in JSON format
   * GET /metrics/json
   */
  async jsonMetrics(req: Request, res: Response): Promise<void> {
    try {
      const metrics = await this.collectAllMetrics();
      
      res.status(200).json({
        timestamp: new Date().toISOString(),
        metrics,
      });
    } catch (error) {
      this.logger.error('Failed to collect JSON metrics', error instanceof Error ? error : undefined);
      res.status(500).json({
        error: 'Failed to collect metrics',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  /**
   * Get performance metrics
   * GET /metrics/performance
   */
  async performanceMetrics(req: Request, res: Response): Promise<void> {
    try {
      const performanceMetrics = await performanceManager.getMetrics();
      const utilityMetrics = PerformanceMonitor.getMetrics();
      
      res.status(200).json({
        timestamp: new Date().toISOString(),
        performance: performanceMetrics,
        operations: utilityMetrics,
      });
    } catch (error) {
      this.logger.error('Failed to collect performance metrics', error instanceof Error ? error : undefined);
      res.status(500).json({
        error: 'Failed to collect performance metrics',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  /**
   * Get configuration metrics
   * GET /metrics/config
   */
  async configMetrics(req: Request, res: Response): Promise<void> {
    try {
      const configMetrics = configHealthMonitor.getMetrics();
      const healthStatus = configHealthMonitor.getLastHealthCheck();
      
      res.status(200).json({
        timestamp: new Date().toISOString(),
        configuration: configMetrics,
        health: healthStatus,
      });
    } catch (error) {
      this.logger.error('Failed to collect configuration metrics', error instanceof Error ? error : undefined);
      res.status(500).json({
        error: 'Failed to collect configuration metrics',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  /**
   * Get event system metrics
   * GET /metrics/events
   */
  async eventMetrics(req: Request, res: Response): Promise<void> {
    try {
      const eventMetrics = eventSystem.getMetrics();
      const eventHistory = eventSystem.getEventHistory(50); // Last 50 events
      
      res.status(200).json({
        timestamp: new Date().toISOString(),
        metrics: eventMetrics,
        recentEvents: eventHistory.map(event => ({
          id: event.id,
          type: event.type,
          source: event.source,
          timestamp: event.timestamp,
          correlationId: event.correlationId,
        })),
      });
    } catch (error) {
      this.logger.error('Failed to collect event metrics', error instanceof Error ? error : undefined);
      res.status(500).json({
        error: 'Failed to collect event metrics',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  /**
   * Get system resource metrics
   * GET /metrics/system
   */
  async systemMetrics(req: Request, res: Response): Promise<void> {
    try {
      const memoryUsage = process.memoryUsage();
      const cpuUsage = process.cpuUsage();
      const uptime = process.uptime();
      
      res.status(200).json({
        timestamp: new Date().toISOString(),
        system: {
          memory: {
            rss: memoryUsage.rss,
            heapTotal: memoryUsage.heapTotal,
            heapUsed: memoryUsage.heapUsed,
            external: memoryUsage.external,
            arrayBuffers: memoryUsage.arrayBuffers,
          },
          cpu: {
            user: cpuUsage.user,
            system: cpuUsage.system,
          },
          process: {
            uptime,
            pid: process.pid,
            version: process.version,
            platform: process.platform,
            arch: process.arch,
          },
        },
      });
    } catch (error) {
      this.logger.error('Failed to collect system metrics', error instanceof Error ? error : undefined);
      res.status(500).json({
        error: 'Failed to collect system metrics',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  /**
   * Collect all metrics from various sources
   */
  private async collectAllMetrics(): Promise<Record<string, any>> {
    const [
      performanceMetrics,
      utilityMetrics,
      configMetrics,
      eventMetrics,
    ] = await Promise.all([
      performanceManager.getMetrics(),
      Promise.resolve(PerformanceMonitor.getMetrics()),
      Promise.resolve(configHealthMonitor.getMetrics()),
      Promise.resolve(eventSystem.getMetrics()),
    ]);

    const memoryUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();

    return {
      performance: performanceMetrics,
      operations: utilityMetrics,
      configuration: configMetrics,
      events: eventMetrics,
      system: {
        memory: memoryUsage,
        cpu: cpuUsage,
        uptime: process.uptime(),
        pid: process.pid,
      },
    };
  }

  /**
   * Format metrics in Prometheus format
   */
  private formatPrometheusMetrics(metrics: Record<string, any>): string {
    const lines: string[] = [];
    
    // Add metadata
    lines.push('# HELP mcp_server_info MCP Server information');
    lines.push('# TYPE mcp_server_info gauge');
    lines.push(`mcp_server_info{version="${process.env.npm_package_version || 'unknown'}"} 1`);
    
    // System metrics
    lines.push('# HELP mcp_server_memory_usage_bytes Memory usage in bytes');
    lines.push('# TYPE mcp_server_memory_usage_bytes gauge');
    lines.push(`mcp_server_memory_usage_bytes{type="rss"} ${metrics.system.memory.rss}`);
    lines.push(`mcp_server_memory_usage_bytes{type="heap_total"} ${metrics.system.memory.heapTotal}`);
    lines.push(`mcp_server_memory_usage_bytes{type="heap_used"} ${metrics.system.memory.heapUsed}`);
    
    lines.push('# HELP mcp_server_uptime_seconds Server uptime in seconds');
    lines.push('# TYPE mcp_server_uptime_seconds counter');
    lines.push(`mcp_server_uptime_seconds ${metrics.system.uptime}`);
    
    // Performance metrics
    if (metrics.performance?.cache?.stats) {
      const cacheStats = metrics.performance.cache.stats;
      lines.push('# HELP mcp_server_cache_hit_rate Cache hit rate');
      lines.push('# TYPE mcp_server_cache_hit_rate gauge');
      lines.push(`mcp_server_cache_hit_rate ${cacheStats.hitRate || 0}`);
      
      lines.push('# HELP mcp_server_cache_operations_total Total cache operations');
      lines.push('# TYPE mcp_server_cache_operations_total counter');
      lines.push(`mcp_server_cache_operations_total{type="hits"} ${cacheStats.hits || 0}`);
      lines.push(`mcp_server_cache_operations_total{type="misses"} ${cacheStats.misses || 0}`);
    }
    
    // Operation metrics
    for (const [operation, stats] of Object.entries(metrics.operations || {})) {
      if (typeof stats === 'object' && stats !== null) {
        const operationStats = stats as any;
        lines.push(`# HELP mcp_server_operation_duration_seconds Operation duration for ${operation}`);
        lines.push(`# TYPE mcp_server_operation_duration_seconds histogram`);
        lines.push(`mcp_server_operation_duration_seconds{operation="${operation}",quantile="avg"} ${operationStats.averageDuration || 0}`);
        lines.push(`mcp_server_operation_duration_seconds{operation="${operation}",quantile="min"} ${operationStats.minDuration || 0}`);
        lines.push(`mcp_server_operation_duration_seconds{operation="${operation}",quantile="max"} ${operationStats.maxDuration || 0}`);
        
        lines.push(`# HELP mcp_server_operation_total Total operations for ${operation}`);
        lines.push(`# TYPE mcp_server_operation_total counter`);
        lines.push(`mcp_server_operation_total{operation="${operation}"} ${operationStats.count || 0}`);
        
        lines.push(`# HELP mcp_server_operation_errors_total Total errors for ${operation}`);
        lines.push(`# TYPE mcp_server_operation_errors_total counter`);
        lines.push(`mcp_server_operation_errors_total{operation="${operation}"} ${operationStats.errors || 0}`);
      }
    }
    
    return lines.join('\n') + '\n';
  }
}

/**
 * Global metrics endpoints instance
 */
export const metricsEndpoints = new MetricsEndpoints();

/**
 * Express router setup function
 */
export function setupMetricsRoutes(app: any): void {
  // Prometheus metrics
  app.get('/metrics', (req: Request, res: Response) => 
    metricsEndpoints.prometheusMetrics(req, res)
  );
  
  // JSON metrics
  app.get('/metrics/json', (req: Request, res: Response) => 
    metricsEndpoints.jsonMetrics(req, res)
  );
  
  // Specific metric categories
  app.get('/metrics/performance', (req: Request, res: Response) => 
    metricsEndpoints.performanceMetrics(req, res)
  );
  
  app.get('/metrics/config', (req: Request, res: Response) => 
    metricsEndpoints.configMetrics(req, res)
  );
  
  app.get('/metrics/events', (req: Request, res: Response) => 
    metricsEndpoints.eventMetrics(req, res)
  );
  
  app.get('/metrics/system', (req: Request, res: Response) => 
    metricsEndpoints.systemMetrics(req, res)
  );
}
