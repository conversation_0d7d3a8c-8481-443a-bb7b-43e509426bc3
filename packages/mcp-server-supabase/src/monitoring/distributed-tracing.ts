import { randomUUID } from 'crypto';
import { contextLogger } from '../utils/logger.js';
import { AsyncLocalStorage } from 'async_hooks';

/**
 * Trace context information
 */
export interface TraceContext {
  traceId: string;
  spanId: string;
  parentSpanId?: string;
  operationName: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  tags: Record<string, string | number | boolean>;
  logs: Array<{
    timestamp: number;
    level: 'debug' | 'info' | 'warn' | 'error';
    message: string;
    fields?: Record<string, any>;
  }>;
  status: 'ok' | 'error' | 'timeout';
  error?: Error;
}

/**
 * Span represents a single operation within a trace
 */
export class Span {
  private context: TraceContext;
  private logger = contextLogger.child({ component: 'tracing' });

  constructor(
    operationName: string,
    traceId?: string,
    parentSpanId?: string
  ) {
    this.context = {
      traceId: traceId || randomUUID(),
      spanId: randomUUID(),
      parentSpanId,
      operationName,
      startTime: Date.now(),
      tags: {},
      logs: [],
      status: 'ok',
    };
  }

  /**
   * Add a tag to the span
   */
  setTag(key: string, value: string | number | boolean): this {
    this.context.tags[key] = value;
    return this;
  }

  /**
   * Add multiple tags to the span
   */
  setTags(tags: Record<string, string | number | boolean>): this {
    Object.assign(this.context.tags, tags);
    return this;
  }

  /**
   * Log an event in the span
   */
  log(level: 'debug' | 'info' | 'warn' | 'error', message: string, fields?: Record<string, any>): this {
    this.context.logs.push({
      timestamp: Date.now(),
      level,
      message,
      fields,
    });
    return this;
  }

  /**
   * Mark span as having an error
   */
  setError(error: Error): this {
    this.context.status = 'error';
    this.context.error = error;
    this.setTag('error', true);
    this.setTag('error.message', error.message);
    this.setTag('error.name', error.name);
    this.log('error', error.message, { stack: error.stack });
    return this;
  }

  /**
   * Finish the span
   */
  finish(): TraceContext {
    this.context.endTime = Date.now();
    this.context.duration = this.context.endTime - this.context.startTime;
    
    // Report the span to the tracer
    distributedTracer.reportSpan(this.context);
    
    return this.context;
  }

  /**
   * Get the trace context
   */
  getContext(): TraceContext {
    return { ...this.context };
  }

  /**
   * Create a child span
   */
  createChild(operationName: string): Span {
    return new Span(operationName, this.context.traceId, this.context.spanId);
  }
}

/**
 * Distributed tracer for managing traces and spans
 */
export class DistributedTracer {
  private spans = new Map<string, TraceContext>();
  private traces = new Map<string, TraceContext[]>();
  private asyncStorage = new AsyncLocalStorage<Span>();
  private logger = contextLogger.child({ component: 'distributed-tracer' });
  private maxSpansPerTrace = 1000;
  private maxTraceAge = 24 * 60 * 60 * 1000; // 24 hours

  /**
   * Start a new trace
   */
  startTrace(operationName: string, traceId?: string): Span {
    const span = new Span(operationName, traceId);
    this.logger.debug('Started new trace', {
      traceId: span.getContext().traceId,
      operationName,
    });
    return span;
  }

  /**
   * Start a new span within the current trace context
   */
  startSpan(operationName: string): Span {
    const currentSpan = this.getCurrentSpan();
    
    if (currentSpan) {
      return currentSpan.createChild(operationName);
    } else {
      // No current span, start a new trace
      return this.startTrace(operationName);
    }
  }

  /**
   * Get the current active span
   */
  getCurrentSpan(): Span | undefined {
    return this.asyncStorage.getStore();
  }

  /**
   * Run a function within a span context
   */
  async runWithSpan<T>(span: Span, fn: () => Promise<T>): Promise<T> {
    return this.asyncStorage.run(span, async () => {
      try {
        const result = await fn();
        span.setTag('success', true);
        return result;
      } catch (error) {
        span.setError(error instanceof Error ? error : new Error(String(error)));
        throw error;
      } finally {
        span.finish();
      }
    });
  }

  /**
   * Trace a function execution
   */
  async trace<T>(operationName: string, fn: () => Promise<T>): Promise<T> {
    const span = this.startSpan(operationName);
    return this.runWithSpan(span, fn);
  }

  /**
   * Report a completed span
   */
  reportSpan(context: TraceContext): void {
    // Store the span
    this.spans.set(context.spanId, context);
    
    // Add to trace
    if (!this.traces.has(context.traceId)) {
      this.traces.set(context.traceId, []);
    }
    
    const traceSpans = this.traces.get(context.traceId)!;
    traceSpans.push(context);
    
    // Limit spans per trace
    if (traceSpans.length > this.maxSpansPerTrace) {
      traceSpans.shift(); // Remove oldest span
    }
    
    this.logger.debug('Span reported', {
      traceId: context.traceId,
      spanId: context.spanId,
      operationName: context.operationName,
      duration: context.duration,
      status: context.status,
    });
    
    // Clean up old traces periodically
    this.cleanupOldTraces();
  }

  /**
   * Get a trace by ID
   */
  getTrace(traceId: string): TraceContext[] | undefined {
    return this.traces.get(traceId);
  }

  /**
   * Get a span by ID
   */
  getSpan(spanId: string): TraceContext | undefined {
    return this.spans.get(spanId);
  }

  /**
   * Get all active traces
   */
  getActiveTraces(): Map<string, TraceContext[]> {
    return new Map(this.traces);
  }

  /**
   * Get tracing statistics
   */
  getStats(): {
    totalTraces: number;
    totalSpans: number;
    averageSpansPerTrace: number;
    errorRate: number;
  } {
    const totalTraces = this.traces.size;
    const totalSpans = this.spans.size;
    const errorSpans = Array.from(this.spans.values()).filter(span => span.status === 'error').length;
    
    return {
      totalTraces,
      totalSpans,
      averageSpansPerTrace: totalTraces > 0 ? totalSpans / totalTraces : 0,
      errorRate: totalSpans > 0 ? errorSpans / totalSpans : 0,
    };
  }

  /**
   * Extract trace context from headers
   */
  extractTraceContext(headers: Record<string, string>): { traceId?: string; spanId?: string } {
    // Support common tracing headers
    const traceId = headers['x-trace-id'] || 
                   headers['x-request-id'] || 
                   headers['traceparent']?.split('-')[1];
    
    const spanId = headers['x-span-id'] || 
                  headers['traceparent']?.split('-')[2];
    
    return { traceId, spanId };
  }

  /**
   * Inject trace context into headers
   */
  injectTraceContext(span: Span, headers: Record<string, string>): void {
    const context = span.getContext();
    headers['x-trace-id'] = context.traceId;
    headers['x-span-id'] = context.spanId;
    
    // W3C Trace Context format
    headers['traceparent'] = `00-${context.traceId}-${context.spanId}-01`;
  }

  /**
   * Clean up old traces to prevent memory leaks
   */
  private cleanupOldTraces(): void {
    const now = Date.now();
    const cutoff = now - this.maxTraceAge;
    
    for (const [traceId, spans] of this.traces.entries()) {
      const oldestSpan = spans.reduce((oldest, span) => 
        span.startTime < oldest.startTime ? span : oldest
      );
      
      if (oldestSpan.startTime < cutoff) {
        // Remove all spans for this trace
        spans.forEach(span => this.spans.delete(span.spanId));
        this.traces.delete(traceId);
        
        this.logger.debug('Cleaned up old trace', { traceId, age: now - oldestSpan.startTime });
      }
    }
  }

  /**
   * Clear all traces and spans
   */
  clear(): void {
    this.spans.clear();
    this.traces.clear();
    this.logger.info('Cleared all traces and spans');
  }
}

/**
 * Global distributed tracer instance
 */
export const distributedTracer = new DistributedTracer();

/**
 * Decorator for tracing method calls
 */
export function traced(operationName?: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    const traceName = operationName || `${target.constructor.name}.${propertyKey}`;
    
    descriptor.value = async function (...args: any[]) {
      return distributedTracer.trace(traceName, async () => {
        return originalMethod.apply(this, args);
      });
    };
    
    return descriptor;
  };
}

/**
 * Express middleware for request tracing
 */
export function tracingMiddleware() {
  return (req: any, res: any, next: any) => {
    const { traceId, spanId } = distributedTracer.extractTraceContext(req.headers);
    const span = distributedTracer.startTrace(`${req.method} ${req.path}`, traceId);
    
    span.setTags({
      'http.method': req.method,
      'http.url': req.url,
      'http.path': req.path,
      'user.agent': req.get('User-Agent') || 'unknown',
    });
    
    // Add trace context to request
    req.traceContext = span.getContext();
    
    // Inject trace context into response headers
    distributedTracer.injectTraceContext(span, res.getHeaders());
    
    // Finish span when response ends
    res.on('finish', () => {
      span.setTags({
        'http.status_code': res.statusCode,
        'http.response_size': res.get('Content-Length') || 0,
      });
      
      if (res.statusCode >= 400) {
        span.setTag('error', true);
        span.log('error', `HTTP ${res.statusCode}`, { statusCode: res.statusCode });
      }
      
      span.finish();
    });
    
    next();
  };
}
