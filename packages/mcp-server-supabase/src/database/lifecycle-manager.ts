import { EventEmitter } from 'events';
import { contextLogger } from '../utils/logger.js';
import { ConfigurationError, ConnectionError, ErrorClassifier } from '../utils/errors.js';
import { 
  SupabaseConnectionConfigManager, 
  supabaseConnectionConfig 
} from '../config/supabase-connection-config.js';
import { 
  SupabaseConnectionPool, 
  getSupabaseConnectionPool, 
  destroySupabaseConnectionPool 
} from '../config/supabase-connection-pool.js';
import { 
  DatabaseQueryWrapper, 
  createDatabaseQueryWrapper 
} from './query-wrapper.js';

/**
 * Lifecycle states for the database connection system
 */
export enum LifecycleState {
  UNINITIALIZED = 'uninitialized',
  INITIALIZING = 'initializing',
  READY = 'ready',
  DEGRADED = 'degraded',
  SHUTTING_DOWN = 'shutting_down',
  SHUTDOWN = 'shutdown',
  ERROR = 'error',
}

/**
 * Health check status
 */
export interface HealthStatus {
  healthy: boolean;
  state: LifecycleState;
  lastCheck: Date;
  error?: any;
  details: {
    configLoaded: boolean;
    poolInitialized: boolean;
    connectionCount: number;
    healthyConnections: number;
    uptime: number;
  };
}

/**
 * Lifecycle event types
 */
export interface LifecycleEvents {
  'state-changed': (oldState: LifecycleState, newState: LifecycleState) => void;
  'initialized': () => void;
  'ready': () => void;
  'degraded': (reason: string) => void;
  'error': (error: any) => void;
  'shutdown': () => void;
  'health-check': (status: HealthStatus) => void;
}

/**
 * Database connection lifecycle manager
 */
export class DatabaseLifecycleManager extends EventEmitter {
  private readonly logger = contextLogger.child({ component: 'DatabaseLifecycleManager' });
  
  private state = LifecycleState.UNINITIALIZED;
  private configManager?: SupabaseConnectionConfigManager;
  private pool?: SupabaseConnectionPool;
  private queryWrapper?: DatabaseQueryWrapper;
  private healthCheckTimer?: NodeJS.Timeout;
  private startTime?: Date;
  private lastHealthCheck?: Date;
  private shutdownPromise?: Promise<void>;
  private isShuttingDown = false;

  constructor() {
    super();
    this.setupErrorHandlers();
  }

  /**
   * Initialize the database system
   */
  async initialize(): Promise<void> {
    if (this.state !== LifecycleState.UNINITIALIZED) {
      throw new ConfigurationError(`Cannot initialize from state: ${this.state}`);
    }

    this.setState(LifecycleState.INITIALIZING);
    this.startTime = new Date();

    try {
      this.logger.info('Initializing database lifecycle manager');

      // Step 1: Load configuration
      this.configManager = supabaseConnectionConfig;
      const config = await this.configManager.loadFromEnv();
      this.logger.info('Configuration loaded successfully');

      // Step 2: Initialize connection pool
      this.pool = getSupabaseConnectionPool(config);
      await this.pool.initialize();
      this.logger.info('Connection pool initialized successfully');

      // Step 3: Create query wrapper
      this.queryWrapper = createDatabaseQueryWrapper(this.pool);
      this.logger.info('Query wrapper created successfully');

      // Step 4: Perform initial health check
      await this.performHealthCheck();

      // Step 5: Start health monitoring
      if (config.SUPABASE_HEALTH_CHECK_ENABLED) {
        this.startHealthMonitoring(config.SUPABASE_HEALTH_CHECK_INTERVAL);
      }

      // Step 6: Set up pool event listeners
      this.setupPoolEventListeners();

      this.setState(LifecycleState.READY);
      this.emit('initialized');
      this.emit('ready');

      this.logger.info('Database lifecycle manager initialized successfully', {
        state: this.state,
        uptime: this.getUptime(),
      });
    } catch (error) {
      this.setState(LifecycleState.ERROR);
      this.emit('error', error);
      
      this.logger.error('Failed to initialize database lifecycle manager', 
        error instanceof Error ? error : undefined);
      
      // Cleanup on failure
      await this.cleanup();
      throw error;
    }
  }

  /**
   * Get the current lifecycle state
   */
  getState(): LifecycleState {
    return this.state;
  }

  /**
   * Check if the system is ready for operations
   */
  isReady(): boolean {
    return this.state === LifecycleState.READY;
  }

  /**
   * Check if the system is healthy
   */
  isHealthy(): boolean {
    return this.state === LifecycleState.READY || this.state === LifecycleState.DEGRADED;
  }

  /**
   * Get the query wrapper (only available when ready)
   */
  getQueryWrapper(): DatabaseQueryWrapper {
    if (!this.queryWrapper || !this.isHealthy()) {
      throw new ConnectionError('Database system is not ready for operations');
    }
    return this.queryWrapper;
  }

  /**
   * Get the connection pool (only available when ready)
   */
  getConnectionPool(): SupabaseConnectionPool {
    if (!this.pool || !this.isHealthy()) {
      throw new ConnectionError('Database system is not ready for operations');
    }
    return this.pool;
  }

  /**
   * Get the configuration manager
   */
  getConfigManager(): SupabaseConnectionConfigManager {
    if (!this.configManager) {
      throw new ConfigurationError('Configuration manager not available');
    }
    return this.configManager;
  }

  /**
   * Perform a health check
   */
  async performHealthCheck(): Promise<HealthStatus> {
    this.lastHealthCheck = new Date();
    
    const status: HealthStatus = {
      healthy: false,
      state: this.state,
      lastCheck: this.lastHealthCheck,
      details: {
        configLoaded: !!this.configManager,
        poolInitialized: !!this.pool,
        connectionCount: 0,
        healthyConnections: 0,
        uptime: this.getUptime(),
      },
    };

    try {
      // Check if basic components are available
      if (!this.configManager || !this.pool || !this.queryWrapper) {
        status.error = 'Core components not initialized';
        return status;
      }

      // Check pool statistics
      const poolStats = this.pool.getStats();
      status.details.connectionCount = poolStats.totalConnections;
      status.details.healthyConnections = poolStats.healthyConnections;

      // Perform a simple connectivity test
      await this.performConnectivityTest();

      // Determine overall health
      status.healthy = poolStats.healthyConnections > 0;
      
      if (!status.healthy && this.state === LifecycleState.READY) {
        this.setState(LifecycleState.DEGRADED);
        this.emit('degraded', 'No healthy connections available');
      } else if (status.healthy && this.state === LifecycleState.DEGRADED) {
        this.setState(LifecycleState.READY);
        this.emit('ready');
      }

    } catch (error) {
      status.healthy = false;
      status.error = ErrorClassifier.createDatabaseError(error);
      
      if (this.state === LifecycleState.READY) {
        this.setState(LifecycleState.DEGRADED);
        this.emit('degraded', `Health check failed: ${error}`);
      }
    }

    this.emit('health-check', status);
    return status;
  }

  /**
   * Get current health status
   */
  async getHealthStatus(): Promise<HealthStatus> {
    if (!this.lastHealthCheck || Date.now() - this.lastHealthCheck.getTime() > 30000) {
      return await this.performHealthCheck();
    }

    // Return cached status if recent
    return {
      healthy: this.isHealthy(),
      state: this.state,
      lastCheck: this.lastHealthCheck,
      details: {
        configLoaded: !!this.configManager,
        poolInitialized: !!this.pool,
        connectionCount: this.pool?.getStats().totalConnections || 0,
        healthyConnections: this.pool?.getStats().healthyConnections || 0,
        uptime: this.getUptime(),
      },
    };
  }

  /**
   * Gracefully shutdown the database system
   */
  async shutdown(): Promise<void> {
    if (this.isShuttingDown) {
      return this.shutdownPromise;
    }

    this.isShuttingDown = true;
    this.setState(LifecycleState.SHUTTING_DOWN);

    this.shutdownPromise = this.performShutdown();
    return this.shutdownPromise;
  }

  /**
   * Get system uptime in milliseconds
   */
  getUptime(): number {
    if (!this.startTime) return 0;
    return Date.now() - this.startTime.getTime();
  }

  /**
   * Get system statistics
   */
  getStatistics(): {
    state: LifecycleState;
    uptime: number;
    lastHealthCheck?: Date;
    poolStats?: any;
    configMasked?: any;
  } {
    return {
      state: this.state,
      uptime: this.getUptime(),
      lastHealthCheck: this.lastHealthCheck,
      poolStats: this.pool?.getStats(),
      configMasked: this.configManager?.getMaskedConfig(),
    };
  }

  /**
   * Perform the actual shutdown process
   */
  private async performShutdown(): Promise<void> {
    this.logger.info('Starting database lifecycle manager shutdown');

    try {
      // Stop health monitoring
      if (this.healthCheckTimer) {
        clearInterval(this.healthCheckTimer);
        this.healthCheckTimer = undefined;
      }

      // Cleanup components
      await this.cleanup();

      this.setState(LifecycleState.SHUTDOWN);
      this.emit('shutdown');

      this.logger.info('Database lifecycle manager shutdown completed', {
        uptime: this.getUptime(),
      });
    } catch (error) {
      this.logger.error('Error during shutdown', error instanceof Error ? error : undefined);
      this.setState(LifecycleState.ERROR);
      throw error;
    }
  }

  /**
   * Cleanup resources
   */
  private async cleanup(): Promise<void> {
    try {
      // Shutdown connection pool
      if (this.pool) {
        await destroySupabaseConnectionPool();
        this.pool = undefined;
      }

      // Clear references
      this.queryWrapper = undefined;
      this.configManager = undefined;

      // Remove all listeners
      this.removeAllListeners();
    } catch (error) {
      this.logger.error('Error during cleanup', error instanceof Error ? error : undefined);
    }
  }

  /**
   * Set the lifecycle state and emit change event
   */
  private setState(newState: LifecycleState): void {
    const oldState = this.state;
    this.state = newState;
    
    this.logger.debug('Lifecycle state changed', { oldState, newState });
    this.emit('state-changed', oldState, newState);
  }

  /**
   * Start health monitoring
   */
  private startHealthMonitoring(interval: number): void {
    this.healthCheckTimer = setInterval(async () => {
      try {
        await this.performHealthCheck();
      } catch (error) {
        this.logger.error('Health check failed', error instanceof Error ? error : undefined);
      }
    }, interval);

    this.logger.info('Health monitoring started', { interval });
  }

  /**
   * Setup pool event listeners
   */
  private setupPoolEventListeners(): void {
    if (!this.pool) return;

    this.pool.on('connectionCreated', (connection) => {
      this.logger.debug('Pool connection created', {
        connectionId: connection.id,
        type: connection.type,
      });
    });

    this.pool.on('connectionDestroyed', (connection) => {
      this.logger.debug('Pool connection destroyed', {
        connectionId: connection.id,
        type: connection.type,
      });
    });

    this.pool.on('connectionReleased', (connection) => {
      this.logger.debug('Pool connection released', {
        connectionId: connection.id,
        type: connection.type,
      });
    });
  }

  /**
   * Setup error handlers
   */
  private setupErrorHandlers(): void {
    this.on('error', (error) => {
      this.logger.error('Lifecycle manager error', error instanceof Error ? error : undefined);
    });

    // Handle uncaught errors
    process.on('uncaughtException', (error) => {
      this.logger.error('Uncaught exception in lifecycle manager', error);
      this.setState(LifecycleState.ERROR);
    });

    process.on('unhandledRejection', (reason) => {
      this.logger.error('Unhandled rejection in lifecycle manager', reason instanceof Error ? reason : undefined);
    });
  }

  /**
   * Perform a simple connectivity test
   */
  private async performConnectivityTest(): Promise<void> {
    if (!this.queryWrapper) {
      throw new ConnectionError('Query wrapper not available');
    }

    // Simple test: try to execute a basic query
    try {
      await this.queryWrapper.executeSql('SELECT 1 as test', {
        timeout: 5000,
        connectionType: 'anon',
      });
    } catch (error) {
      throw new ConnectionError(`Connectivity test failed: ${error}`);
    }
  }
}

/**
 * Singleton instance of the database lifecycle manager
 */
let lifecycleManagerInstance: DatabaseLifecycleManager | null = null;

/**
 * Get or create the singleton lifecycle manager
 */
export function getDatabaseLifecycleManager(): DatabaseLifecycleManager {
  if (!lifecycleManagerInstance) {
    lifecycleManagerInstance = new DatabaseLifecycleManager();
  }
  return lifecycleManagerInstance;
}

/**
 * Initialize the database system
 */
export async function initializeDatabaseSystem(): Promise<DatabaseLifecycleManager> {
  const manager = getDatabaseLifecycleManager();
  
  if (!manager.isReady() && manager.getState() === LifecycleState.UNINITIALIZED) {
    await manager.initialize();
  }
  
  return manager;
}

/**
 * Shutdown the database system
 */
export async function shutdownDatabaseSystem(): Promise<void> {
  if (lifecycleManagerInstance) {
    await lifecycleManagerInstance.shutdown();
    lifecycleManagerInstance = null;
  }
}

/**
 * Get a ready-to-use query wrapper
 */
export async function getQueryWrapper(): Promise<DatabaseQueryWrapper> {
  const manager = await initializeDatabaseSystem();
  return manager.getQueryWrapper();
}

/**
 * Get a ready-to-use connection pool
 */
export async function getConnectionPool(): Promise<SupabaseConnectionPool> {
  const manager = await initializeDatabaseSystem();
  return manager.getConnectionPool();
}

/**
 * Check if the database system is healthy
 */
export async function isDatabaseHealthy(): Promise<boolean> {
  try {
    const manager = getDatabaseLifecycleManager();
    if (manager.getState() === LifecycleState.UNINITIALIZED) {
      return false;
    }
    
    const status = await manager.getHealthStatus();
    return status.healthy;
  } catch {
    return false;
  }
}