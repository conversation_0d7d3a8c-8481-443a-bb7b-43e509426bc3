import { tool } from '@supabase/mcp-utils';
import { z } from 'zod';
import type { SupabasePlatform } from '../platform/types.js';

export type LocalDevelopmentToolsOptions = {
  platform: SupabasePlatform;
  projectId?: string;
};

export function getLocalDevelopmentTools({
  platform,
  projectId = 'local',
}: LocalDevelopmentToolsOptions) {
  return {
    check_local_connection: tool({
      description: 'Verifies connection to the local Supabase instance',
      parameters: z.object({}),
      execute: async () => {
        try {
          // Try to execute a simple query to verify connection
          await platform.executeSql(projectId, {
            query: 'SELECT 1 as test',
            read_only: true,
          });
          
          return {
            connected: true,
            message: 'Successfully connected to local Supabase instance',
            url: await platform.getProjectUrl(projectId),
          };
        } catch (error) {
          return {
            connected: false,
            message: `Failed to connect: ${error}`,
            troubleshooting: [
              'Check if Supabase is running: supabase status',
              'Start Supabase if not running: supabase start',
              'Verify environment variables are set correctly',
              'Check if the database is accessible'
            ],
          };
        }
      },
    }),

    get_local_config: tool({
      description: 'Display current local Supabase configuration',
      parameters: z.object({}),
      execute: async () => {
        return {
          url: await platform.getProjectUrl(projectId),
          anonKey: await platform.getAnonKey(projectId),
          project: await platform.getProject(projectId),
          instructions: {
            studio: 'Access Supabase Studio at https://devdb.syncrobit.net/studio',
            logs: 'View logs with: docker compose logs supabase-db',
            status: 'Check status with: supabase status',
          },
        };
      },
    }),

    supabase_status: tool({
      description: 'Instructions for checking local Supabase service status',
      parameters: z.object({}),
      execute: async () => {
        return {
          command: 'supabase status',
          description: 'Run this command in your terminal to see all running services',
          services: [
            'API URL - REST API endpoint',
            'GraphQL URL - GraphQL endpoint', 
            'DB URL - Direct database connection',
            'Studio URL - Web interface',
            'Inbucket URL - Email testing',
            'JWT secret - For token validation',
            'anon key - Public API key',
            'service_role key - Admin API key'
          ],
        };
      },
    }),

    list_docker_services: tool({
      description: 'List running Docker containers for Supabase',
      parameters: z.object({}),
      execute: async () => {
        return {
          command: 'docker compose ps',
          description: 'Run this command to see all Supabase containers',
          containers: [
            'supabase-db - PostgreSQL database',
            'supabase-auth - Authentication service',
            'supabase-rest - PostgREST API',
            'supabase-realtime - Realtime subscriptions',
            'supabase-storage - File storage',
            'supabase-inbucket - Email testing',
            'supabase-studio - Web interface',
            'supabase-edge-runtime - Edge functions'
          ],
          logs: 'View logs with: docker compose logs [container-name]',
        };
      },
    }),

    create_migration: tool({
      description: 'Instructions for creating a new migration file',
      parameters: z.object({
        name: z.string().describe('Name for the migration in snake_case'),
      }),
      execute: async ({ name }) => {
        return {
          command: `supabase migration new ${name}`,
          description: 'Creates a new migration file in the migrations directory',
          steps: [
            '1. Run the command above to create the migration file',
            '2. Edit the generated SQL file in supabase/migrations/',
            '3. Test the migration locally',
            '4. Use the apply_migration tool to apply it'
          ],
          location: 'supabase/migrations/',
        };
      },
    }),

    reset_database: tool({
      description: 'Instructions for resetting the local database',
      parameters: z.object({
        confirm: z.boolean().describe('Set to true to confirm you want to reset').default(false),
      }),
      execute: async ({ confirm }) => {
        if (!confirm) {
          return {
            warning: 'This will destroy all local data!',
            command: 'supabase db reset',
            confirmation: 'Set confirm: true to see the reset instructions',
          };
        }

        return {
          commands: [
            'supabase db reset',
            'supabase db reset --debug  # For verbose output'
          ],
          description: 'Resets the local database to a clean state',
          warning: '⚠️  This will delete all data in your local database!',
          steps: [
            '1. Stop all connections to the database',
            '2. Run: supabase db reset',
            '3. The database will be recreated with initial migrations',
            '4. Run seed scripts if any exist'
          ],
        };
      },
    }),

    list_auth_users: tool({
      description: 'List users in the local auth system',
      parameters: z.object({
        limit: z.number().min(1).max(100).default(10).describe('Number of users to return'),
      }),
      execute: async ({ limit }) => {
        const query = `
          SELECT 
            id,
            email,
            created_at,
            email_confirmed_at,
            last_sign_in_at,
            role
          FROM auth.users 
          ORDER BY created_at DESC 
          LIMIT ${limit}
        `;
        
        return await platform.executeSql(projectId, {
          query,
          read_only: true,
        });
      },
    }),

    list_storage_buckets: tool({
      description: 'List storage buckets in the local instance',
      parameters: z.object({}),
      execute: async () => {
        const query = `
          SELECT 
            id,
            name,
            public,
            created_at,
            updated_at
          FROM storage.buckets 
          ORDER BY created_at DESC
        `;
        
        return await platform.executeSql(projectId, {
          query,
          read_only: true,
        });
      },
    }),

    inspect_table_schema: tool({
      description: 'Get detailed schema information for a specific table',
      parameters: z.object({
        table_name: z.string().describe('Name of the table to inspect'),
        schema_name: z.string().default('public').describe('Schema name'),
      }),
      execute: async ({ table_name, schema_name }) => {
        const query = `
          SELECT 
            column_name,
            data_type,
            is_nullable,
            column_default,
            ordinal_position,
            character_maximum_length,
            numeric_precision,
            numeric_scale
          FROM information_schema.columns 
          WHERE table_name = '${table_name}' 
          AND table_schema = '${schema_name}'
          ORDER BY ordinal_position
        `;
        
        return await platform.executeSql(projectId, {
          query,
          read_only: true,
        });
      },
    }),

    explain_query: tool({
      description: 'Analyze query performance with EXPLAIN',
      parameters: z.object({
        query: z.string().describe('SQL query to analyze'),
        analyze: z.boolean().default(false).describe('Run EXPLAIN ANALYZE (actually executes the query)'),
      }),
      execute: async ({ query, analyze }) => {
        const explainQuery = analyze 
          ? `EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON) ${query}`
          : `EXPLAIN (FORMAT JSON) ${query}`;
        
        return await platform.executeSql(projectId, {
          query: explainQuery,
          read_only: !analyze,
        });
      },
    }),
  };
}