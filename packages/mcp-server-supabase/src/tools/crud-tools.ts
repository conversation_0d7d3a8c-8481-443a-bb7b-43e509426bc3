import { z } from 'zod';
import type { SupabasePlatform } from '../platform/types.js';
import { injectableTool } from './util.js';
import { contextLogger, RequestTracker } from '../utils/logger.js';
import {
  tableSpecSchema,
  columnSelectionSchema,
  dataRecordSchema,
  whereClauseSchema,
  orderBySchema,
  paginationSchema,
  returningSchema,
  safeParseWithDetails,
  type WhereClause,
} from './crud-validation.js';
import {
  createCrudError<PERSON><PERSON><PERSON>,
  CrudErrorUtils,
  type CrudErrorContext,
} from './crud-error-handler.js';

export type CrudToolsOptions = {
  platform: SupabasePlatform;
  projectId?: string;
  readOnly?: boolean;
};

/**
 * Enhanced validation schemas are now imported from crud-validation.ts
 * These provide comprehensive validation including:
 * - PostgreSQL identifier validation with reserved word checking
 * - Enhanced column value validation with size limits
 * - Comprehensive WHERE clause validation with logical operators
 * - Better error messaging and type safety
 */

export function getCrudTools({
  platform,
  projectId,
  readOnly,
}: CrudToolsOptions) {
  const logger = contextLogger.child({ component: 'CrudTools' });
  const project_id = projectId;

  /**
   * Helper function to validate data and handle errors
   */
  function validateOrThrow<T>(
    validation: { success: true; data: T } | { success: false; error: string },
    errorHandler?: any,
    context?: any
  ): T {
    if (!validation.success) {
      const error = new Error((validation as { success: false; error: string }).error);
      if (errorHandler && context) {
        errorHandler.handleError(error, context);
        // This never returns, but TypeScript doesn't know that
        throw error;
      } else {
        throw error;
      }
    }
    return validation.data;
  }

  /**
   * Helper function to escape SQL values for inline queries
   */
  function escapeSqlValue(value: any): string {
    if (value === null || value === undefined) {
      return 'NULL';
    }
    if (typeof value === 'string') {
      return `'${value.replace(/'/g, "''")}'`;
    }
    if (typeof value === 'number') {
      return String(value);
    }
    if (typeof value === 'boolean') {
      return value ? 'TRUE' : 'FALSE';
    }
    if (value instanceof Date) {
      return `'${value.toISOString()}'`;
    }
    if (Array.isArray(value) || typeof value === 'object') {
      return `'${JSON.stringify(value).replace(/'/g, "''")}'`;
    }
    return `'${String(value).replace(/'/g, "''")}'`;
  }

  /**
   * Enhanced helper function to build WHERE clause SQL with inline values
   */
  function buildWhereClause(where: WhereClause): string {
    if ('AND' in where) {
      const conditions = where.AND.map(condition => buildWhereClause(condition));
      return `(${conditions.join(' AND ')})`;
    }

    if ('OR' in where) {
      const conditions = where.OR.map(condition => buildWhereClause(condition));
      return `(${conditions.join(' OR ')})`;
    }

    if ('NOT' in where) {
      const notCondition = buildWhereClause(where.NOT);
      return `NOT (${notCondition})`;
    }

    // Simple condition
    const condition = where as { column: string; operator: string; value?: any };

    switch (condition.operator) {
      case 'IS NULL':
        return `${quoteIdentifier(condition.column)} IS NULL`;
      case 'IS NOT NULL':
        return `${quoteIdentifier(condition.column)} IS NOT NULL`;
      case 'IN':
        if (!Array.isArray(condition.value)) {
          throw new Error('IN operator requires an array value');
        }
        const inValues = condition.value.map(v => escapeSqlValue(v)).join(', ');
        return `${quoteIdentifier(condition.column)} IN (${inValues})`;
      case 'NOT IN':
        if (!Array.isArray(condition.value)) {
          throw new Error('NOT IN operator requires an array value');
        }
        const notInValues = condition.value.map(v => escapeSqlValue(v)).join(', ');
        return `${quoteIdentifier(condition.column)} NOT IN (${notInValues})`;
      case 'BETWEEN':
        if (!Array.isArray(condition.value) || condition.value.length !== 2) {
          throw new Error('BETWEEN operator requires exactly two values');
        }
        return `${quoteIdentifier(condition.column)} BETWEEN ${escapeSqlValue(condition.value[0])} AND ${escapeSqlValue(condition.value[1])}`;
      case 'NOT BETWEEN':
        if (!Array.isArray(condition.value) || condition.value.length !== 2) {
          throw new Error('NOT BETWEEN operator requires exactly two values');
        }
        return `${quoteIdentifier(condition.column)} NOT BETWEEN ${escapeSqlValue(condition.value[0])} AND ${escapeSqlValue(condition.value[1])}`;
      default:
        return `${quoteIdentifier(condition.column)} ${condition.operator} ${escapeSqlValue(condition.value)}`;
    }
  }

  /**
   * Helper function to safely quote identifiers
   */
  function quoteIdentifier(identifier: string): string {
    return `"${identifier.replace(/"/g, '""')}"`;
  }

  const crudTools = {
    create_record: injectableTool({
      description: 'Insert a new record into a table.',
      parameters: z.object({
        project_id: z.string(),
        ...tableSpecSchema.shape,
        data: dataRecordSchema.describe('Column-value pairs for the new record'),
        returning: returningSchema.describe('Columns to return after insert (default: all)'),
      }),
      inject: { project_id },
      execute: async ({ project_id, schema, table, data, returning }) => {
        const requestId = RequestTracker.generateRequestId();
        const errorHandler = createCrudErrorHandler(requestId);
        const startTime = Date.now();

        const context: CrudErrorContext = {
          operation: 'CREATE',
          schema,
          table,
          columns: Object.keys(data),
          recordCount: 1,
          requestId,
        };

        // Validate operation parameters
        errorHandler.validateOperation(context);

        // Check read-only mode
        if (readOnly) {
          errorHandler.handleError(new Error('Cannot create records in read-only mode.'), context);
        }

        // Enhanced validation using safe parsing with better error handling
        validateOrThrow(
          safeParseWithDetails(tableSpecSchema, { schema, table }, 'Table specification'),
          errorHandler,
          context
        );

        validateOrThrow(
          safeParseWithDetails(dataRecordSchema, data, 'Data record'),
          errorHandler,
          context
        );

        if (returning) {
          validateOrThrow(
            safeParseWithDetails(returningSchema, returning, 'Returning columns'),
            errorHandler,
            context
          );
        }

        // Execute with retry logic and enhanced error handling
        return await errorHandler.executeWithRetry(async () => {
          const columns = Object.keys(data);
          const values = Object.values(data);

          const columnsSql = columns.map(col => quoteIdentifier(col)).join(', ');
          const valuesSql = values.map(val => escapeSqlValue(val)).join(', ');
          const tableName = `${quoteIdentifier(schema)}.${quoteIdentifier(table)}`;

          let returningSql = '';
          if (returning && returning.length > 0) {
            returningSql = ` RETURNING ${returning.map((col: string) => quoteIdentifier(col)).join(', ')}`;
          } else {
            returningSql = ' RETURNING *';
          }

          const query = `INSERT INTO ${tableName} (${columnsSql}) VALUES (${valuesSql})${returningSql}`;
          context.query = query;

          logger.info('Executing CREATE query', {
            table: `${schema}.${table}`,
            columns: columns.length,
            requestId,
          });

          const result = await platform.executeSql(project_id, {
            query,
            read_only: false,
          });

          const response = {
            success: true,
            data: result,
            rowsAffected: result.length,
            requestId,
          };

          // Log successful operation
          const duration = Date.now() - startTime;
          errorHandler.logSuccess(context, response, duration);

          return response;
        }, context, {
          maxRetries: 2, // CREATE operations can be retried for network/timeout issues
          baseDelay: 1000,
        });
      },
    }),

    read_records: injectableTool({
      description: 'Read records from a table with optional filtering, sorting, and pagination.',
      parameters: z.object({
        project_id: z.string(),
        ...tableSpecSchema.shape,
        columns: columnSelectionSchema.optional().describe('Columns to select (default: all)'),
        where: whereClauseSchema.optional().describe('WHERE conditions'),
        orderBy: z.array(orderBySchema).optional().describe('ORDER BY clauses'),
        ...paginationSchema.shape,
      }),
      inject: { project_id },
      execute: async ({ project_id, schema, table, columns, where, orderBy, limit, offset }) => {
        // Enhanced validation using safe parsing
        validateOrThrow(
          safeParseWithDetails(tableSpecSchema, { schema, table }, 'Table specification')
        );

        if (columns) {
          validateOrThrow(
            safeParseWithDetails(columnSelectionSchema, columns, 'Column selection')
          );
        }

        if (where) {
          validateOrThrow(
            safeParseWithDetails(whereClauseSchema, where, 'WHERE clause')
          );
        }

        if (orderBy) {
          validateOrThrow(
            safeParseWithDetails(z.array(orderBySchema), orderBy, 'ORDER BY clause')
          );
        }

        validateOrThrow(
          safeParseWithDetails(paginationSchema, { limit, offset }, 'Pagination')
        );

        try {
          const tableName = `${quoteIdentifier(schema)}.${quoteIdentifier(table)}`;

          // Build SELECT clause
          let selectClause = '*';
          if (columns && columns.length > 0) {
            selectClause = columns.map(col => quoteIdentifier(col)).join(', ');
          }

          let query = `SELECT ${selectClause} FROM ${tableName}`;

          // Build WHERE clause
          if (where) {
            const whereSql = buildWhereClause(where);
            query += ` WHERE ${whereSql}`;
          }

          // Build ORDER BY clause
          if (orderBy && orderBy.length > 0) {
            const orderClauses = orderBy.map(order =>
              `${quoteIdentifier(order.column)} ${order.direction}`
            );
            query += ` ORDER BY ${orderClauses.join(', ')}`;
          }

          // Build LIMIT and OFFSET
          if (limit !== undefined) {
            query += ` LIMIT ${limit}`;
          }

          if (offset !== undefined) {
            query += ` OFFSET ${offset}`;
          }

          logger.info('Executing READ query', {
            table: `${schema}.${table}`,
            hasWhere: !!where,
            hasOrderBy: !!(orderBy && orderBy.length > 0),
            limit,
            offset
          });

          const result = await platform.executeSql(project_id, {
            query,
            read_only: true,
          });

          return {
            success: true,
            data: result,
            rowsReturned: result.length,
          };
        } catch (error) {
          logger.error(`READ operation failed for ${schema}.${table}: ${error instanceof Error ? error.message : String(error)}`);
          throw new Error(`Failed to read records: ${error instanceof Error ? error.message : String(error)}`);
        }
      },
    }),

    update_records: injectableTool({
      description: 'Update existing records in a table.',
      parameters: z.object({
        project_id: z.string(),
        ...tableSpecSchema.shape,
        data: dataRecordSchema.describe('Column-value pairs to update'),
        where: whereClauseSchema.describe('WHERE conditions to identify records to update'),
        returning: z.array(z.string()).optional().describe('Columns to return after update (default: all)'),
      }),
      inject: { project_id },
      execute: async ({ project_id, schema, table, data, where, returning }) => {
        const requestId = RequestTracker.generateRequestId();
        const errorHandler = createCrudErrorHandler(requestId);
        const startTime = Date.now();

        const context: CrudErrorContext = {
          operation: 'UPDATE',
          schema,
          table,
          columns: Object.keys(data),
          whereClause: where,
          recordCount: undefined, // Will be known after execution
          requestId,
        };

        // Validate operation parameters (includes WHERE clause requirement for UPDATE)
        errorHandler.validateOperation(context);

        // Check read-only mode
        if (readOnly) {
          errorHandler.handleError(new Error('Cannot update records in read-only mode.'), context);
        }

        // Enhanced validation with better error handling
        validateOrThrow(
          safeParseWithDetails(tableSpecSchema, { schema, table }, 'Table specification'),
          errorHandler,
          context
        );

        validateOrThrow(
          safeParseWithDetails(dataRecordSchema, data, 'Data record'),
          errorHandler,
          context
        );

        validateOrThrow(
          safeParseWithDetails(whereClauseSchema, where, 'WHERE clause'),
          errorHandler,
          context
        );

        if (returning) {
          validateOrThrow(
            safeParseWithDetails(z.array(z.string()), returning, 'Returning columns'),
            errorHandler,
            context
          );
        }

        // Execute with retry logic and enhanced error handling
        return await errorHandler.executeWithRetry(async () => {
          const updates = Object.keys(data);
          const values = Object.values(data);

          if (updates.length === 0) {
            errorHandler.handleError(new Error('No data provided for update'), context);
          }

          const tableName = `${quoteIdentifier(schema)}.${quoteIdentifier(table)}`;

          // Build SET clause
          const setClause = updates.map((col, i) =>
            `${quoteIdentifier(col)} = ${escapeSqlValue(values[i])}`
          ).join(', ');

          let query = `UPDATE ${tableName} SET ${setClause}`;

          // Build WHERE clause
          const whereSql = buildWhereClause(where);
          query += ` WHERE ${whereSql}`;

          // Build RETURNING clause
          let returningSql = '';
          if (returning && returning.length > 0) {
            returningSql = ` RETURNING ${returning.map((col: string) => quoteIdentifier(col)).join(', ')}`;
          } else {
            returningSql = ' RETURNING *';
          }
          query += returningSql;
          context.query = query;

          logger.info('Executing UPDATE query', {
            table: `${schema}.${table}`,
            columns: updates.length,
            hasWhere: true,
            requestId,
          });

          const result = await platform.executeSql(project_id, {
            query,
            read_only: false,
          });

          const response = {
            success: true,
            data: result,
            rowsAffected: result.length,
            requestId,
          };

          // Log successful operation
          const duration = Date.now() - startTime;
          context.recordCount = result.length;
          errorHandler.logSuccess(context, response, duration);

          return response;
        }, context, {
          maxRetries: 2, // UPDATE operations can be retried for network/timeout issues
          baseDelay: 1000,
        });
      },
    }),

    delete_records: injectableTool({
      description: 'Delete records from a table.',
      parameters: z.object({
        project_id: z.string(),
        ...tableSpecSchema.shape,
        where: whereClauseSchema.describe('WHERE conditions to identify records to delete'),
        returning: z.array(z.string()).optional().describe('Columns to return from deleted records (default: all)'),
      }),
      inject: { project_id },
      execute: async ({ project_id, schema, table, where, returning }) => {
        if (readOnly) {
          throw new Error('Cannot delete records in read-only mode.');
        }

        try {
          const tableName = `${quoteIdentifier(schema)}.${quoteIdentifier(table)}`;

          let query = `DELETE FROM ${tableName}`;

          // Build WHERE clause
          const whereSql = buildWhereClause(where);
          query += ` WHERE ${whereSql}`;

          // Build RETURNING clause
          let returningSql = '';
          if (returning && returning.length > 0) {
            returningSql = ` RETURNING ${returning.map((col: string) => quoteIdentifier(col)).join(', ')}`;
          } else {
            returningSql = ' RETURNING *';
          }
          query += returningSql;

          logger.info('Executing DELETE query', {
            table: `${schema}.${table}`,
            hasWhere: true
          });

          const result = await platform.executeSql(project_id, {
            query,
            read_only: false,
          });

          return {
            success: true,
            data: result,
            rowsAffected: result.length,
          };
        } catch (error) {
          logger.error(`DELETE operation failed for ${schema}.${table}: ${error instanceof Error ? error.message : String(error)}`);
          throw new Error(`Failed to delete records: ${error instanceof Error ? error.message : String(error)}`);
        }
      },
    }),

    upsert_record: injectableTool({
      description: 'Insert a record or update it if it already exists (upsert operation).',
      parameters: z.object({
        project_id: z.string(),
        ...tableSpecSchema.shape,
        data: dataRecordSchema.describe('Column-value pairs for the record'),
        conflictColumns: z.array(z.string()).describe('Columns that define uniqueness for conflict resolution'),
        updateOnConflict: z.boolean().default(true).describe('Whether to update on conflict (default: true)'),
        returning: z.array(z.string()).optional().describe('Columns to return after upsert (default: all)'),
      }),
      inject: { project_id },
      execute: async ({ project_id, schema, table, data, conflictColumns, updateOnConflict, returning }) => {
        if (readOnly) {
          throw new Error('Cannot upsert records in read-only mode.');
        }

        try {
          const columns = Object.keys(data);
          const values = Object.values(data);
          
          if (columns.length === 0) {
            throw new Error('No data provided for upsert');
          }

          if (conflictColumns.length === 0) {
            throw new Error('Conflict columns must be specified for upsert');
          }

          const tableName = `${quoteIdentifier(schema)}.${quoteIdentifier(table)}`;
          const columnsSql = columns.map(col => quoteIdentifier(col)).join(', ');
          const valuesSql = values.map(val => escapeSqlValue(val)).join(', ');
          const conflictColumnsSql = conflictColumns.map((col: string) => quoteIdentifier(col)).join(', ');

          let query = `INSERT INTO ${tableName} (${columnsSql}) VALUES (${valuesSql})`;
          query += ` ON CONFLICT (${conflictColumnsSql})`;

          if (updateOnConflict) {
            // Build update set for conflict resolution, excluding conflict columns
            const updateColumns = columns.filter(col => !conflictColumns.includes(col));
            if (updateColumns.length > 0) {
              const updateSetSql = updateColumns.map(col =>
                `${quoteIdentifier(col)} = EXCLUDED.${quoteIdentifier(col)}`
              ).join(', ');
              query += ` DO UPDATE SET ${updateSetSql}`;
            } else {
              query += ' DO NOTHING';
            }
          } else {
            query += ' DO NOTHING';
          }

          // Build RETURNING clause
          let returningSql = '';
          if (returning && returning.length > 0) {
            returningSql = ` RETURNING ${returning.map((col: string) => quoteIdentifier(col)).join(', ')}`;
          } else {
            returningSql = ' RETURNING *';
          }
          query += returningSql;

          logger.info('Executing UPSERT query', {
            table: `${schema}.${table}`,
            columns: columns.length,
            conflictColumns: conflictColumns.length,
            updateOnConflict
          });

          const result = await platform.executeSql(project_id, {
            query,
            read_only: false,
          });

          return {
            success: true,
            data: result,
            rowsAffected: result.length,
          };
        } catch (error) {
          logger.error(`UPSERT operation failed for ${schema}.${table}: ${error instanceof Error ? error.message : String(error)}`);
          throw new Error(`Failed to upsert record: ${error instanceof Error ? error.message : String(error)}`);
        }
      },
    }),

    // ==================== BULK OPERATIONS ====================

    bulk_create_records: injectableTool({
      description: 'Insert multiple records into a table in a single operation.',
      parameters: z.object({
        project_id: z.string(),
        ...tableSpecSchema.shape,
        data: z.array(dataRecordSchema).min(1).max(1000).describe('Array of records to insert (max 1000)'),
        returning: returningSchema.describe('Columns to return after insert (default: all)'),
        batchSize: z.number().min(1).max(1000).default(100).describe('Number of records per batch (default: 100)'),
      }),
      inject: { project_id },
      execute: async ({ project_id, schema, table, data, returning, batchSize }) => {
        if (readOnly) {
          throw new Error('Cannot create records in read-only mode.');
        }

        // Enhanced validation using safe parsing
        validateOrThrow(
          safeParseWithDetails(tableSpecSchema, { schema, table }, 'Table specification')
        );

        validateOrThrow(
          safeParseWithDetails(z.array(dataRecordSchema).min(1).max(1000), data, 'Bulk data records')
        );

        try {
          const tableName = `${quoteIdentifier(schema)}.${quoteIdentifier(table)}`;
          let returningSql = '';
          if (returning && returning.length > 0) {
            returningSql = ` RETURNING ${returning.map((col: string) => quoteIdentifier(col)).join(', ')}`;
          } else {
            returningSql = ' RETURNING *';
          }

          let allResults: any[] = [];
          const totalRecords = data.length;

          // Process in batches to avoid query size limits
          for (let i = 0; i < totalRecords; i += batchSize) {
            const batch = data.slice(i, i + batchSize);
            
            // Ensure all records have the same columns
            const firstRecord = batch[0];
            if (!firstRecord) {
              throw new Error('Batch cannot be empty');
            }
            const expectedColumns = Object.keys(firstRecord).sort();
            
            for (let j = 1; j < batch.length; j++) {
              const currentRecord = batch[j];
              if (!currentRecord) {
                throw new Error(`Record at index ${j} is undefined`);
              }
              const recordColumns = Object.keys(currentRecord).sort();
              if (JSON.stringify(recordColumns) !== JSON.stringify(expectedColumns)) {
                throw new Error(`Record ${i + j + 1} has different columns than the first record. All records must have the same column structure.`);
              }
            }

            const columns = expectedColumns;
            const columnsSql = columns.map(col => quoteIdentifier(col)).join(', ');
            
            // Build VALUES clauses for the batch
            const valuesClauses = batch.map(record => {
              const values = columns.map(col => escapeSqlValue(record[col]));
              return `(${values.join(', ')})`;
            });

            const query = `INSERT INTO ${tableName} (${columnsSql}) VALUES ${valuesClauses.join(', ')}${returningSql}`;

            logger.info('Executing BULK CREATE query', {
              table: `${schema}.${table}`,
              batchNumber: Math.floor(i / batchSize) + 1,
              batchSize: batch.length,
              totalBatches: Math.ceil(totalRecords / batchSize)
            });

            const batchResult = await platform.executeSql(project_id, {
              query,
              read_only: false,
            });

            allResults = allResults.concat(batchResult);
          }

          return {
            success: true,
            data: allResults,
            rowsAffected: allResults.length,
            totalRecords: totalRecords,
            batchesProcessed: Math.ceil(totalRecords / batchSize),
          };
        } catch (error) {
          logger.error(`BULK CREATE operation failed for ${schema}.${table}: ${error instanceof Error ? error.message : String(error)}`);
          throw new Error(`Failed to bulk create records: ${error instanceof Error ? error.message : String(error)}`);
        }
      },
    }),

    bulk_update_records: injectableTool({
      description: 'Update multiple records with different data in a single transaction.',
      parameters: z.object({
        project_id: z.string(),
        ...tableSpecSchema.shape,
        updates: z.array(z.object({
          where: whereClauseSchema.describe('WHERE conditions to identify the record to update'),
          data: dataRecordSchema.describe('Column-value pairs to update'),
        })).min(1).max(1000).describe('Array of update operations (max 1000)'),
        returning: z.array(z.string()).optional().describe('Columns to return after update (default: all)'),
      }),
      inject: { project_id },
      execute: async ({ project_id, schema, table, updates, returning }) => {
        if (readOnly) {
          throw new Error('Cannot update records in read-only mode.');
        }

        try {
          if (updates.length === 0) {
            throw new Error('No update operations provided');
          }

          const tableName = `${quoteIdentifier(schema)}.${quoteIdentifier(table)}`;
          let returningSql = '';
          if (returning && returning.length > 0) {
            returningSql = ` RETURNING ${returning.map((col: string) => quoteIdentifier(col)).join(', ')}`;
          } else {
            returningSql = ' RETURNING *';
          }

          // Use a CTE (Common Table Expression) for bulk updates
          const updateCases = [];
          const allColumns = new Set<string>();
          const whereConditions: string[] = [];

          // Extract all unique columns and build CASE statements
          updates.forEach((update, index) => {
            Object.keys(update.data).forEach(col => allColumns.add(col));
            whereConditions.push(buildWhereClause(update.where));
          });

          const columnsList = Array.from(allColumns);
          
          // Build CASE statements for each column
          const caseClauses = columnsList.map(column => {
            const cases = updates.map((update, index) => {
              if (update.data[column] !== undefined) {
                return `WHEN (${buildWhereClause(update.where)}) THEN ${escapeSqlValue(update.data[column])}`;
              }
              return null;
            }).filter(Boolean);
            
            if (cases.length === 0) return null;
            
            return `${quoteIdentifier(column)} = CASE ${cases.join(' ')} ELSE ${quoteIdentifier(column)} END`;
          }).filter(Boolean);

          if (caseClauses.length === 0) {
            throw new Error('No valid updates to perform');
          }

          const combinedWhere = `(${whereConditions.join(') OR (')})`;
          const query = `UPDATE ${tableName} SET ${caseClauses.join(', ')} WHERE ${combinedWhere}${returningSql}`;

          logger.info('Executing BULK UPDATE query', {
            table: `${schema}.${table}`,
            updateCount: updates.length,
            columnsAffected: columnsList.length
          });

          const result = await platform.executeSql(project_id, {
            query,
            read_only: false,
          });

          return {
            success: true,
            data: result,
            rowsAffected: result.length,
            updatesRequested: updates.length,
          };
        } catch (error) {
          logger.error(`BULK UPDATE operation failed for ${schema}.${table}: ${error instanceof Error ? error.message : String(error)}`);
          throw new Error(`Failed to bulk update records: ${error instanceof Error ? error.message : String(error)}`);
        }
      },
    }),

    bulk_delete_records: injectableTool({
      description: 'Delete multiple sets of records matching different conditions.',
      parameters: z.object({
        project_id: z.string(),
        ...tableSpecSchema.shape,
        conditions: z.array(whereClauseSchema).min(1).max(100).describe('Array of WHERE conditions for deletion (max 100)'),
        returning: z.array(z.string()).optional().describe('Columns to return from deleted records (default: all)'),
      }),
      inject: { project_id },
      execute: async ({ project_id, schema, table, conditions, returning }) => {
        if (readOnly) {
          throw new Error('Cannot delete records in read-only mode.');
        }

        try {
          if (conditions.length === 0) {
            throw new Error('No delete conditions provided');
          }

          const tableName = `${quoteIdentifier(schema)}.${quoteIdentifier(table)}`;
          let returningSql = '';
          if (returning && returning.length > 0) {
            returningSql = ` RETURNING ${returning.map((col: string) => quoteIdentifier(col)).join(', ')}`;
          } else {
            returningSql = ' RETURNING *';
          }

          // Combine all conditions with OR
          const whereConditions = conditions.map(condition => buildWhereClause(condition));
          const combinedWhere = `(${whereConditions.join(') OR (')})`;
          
          const query = `DELETE FROM ${tableName} WHERE ${combinedWhere}${returningSql}`;

          logger.info('Executing BULK DELETE query', {
            table: `${schema}.${table}`,
            conditionCount: conditions.length
          });

          const result = await platform.executeSql(project_id, {
            query,
            read_only: false,
          });

          return {
            success: true,
            data: result,
            rowsAffected: result.length,
            conditionsProvided: conditions.length,
          };
        } catch (error) {
          logger.error(`BULK DELETE operation failed for ${schema}.${table}: ${error instanceof Error ? error.message : String(error)}`);
          throw new Error(`Failed to bulk delete records: ${error instanceof Error ? error.message : String(error)}`);
        }
      },
    }),

    // ==================== TRANSACTION SUPPORT ====================

    execute_transaction: injectableTool({
      description: 'Execute multiple CRUD operations in a single transaction.',
      parameters: z.object({
        project_id: z.string(),
        operations: z.array(z.object({
          type: z.enum(['CREATE', 'READ', 'UPDATE', 'DELETE', 'UPSERT']).describe('Type of operation'),
          ...tableSpecSchema.shape,
          data: dataRecordSchema.optional().describe('Data for CREATE/UPDATE/UPSERT operations'),
          where: whereClauseSchema.optional().describe('WHERE conditions for READ/UPDATE/DELETE operations'),
          columns: columnSelectionSchema.optional().describe('Columns to select for READ operations'),
          conflictColumns: z.array(z.string()).optional().describe('Conflict columns for UPSERT operations'),
          updateOnConflict: z.boolean().optional().describe('Whether to update on conflict for UPSERT'),
          returning: z.array(z.string()).optional().describe('Columns to return'),
        })).min(1).max(50).describe('Array of operations to execute in transaction (max 50)'),
      }),
      inject: { project_id },
      execute: async ({ project_id, operations }) => {
        if (readOnly) {
          throw new Error('Cannot execute transactions in read-only mode.');
        }

        try {
          const queries = ['BEGIN;'];
          const operationDetails = [];

          // Build all queries first to validate them
          for (let i = 0; i < operations.length; i++) {
            const op = operations[i];
            if (!op) {
              throw new Error(`Operation ${i + 1}: Operation is undefined`);
            }
            
            const tableName = `${quoteIdentifier(op.schema)}.${quoteIdentifier(op.table)}`;
            let query = '';
            let returningSql = '';

            if (op.returning && op.returning.length > 0) {
              returningSql = ` RETURNING ${op.returning.map((col: string) => quoteIdentifier(col)).join(', ')}`;
            } else if (op.type !== 'READ') {
              returningSql = ' RETURNING *';
            }

            switch (op.type) {
              case 'CREATE':
                if (!op.data || Object.keys(op.data).length === 0) {
                  throw new Error(`Operation ${i + 1}: CREATE requires data`);
                }
                const createColumns = Object.keys(op.data);
                const createValues = Object.values(op.data);
                const createColumnsSql = createColumns.map(col => quoteIdentifier(col)).join(', ');
                const createValuesSql = createValues.map(val => escapeSqlValue(val)).join(', ');
                query = `INSERT INTO ${tableName} (${createColumnsSql}) VALUES (${createValuesSql})${returningSql}`;
                break;

              case 'READ':
                let selectClause = '*';
                if (op.columns && op.columns.length > 0) {
                  selectClause = op.columns.map(col => quoteIdentifier(col)).join(', ');
                }
                query = `SELECT ${selectClause} FROM ${tableName}`;
                if (op.where) {
                  query += ` WHERE ${buildWhereClause(op.where)}`;
                }
                break;

              case 'UPDATE':
                if (!op.data || Object.keys(op.data).length === 0) {
                  throw new Error(`Operation ${i + 1}: UPDATE requires data`);
                }
                if (!op.where) {
                  throw new Error(`Operation ${i + 1}: UPDATE requires WHERE conditions for safety`);
                }
                const updateColumns = Object.keys(op.data);
                const updateValues = Object.values(op.data);
                const updateSetClause = updateColumns.map((col, idx) =>
                  `${quoteIdentifier(col)} = ${escapeSqlValue(updateValues[idx])}`
                ).join(', ');
                query = `UPDATE ${tableName} SET ${updateSetClause} WHERE ${buildWhereClause(op.where)}${returningSql}`;
                break;

              case 'DELETE':
                if (!op.where) {
                  throw new Error(`Operation ${i + 1}: DELETE requires WHERE conditions for safety`);
                }
                query = `DELETE FROM ${tableName} WHERE ${buildWhereClause(op.where)}${returningSql}`;
                break;

              case 'UPSERT':
                if (!op.data || Object.keys(op.data).length === 0) {
                  throw new Error(`Operation ${i + 1}: UPSERT requires data`);
                }
                if (!op.conflictColumns || op.conflictColumns.length === 0) {
                  throw new Error(`Operation ${i + 1}: UPSERT requires conflict columns`);
                }
                const upsertColumns = Object.keys(op.data);
                const upsertValues = Object.values(op.data);
                const upsertColumnsSql = upsertColumns.map(col => quoteIdentifier(col)).join(', ');
                const upsertValuesSql = upsertValues.map(val => escapeSqlValue(val)).join(', ');
                const conflictColumnsSql = op.conflictColumns.map((col: string) => quoteIdentifier(col)).join(', ');
                
                query = `INSERT INTO ${tableName} (${upsertColumnsSql}) VALUES (${upsertValuesSql}) ON CONFLICT (${conflictColumnsSql})`;
                if (op.updateOnConflict !== false) {
                  const updateColumns = upsertColumns.filter(col => !op.conflictColumns!.includes(col));
                  if (updateColumns.length > 0) {
                    const updateSetSql = updateColumns.map(col =>
                      `${quoteIdentifier(col)} = EXCLUDED.${quoteIdentifier(col)}`
                    ).join(', ');
                    query += ` DO UPDATE SET ${updateSetSql}`;
                  } else {
                    query += ' DO NOTHING';
                  }
                } else {
                  query += ' DO NOTHING';
                }
                query += returningSql;
                break;

              default:
                throw new Error(`Operation ${i + 1}: Unknown operation type ${(op as any).type}`);
            }

            queries.push(query + ';');
            operationDetails.push({
              operation: i + 1,
              type: op.type,
              table: `${op.schema}.${op.table}`,
              query: query
            });
          }

          queries.push('COMMIT;');
          const fullTransaction = queries.join('\n');

          logger.info('Executing TRANSACTION', {
            operationCount: operations.length,
            operations: operationDetails.map(op => `${op.operation}: ${op.type} on ${op.table}`)
          });

          // Execute the complete transaction
          const result = await platform.executeSql(project_id, {
            query: fullTransaction,
            read_only: false,
          });

          return {
            success: true,
            data: result,
            operationsExecuted: operations.length,
            operationDetails: operationDetails,
          };
        } catch (error) {
          // If there's an error, the transaction will automatically rollback
          logger.error(`TRANSACTION failed: ${error instanceof Error ? error.message : String(error)}`);
          throw new Error(`Failed to execute transaction: ${error instanceof Error ? error.message : String(error)}`);
        }
      },
    }),
  };

  return crudTools;
}