# Stage 1: Build environment
FROM node:20.15.1-alpine3.20 AS builder

# Update packages to ensure all security patches are applied
RUN apk --no-cache upgrade

# Set the working directory in the container
WORKDIR /usr/src/app

# Copy root package files and all package sources
COPY package.json ./
COPY package-lock.json ./
COPY packages/ ./packages/

# Install all dependencies
RUN npm install

# Set the working directory to the supabase mcp server for the build
WORKDIR /usr/src/app/packages/mcp-server-supabase

# Build the TypeScript project
RUN npm run build

# Prune development dependencies
RUN npm prune --production

# Stage 2: Production environment
FROM node:20-slim

# Set the working directory in the container
WORKDIR /usr/src/app

# Copy the built application and production node_modules from the builder stage
COPY --from=builder /usr/src/app/packages/mcp-server-supabase/dist ./packages/mcp-server-supabase/dist
COPY --from=builder /usr/src/app/node_modules ./node_modules
COPY --from=builder /usr/src/app/packages/mcp-utils ./packages/mcp-utils

# Define the entry point for the container
ENTRYPOINT ["node", "./packages/mcp-server-supabase/dist/transports/stdio.js"]