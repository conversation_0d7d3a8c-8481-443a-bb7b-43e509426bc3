// Simple test script to verify validation implementation
import {
  postgresIdentifierSchema,
  columnValueSchema,
  whereConditionSchema,
  tableSpecSchema,
  safeParseWithDetails,
} from './src/tools/crud-validation.js';

console.log('Testing PostgreSQL identifier validation...');

// Test valid identifiers
const validIdentifiers = ['table_name', 'column123', '_private_column', 'user_id'];
validIdentifiers.forEach(identifier => {
  const result = postgresIdentifierSchema.safeParse(identifier);
  console.log(`${identifier}: ${result.success ? 'PASS' : 'FAIL'}`);
});

// Test invalid identifiers (should fail)
const invalidIdentifiers = ['123invalid', 'select', 'invalid-name', 'a'.repeat(64)];
invalidIdentifiers.forEach(identifier => {
  const result = postgresIdentifierSchema.safeParse(identifier);
  console.log(`${identifier}: ${result.success ? 'UNEXPECTED PASS' : 'EXPECTED FAIL'}`);
});

console.log('\nTesting column value validation...');

// Test valid values
const validValues = ['text', 42, true, null, { key: 'value' }];
validValues.forEach(value => {
  const result = columnValueSchema.safeParse(value);
  console.log(`${JSON.stringify(value)}: ${result.success ? 'PASS' : 'FAIL'}`);
});

// Test invalid values (should fail)
const largeString = 'a'.repeat(1048577);
const result = columnValueSchema.safeParse(largeString);
console.log(`Large string (${largeString.length} chars): ${result.success ? 'UNEXPECTED PASS' : 'EXPECTED FAIL'}`);

console.log('\nTesting WHERE condition validation...');

// Test valid WHERE conditions
const validConditions = [
  { column: 'id', operator: '=', value: 1 },
  { column: 'name', operator: 'LIKE', value: '%test%' },
  { column: 'active', operator: 'IS NULL' },
  { column: 'status', operator: 'IN', value: ['active', 'pending'] },
];

validConditions.forEach(condition => {
  const result = whereConditionSchema.safeParse(condition);
  console.log(`${JSON.stringify(condition)}: ${result.success ? 'PASS' : 'FAIL'}`);
});

console.log('\nTesting safe parsing with details...');

// Test safe parsing with validation error formatting
const testData = { invalid: 'data' };
const result2 = safeParseWithDetails(
  tableSpecSchema,
  testData,
  'Table specification'
);

console.log(`Safe parsing test: ${result2.success ? 'PASS' : 'EXPECTED FAIL'}`);
if (!result2.success) {
  console.log(`Error message: ${result2.error}`);
}

console.log('\nValidation implementation testing complete!');