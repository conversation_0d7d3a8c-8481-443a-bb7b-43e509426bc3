// Comprehensive test script for enhanced CRUD operation logic
import { getCrudTools } from './src/tools/crud-tools.js';
import { createMockPlatform } from './test/test-utils.js';

console.log('🧪 Testing Enhanced CRUD Operation Logic...\n');

// Create a mock platform for testing
const mockPlatform = createMockPlatform();
const crudTools = getCrudTools({
  platform: mockPlatform,
  projectId: 'test-project',
  readOnly: false,
});

// Helper function to simulate tool execution
async function executeToolTest(toolName: string, params: any) {
  try {
    console.log(`\n📋 Testing ${toolName}:`);
    console.log(`Parameters:`, JSON.stringify(params, null, 2));
    
    const tool = crudTools[toolName as keyof typeof crudTools];
    if (!tool) {
      throw new Error(`Tool ${toolName} not found`);
    }
    
    // @ts-ignore - We're testing the tool execution
    const result = await tool.execute(params);
    console.log(`✅ ${toolName} executed successfully`);
    console.log(`Result:`, JSON.stringify(result, null, 2));
    return result;
  } catch (error) {
    console.log(`❌ ${toolName} failed:`, error instanceof Error ? error.message : String(error));
    return null;
  }
}

async function runTests() {
  console.log('='.repeat(60));
  console.log('🔬 Enhanced CRUD Operation Logic Tests');
  console.log('='.repeat(60));

  // Test 1: Basic CRUD Operations
  console.log('\n1️⃣  Testing Basic CRUD Operations...');
  
  await executeToolTest('create_record', {
    project_id: 'test-project',
    schema: 'public',
    table: 'users',
    data: {
      name: 'John Doe',
      email: '<EMAIL>',
      age: 30
    }
  });

  await executeToolTest('read_records', {
    project_id: 'test-project',
    schema: 'public',
    table: 'users',
    where: {
      column: 'email',
      operator: '=',
      value: '<EMAIL>'
    }
  });

  await executeToolTest('update_records', {
    project_id: 'test-project',
    schema: 'public',
    table: 'users',
    data: {
      age: 31
    },
    where: {
      column: 'email',
      operator: '=',
      value: '<EMAIL>'
    }
  });

  await executeToolTest('delete_records', {
    project_id: 'test-project',
    schema: 'public',
    table: 'users',
    where: {
      column: 'email',
      operator: '=',
      value: '<EMAIL>'
    }
  });

  // Test 2: Complex WHERE Conditions
  console.log('\n2️⃣  Testing Complex WHERE Conditions...');
  
  await executeToolTest('read_records', {
    project_id: 'test-project',
    schema: 'public',
    table: 'users',
    where: {
      AND: [
        { column: 'age', operator: '>=', value: 18 },
        { column: 'status', operator: 'IN', value: ['active', 'pending'] }
      ]
    }
  });

  await executeToolTest('read_records', {
    project_id: 'test-project',
    schema: 'public',
    table: 'users',
    where: {
      OR: [
        { column: 'email', operator: 'LIKE', value: '%@gmail.com' },
        { column: 'email', operator: 'LIKE', value: '%@yahoo.com' }
      ]
    }
  });

  // Test 3: Bulk Operations
  console.log('\n3️⃣  Testing Bulk Operations...');
  
  await executeToolTest('bulk_create_records', {
    project_id: 'test-project',
    schema: 'public',
    table: 'users',
    data: [
      { name: 'Alice', email: '<EMAIL>', age: 25 },
      { name: 'Bob', email: '<EMAIL>', age: 35 },
      { name: 'Charlie', email: '<EMAIL>', age: 28 }
    ],
    batchSize: 2
  });

  await executeToolTest('bulk_update_records', {
    project_id: 'test-project',
    schema: 'public',
    table: 'users',
    updates: [
      {
        where: { column: 'name', operator: '=', value: 'Alice' },
        data: { age: 26 }
      },
      {
        where: { column: 'name', operator: '=', value: 'Bob' },
        data: { age: 36 }
      }
    ]
  });

  await executeToolTest('bulk_delete_records', {
    project_id: 'test-project',
    schema: 'public',
    table: 'users',
    conditions: [
      { column: 'age', operator: '<', value: 20 },
      { column: 'status', operator: '=', value: 'inactive' }
    ]
  });

  // Test 4: Upsert Operations
  console.log('\n4️⃣  Testing Upsert Operations...');
  
  await executeToolTest('upsert_record', {
    project_id: 'test-project',
    schema: 'public',
    table: 'users',
    data: {
      email: '<EMAIL>',
      name: 'John Smith',
      age: 32
    },
    conflictColumns: ['email'],
    updateOnConflict: true
  });

  // Test 5: Transaction Support
  console.log('\n5️⃣  Testing Transaction Support...');
  
  await executeToolTest('execute_transaction', {
    project_id: 'test-project',
    operations: [
      {
        type: 'CREATE',
        schema: 'public',
        table: 'users',
        data: { name: 'Transaction User', email: '<EMAIL>', age: 40 }
      },
      {
        type: 'UPDATE',
        schema: 'public',
        table: 'users',
        data: { age: 41 },
        where: { column: 'email', operator: '=', value: '<EMAIL>' }
      },
      {
        type: 'READ',
        schema: 'public',
        table: 'users',
        columns: ['name', 'email', 'age'],
        where: { column: 'email', operator: '=', value: '<EMAIL>' }
      }
    ]
  });

  // Test 6: Error Handling
  console.log('\n6️⃣  Testing Error Handling...');
  
  // Invalid table name
  await executeToolTest('create_record', {
    project_id: 'test-project',
    schema: 'public',
    table: 'select', // Reserved word
    data: { name: 'Test' }
  });

  // Missing required WHERE clause for UPDATE
  await executeToolTest('update_records', {
    project_id: 'test-project',
    schema: 'public',
    table: 'users',
    data: { age: 99 }
    // Missing WHERE clause
  });

  // Bulk operations with mismatched columns
  await executeToolTest('bulk_create_records', {
    project_id: 'test-project',
    schema: 'public',
    table: 'users',
    data: [
      { name: 'Alice', email: '<EMAIL>' },
      { name: 'Bob', age: 35 } // Different columns
    ]
  });

  console.log('\n='.repeat(60));
  console.log('🎯 Enhanced CRUD Operation Logic Testing Complete!');
  console.log('='.repeat(60));
}

// Create a simple mock platform for testing
function createMockPlatform() {
  return {
    async executeSql(projectId: string, options: any) {
      // Mock successful execution
      console.log(`🔧 Mock SQL execution:`, options.query);
      
      // Return mock data based on query type
      if (options.query.includes('SELECT')) {
        return [
          { id: 1, name: 'John Doe', email: '<EMAIL>', age: 30 },
          { id: 2, name: 'Jane Smith', email: '<EMAIL>', age: 25 }
        ];
      } else if (options.query.includes('INSERT') || options.query.includes('UPDATE') || options.query.includes('DELETE')) {
        return [
          { id: 1, name: 'John Doe', email: '<EMAIL>', age: 30 }
        ];
      }
      
      return [];
    }
  };
}

// Run the tests
runTests().catch(console.error);