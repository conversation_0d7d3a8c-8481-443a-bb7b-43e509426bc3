import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config({ path: '.env.local' });

async function testConnection() {
  console.log('Testing database connection...');
  console.log('SUPABASE_URL:', process.env.SUPABASE_URL);
  console.log('DATABASE_URL:', process.env.DATABASE_URL);
  
  const client = createClient(
    process.env.SUPABASE_URL || 'https://devdb.syncrobit.net',
    process.env.SUPABASE_SERVICE_ROLE_KEY
  );

  try {
    // Test connection with public schema first
    const { data, error } = await client
      .from('token_store')
      .select('*')
      .limit(1);

    if (error) {
      console.error('Database error:', error);
      return false;
    }

    console.log('Connection successful! Data:', data);
    return true;
  } catch (err) {
    console.error('Connection failed:', err);
    return false;
  }
}

testConnection().then(success => {
  process.exit(success ? 0 : 1);
});