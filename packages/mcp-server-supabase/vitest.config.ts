import { defineConfig, configDefaults } from 'vitest/config';
import { textLoaderPlugin } from './test/plugins/text-loader.js';

export default defineConfig({
  plugins: [textLoaderPlugin('.sql')],
  resolve: {
    alias: {
      '@': new URL('./src', import.meta.url).pathname,
    },
  },
  test: {
    setupFiles: ['./vitest.setup.ts'],
    testTimeout: 30_000, // PGlite can take a while to initialize
    environment: 'node',
    coverage: {
      reporter: ['text', 'lcov'],
      reportsDirectory: 'test/coverage',
      include: ['src/**/*.{ts,tsx}'],
      exclude: [...configDefaults.coverage.exclude!, 'src/transports/stdio.ts'],
    },
  },
});
