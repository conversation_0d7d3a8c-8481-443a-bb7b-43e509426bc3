#!/usr/bin/env node

/**
 * Configuration Helper for Local Supabase MCP Server
 * 
 * This script helps generate MCP client configurations by reading
 * the current Supabase status and creating ready-to-use config files.
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const os = require('os');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function getSupabaseStatus() {
  try {
    const status = execSync('supabase status', { encoding: 'utf8' });
    
    // Parse the status output to extract key information
    const lines = status.split('\n');
    const config = {};
    
    for (const line of lines) {
      if (line.includes('API URL:')) {
        config.url = line.split(':').slice(1).join(':').trim();
      } else if (line.includes('anon key:')) {
        config.anonKey = line.split(':')[1].trim();
      } else if (line.includes('service_role key:')) {
        config.serviceRoleKey = line.split(':')[1].trim();
      }
    }
    
    return config;
  } catch (error) {
    log('❌ Failed to get Supabase status. Make sure Supabase is running.', 'red');
    log('   Run: supabase start', 'yellow');
    process.exit(1);
  }
}

function getProjectPath() {
  return path.resolve(__dirname);
}

function generateClaudeDesktopConfig(config, projectPath) {
  const configPath = path.join(projectPath, 'dist', 'transports', 'stdio.js');
  
  return {
    mcpServers: {
      'supabase-local': {
        command: 'node',
        args: [configPath],
        env: {
          SUPABASE_URL: config.url,
          SUPABASE_ANON_KEY: config.anonKey,
          SUPABASE_SERVICE_ROLE_KEY: config.serviceRoleKey,
          READ_ONLY: 'false',
          DEBUG_SQL: 'false'
        }
      }
    }
  };
}

function generateClineConfig(config, projectPath) {
  const configPath = path.join(projectPath, 'dist', 'transports', 'stdio.js');
  const nodeCommand = process.platform === 'win32' ? 'node' : '/usr/local/bin/node';
  
  return {
    mcpServers: {
      'supabase-local': {
        command: nodeCommand,
        args: [configPath],
        env: {
          SUPABASE_URL: config.url,
          SUPABASE_ANON_KEY: config.anonKey,
          SUPABASE_SERVICE_ROLE_KEY: config.serviceRoleKey,
          READ_ONLY: 'false',
          DEBUG_SQL: 'false'
        }
      }
    }
  };
}

function getClaudeDesktopConfigPath() {
  const platform = process.platform;
  const homeDir = os.homedir();
  
  switch (platform) {
    case 'darwin': // macOS
      return path.join(homeDir, 'Library', 'Application Support', 'Claude', 'claude_desktop_config.json');
    case 'win32': // Windows
      return path.join(process.env.APPDATA || path.join(homeDir, 'AppData', 'Roaming'), 'Claude', 'claude_desktop_config.json');
    case 'linux': // Linux
      return path.join(homeDir, '.config', 'Claude', 'claude_desktop_config.json');
    default:
      return null;
  }
}

function saveConfig(config, filename) {
  const outputPath = path.join(__dirname, 'examples', filename);
  fs.writeFileSync(outputPath, JSON.stringify(config, null, 2));
  log(`✅ Configuration saved to: ${outputPath}`, 'green');
}

function main() {
  log('🔧 Local Supabase MCP Configuration Helper', 'cyan');
  log('============================================', 'cyan');
  
  // Check if Supabase is running and get configuration
  log('📡 Getting Supabase status...', 'blue');
  const supabaseConfig = getSupabaseStatus();
  
  if (!supabaseConfig.url || !supabaseConfig.anonKey || !supabaseConfig.serviceRoleKey) {
    log('❌ Could not extract all required configuration from Supabase status', 'red');
    process.exit(1);
  }
  
  log('✅ Supabase configuration retrieved', 'green');
  log(`   URL: ${supabaseConfig.url}`, 'yellow');
  log(`   Anon Key: ${supabaseConfig.anonKey.substring(0, 20)}...`, 'yellow');
  log(`   Service Role Key: ${supabaseConfig.serviceRoleKey.substring(0, 20)}...`, 'yellow');
  
  // Get project path
  const projectPath = getProjectPath();
  log(`📁 Project path: ${projectPath}`, 'blue');
  
  // Check if built files exist
  const builtServerPath = path.join(projectPath, 'dist', 'transports', 'stdio.js');
  if (!fs.existsSync(builtServerPath)) {
    log('❌ Built server files not found. Please run: npm run build', 'red');
    process.exit(1);
  }
  
  // Generate configurations
  log('⚙️  Generating configurations...', 'blue');
  
  const claudeConfig = generateClaudeDesktopConfig(supabaseConfig, projectPath);
  const clineConfig = generateClineConfig(supabaseConfig, projectPath);
  const cursorConfig = generateClaudeDesktopConfig(supabaseConfig, projectPath); // Same as Claude Desktop
  
  // Save configurations
  saveConfig(claudeConfig, 'claude-desktop-config-generated.json');
  saveConfig(clineConfig, 'cline-config-generated.json');
  saveConfig(cursorConfig, 'cursor-config-generated.json');
  
  // Try to detect Claude Desktop config path
  const claudeConfigPath = getClaudeDesktopConfigPath();
  if (claudeConfigPath) {
    log(`💡 Claude Desktop config should go in: ${claudeConfigPath}`, 'yellow');
    
    if (fs.existsSync(claudeConfigPath)) {
      log('⚠️  Existing Claude Desktop config found', 'yellow');
      log('   Please merge the generated config manually', 'yellow');
    }
  }
  
  // Instructions
  log('', 'reset');
  log('📋 Next Steps:', 'cyan');
  log('1. Choose the appropriate generated config file from examples/', 'reset');
  log('2. Copy the configuration to your MCP client', 'reset');
  log('3. Update the file paths if needed', 'reset');
  log('4. Restart your MCP client', 'reset');
  log('', 'reset');
  log('🧪 Test your setup:', 'cyan');
  log('   npm run dev:local', 'yellow');
  log('', 'reset');
  log('✅ Configuration generation complete!', 'green');
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = {
  getSupabaseStatus,
  generateClaudeDesktopConfig,
  generateClineConfig,
  getClaudeDesktopConfigPath
};