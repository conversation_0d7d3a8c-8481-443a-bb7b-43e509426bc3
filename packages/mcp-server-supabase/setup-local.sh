#!/bin/bash

# =============================================================================
# Local Supabase MCP Server Setup Script
# =============================================================================
# This script automates the setup process for the local Supabase MCP server
# Run with: bash setup-local.sh

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Print colored output
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_header() {
    echo -e "${BLUE}"
    echo "============================================================================="
    echo "$1"
    echo "============================================================================="
    echo -e "${NC}"
}

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

print_header "🚀 Local Supabase MCP Server Setup"

# =============================================================================
# Prerequisites Check
# =============================================================================

print_header "📋 Checking Prerequisites"

# Check Node.js
if command_exists node; then
    NODE_VERSION=$(node --version)
    NODE_MAJOR=$(echo $NODE_VERSION | cut -d'.' -f1 | sed 's/v//')
    if [ "$NODE_MAJOR" -ge 18 ]; then
        print_success "Node.js $NODE_VERSION (✓ >= v18.0.0)"
    else
        print_error "Node.js $NODE_VERSION is too old. Please install Node.js 18 or higher."
        exit 1
    fi
else
    print_error "Node.js is not installed. Please install Node.js 18 or higher."
    print_info "Visit: https://nodejs.org/"
    exit 1
fi

# Check npm
if command_exists npm; then
    NPM_VERSION=$(npm --version)
    print_success "npm $NPM_VERSION"
else
    print_error "npm is not installed."
    exit 1
fi

# Check Docker
if command_exists docker; then
    DOCKER_VERSION=$(docker --version | cut -d' ' -f3 | sed 's/,//')
    print_success "Docker $DOCKER_VERSION"
    
    # Check if Docker is running
    if docker info >/dev/null 2>&1; then
        print_success "Docker daemon is running"
    else
        print_error "Docker daemon is not running. Please start Docker."
        exit 1
    fi
else
    print_error "Docker is not installed. Please install Docker."
    print_info "Visit: https://docs.docker.com/get-docker/"
    exit 1
fi

# Check Docker Compose
if command_exists "docker compose" || command_exists docker-compose; then
    if command_exists "docker compose"; then
        COMPOSE_VERSION=$(docker compose version | cut -d' ' -f4)
        print_success "Docker Compose $COMPOSE_VERSION"
        DOCKER_COMPOSE_CMD="docker compose"
    else
        COMPOSE_VERSION=$(docker-compose --version | cut -d' ' -f3 | sed 's/,//')
        print_success "Docker Compose $COMPOSE_VERSION"
        DOCKER_COMPOSE_CMD="docker-compose"
    fi
else
    print_error "Docker Compose is not installed."
    exit 1
fi

# Check Supabase CLI
if command_exists supabase; then
    SUPABASE_VERSION=$(supabase --version)
    print_success "Supabase CLI $SUPABASE_VERSION"
else
    print_warning "Supabase CLI is not installed."
    print_info "Installing Supabase CLI globally..."
    npm install -g supabase
    if command_exists supabase; then
        SUPABASE_VERSION=$(supabase --version)
        print_success "Supabase CLI $SUPABASE_VERSION installed"
    else
        print_error "Failed to install Supabase CLI"
        exit 1
    fi
fi

# =============================================================================
# Project Setup
# =============================================================================

print_header "🏗️  Setting Up Project"

# Navigate to script directory
cd "$SCRIPT_DIR"

# Install dependencies
print_info "Installing npm dependencies..."
npm install
print_success "Dependencies installed"

# Build project
print_info "Building project..."
npm run build
print_success "Project built successfully"

# =============================================================================
# Supabase Setup
# =============================================================================

print_header "🗄️  Setting Up Local Supabase"

# Check if already in a Supabase project
if [ -f "../../supabase/config.toml" ]; then
    print_success "Found existing Supabase configuration"
    SUPABASE_DIR="../../"
else
    print_warning "No Supabase configuration found"
    echo "Do you want to initialize Supabase in the parent directory? (y/n)"
    read -r INIT_SUPABASE
    
    if [ "$INIT_SUPABASE" = "y" ] || [ "$INIT_SUPABASE" = "Y" ]; then
        cd ../..
        print_info "Initializing Supabase..."
        supabase init
        print_success "Supabase initialized"
        SUPABASE_DIR="."
        cd "$SCRIPT_DIR"
    else
        print_info "Please run 'supabase init' in your project directory first"
        exit 1
    fi
fi

# Start Supabase
print_info "Starting Supabase services..."
cd "$SUPABASE_DIR"

# Check if already running
if supabase status >/dev/null 2>&1; then
    print_success "Supabase is already running"
else
    print_info "Starting Supabase (this may take a few minutes on first run)..."
    supabase start
    print_success "Supabase started successfully"
fi

# Get status and keys
print_info "Getting Supabase configuration..."
SUPABASE_STATUS=$(supabase status)
echo "$SUPABASE_STATUS"

# Extract keys (this is a simple extraction, may need adjustment based on CLI output format)
SUPABASE_URL=$(echo "$SUPABASE_STATUS" | grep "API URL" | awk '{print $3}')
ANON_KEY=$(echo "$SUPABASE_STATUS" | grep "anon key" | awk '{print $3}')
SERVICE_ROLE_KEY=$(echo "$SUPABASE_STATUS" | grep "service_role key" | awk '{print $3}')

cd "$SCRIPT_DIR"

# =============================================================================
# Environment Configuration
# =============================================================================

print_header "⚙️  Configuring Environment"

# Create .env file
if [ -f ".env" ]; then
    print_warning ".env file already exists"
    echo "Do you want to overwrite it? (y/n)"
    read -r OVERWRITE_ENV
    
    if [ "$OVERWRITE_ENV" != "y" ] && [ "$OVERWRITE_ENV" != "Y" ]; then
        print_info "Keeping existing .env file"
        print_info "Please manually update your .env file with the values from 'supabase status'"
    else
        SHOULD_CREATE_ENV=true
    fi
else
    SHOULD_CREATE_ENV=true
fi

if [ "$SHOULD_CREATE_ENV" = true ]; then
    print_info "Creating .env file..."
    
    # Copy from .env.example and replace values
    cp .env.example .env
    
    # Replace values if we extracted them successfully
    if [ -n "$SUPABASE_URL" ] && [ -n "$ANON_KEY" ] && [ -n "$SERVICE_ROLE_KEY" ]; then
        sed -i.bak "s|SUPABASE_URL=.*|SUPABASE_URL=$SUPABASE_URL|" .env
        sed -i.bak "s|SUPABASE_ANON_KEY=.*|SUPABASE_ANON_KEY=$ANON_KEY|" .env
        sed -i.bak "s|SUPABASE_SERVICE_ROLE_KEY=.*|SUPABASE_SERVICE_ROLE_KEY=$SERVICE_ROLE_KEY|" .env
        rm .env.bak
        print_success ".env file created with auto-detected values"
    else
        print_warning ".env file created from template"
        print_info "Please manually update the keys in .env with values from 'supabase status'"
    fi
fi

# =============================================================================
# Testing
# =============================================================================

print_header "🧪 Testing Connection"

print_info "Testing MCP server connection..."

# Test build
if [ -f "dist/transports/stdio.js" ]; then
    print_success "Built files found"
else
    print_error "Built files not found. Build may have failed."
    exit 1
fi

# Test with a simple connection check
print_info "Testing local Supabase connection..."
if timeout 10s node dist/transports/stdio.js --help >/dev/null 2>&1; then
    print_success "MCP server executable works"
else
    print_warning "Could not test MCP server (this might be normal)"
fi

# =============================================================================
# Final Instructions
# =============================================================================

print_header "🎉 Setup Complete!"

echo
print_success "Local Supabase MCP Server is ready!"
echo
print_info "Next steps:"
echo "  1. Verify your .env file has the correct values:"
echo "     cat .env"
echo
echo "  2. Test the MCP server:"
echo "     npm run dev:local"
echo
echo "  3. Configure your MCP client (Claude Desktop, Cline, Cursor):"
echo "     See examples/ directory for configuration samples"
echo
echo "  4. Access Supabase Studio:"
echo "     Open https://devdb.syncrobit.net/studio in your browser"
echo

print_info "Useful commands:"
echo "  supabase status          - Check service status"
echo "  supabase stop           - Stop all services"
echo "  supabase start          - Start all services"
echo "  supabase logs           - View logs"
echo "  docker compose ps       - Check containers"
echo

print_info "Configuration files created:"
echo "  .env                    - Environment variables"
echo "  dist/                   - Built MCP server"
echo

if [ -n "$SUPABASE_URL" ]; then
    print_info "Your local Supabase instance:"
    echo "  URL: $SUPABASE_URL"
    echo "  Studio: https://devdb.syncrobit.net/studio"
    echo "  Database: **************************************************/postgres"
fi

echo
print_success "Setup complete! Happy coding! 🚀"