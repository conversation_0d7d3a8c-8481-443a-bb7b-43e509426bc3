# Migration Guide: From Cloud to Local Supabase MCP Server

This document outlines the migration from the cloud-based Supabase MCP server to this local-only implementation, including feature changes, migration steps, and compatibility considerations.

## 📋 Overview

The local Supabase MCP server is a specialized version designed exclusively for local self-hosted Supabase instances. While maintaining the core MCP architecture, it has been adapted to work with local Docker containers instead of the Supabase Management API.

## 🔄 Key Differences

### ❌ Removed Features (Cloud-Only)

These features are not available in the local version as they require the Supabase Management API:

#### Project Management
- **`list_projects`** - Cannot list multiple projects (local = single instance)
- **`create_project`** - Cannot create new projects via API
- **`pause_project`** - Cannot pause/resume projects
- **`restore_project`** - Cannot restore projects from backups

#### Organization Management  
- **`list_organizations`** - No organization concept in local instances
- **`get_organization`** - No organization details available

#### Branching Operations
- **`list_branches`** - Database branching not supported locally
- **`create_branch`** - Cannot create database branches
- **`delete_branch`** - Cannot delete database branches
- **`merge_branch`** - Cannot merge database branches
- **`reset_branch`** - Cannot reset database branches
- **`rebase_branch`** - Cannot rebase database branches

#### Cloud-Specific Debugging
- **`get_logs`** - Cloud logging not available (use Docker logs instead)
- **`get_security_advisors`** - Cloud security analysis not available
- **`get_performance_advisors`** - Cloud performance analysis not available

#### Cost and Billing
- **Cost confirmation tools** - No billing in local instances

### ✅ Enhanced Local Features

#### New Local Development Tools
- **`check_local_connection`** - Verify local Supabase connectivity
- **`get_local_config`** - Display local configuration and URLs
- **`supabase_status`** - Instructions for checking service status
- **`list_docker_services`** - List running Supabase containers
- **`create_migration`** - Enhanced migration creation workflow
- **`reset_database`** - Safe database reset instructions

#### Enhanced Data Inspection
- **`list_auth_users`** - Direct auth table queries
- **`list_storage_buckets`** - Direct storage table queries
- **`inspect_table_schema`** - Detailed table schema analysis
- **`explain_query`** - PostgreSQL query performance analysis

### 🔧 Modified Behavior

#### Authentication
- **Cloud**: Uses personal access tokens and Management API
- **Local**: Uses API keys (anon + service_role) from local instance

#### Connection Management
- **Cloud**: Connects to cloud-hosted databases via API
- **Local**: Direct connection to local PostgreSQL instance

#### Error Handling
- **Cloud**: Cloud-specific error messages and API responses
- **Local**: Local-specific troubleshooting with Docker/CLI guidance

#### Configuration
- **Cloud**: Project references and organization IDs
- **Local**: Single "local" project with environment variables

## 🚀 Migration Steps

### Step 1: Backup Existing Configuration

If you're currently using the cloud version, backup your configuration:

```bash
# Backup your current MCP client configuration
cp ~/.config/Claude/claude_desktop_config.json ~/.config/Claude/claude_desktop_config.json.backup
```

### Step 2: Set Up Local Supabase

```bash
# Initialize and start local Supabase
supabase init
supabase start

# Get configuration details
supabase status
```

### Step 3: Install Local MCP Server

```bash
# Clone this repository
git clone https://github.com/supabase-community/supabase-mcp.git
cd supabase-mcp/packages/mcp-server-supabase

# Install and build
npm install
npm run build

# Configure environment
cp .env.example .env
# Edit .env with your local Supabase details
```

### Step 4: Update MCP Client Configuration

Replace your cloud configuration with local configuration:

```json
{
  "mcpServers": {
    "supabase-local": {
      "command": "node",
      "args": [
        "/path/to/packages/mcp-server-supabase/dist/transports/stdio.js"
      ],
      "env": {
        "SUPABASE_URL": "https://devdb.syncrobit.net",
        "SUPABASE_ANON_KEY": "your-local-anon-key",
        "SUPABASE_SERVICE_ROLE_KEY": "your-local-service-role-key"
      }
    }
  }
}
```

### Step 5: Test Migration

```bash
# Test the connection
npm run dev:local

# Verify tools are available in your MCP client
# Try: check_local_connection
```

## 🔗 Tool Mapping

### Direct Replacements

| Cloud Tool | Local Tool | Notes |
|------------|-------------|-------|
| `execute_sql` | `execute_sql` | ✅ Same functionality |
| `list_tables` | `list_tables` | ✅ Same functionality |
| `list_extensions` | `list_extensions` | ✅ Same functionality |
| `apply_migration` | `apply_migration` | ✅ Same functionality |
| `list_migrations` | `list_migrations` | ✅ Same functionality |
| `list_edge_functions` | `list_edge_functions` | ✅ Adapted for local |
| `get_edge_function` | `get_edge_function` | ✅ Adapted for local |

### New Local Alternatives

| Removed Cloud Tool | Local Alternative | Workflow |
|-------------------|-------------------|----------|
| `get_logs` | Docker commands | `docker compose logs supabase-db` |
| `list_projects` | Single local instance | Use `get_local_config` |
| `create_project` | Supabase CLI | `supabase init` + `supabase start` |
| `pause_project` | Docker commands | `docker compose stop` |
| Cloud security advisors | Manual analysis | Use `inspect_table_schema` + SQL queries |

### Enhanced Local Tools

| Base Tool | Enhanced Version | New Features |
|-----------|------------------|--------------|
| Connection testing | `check_local_connection` | Local-specific troubleshooting |
| Configuration | `get_local_config` | Docker URLs, CLI commands |
| Migration workflow | `create_migration` | Local CLI integration |

## 🛠️ Development Workflow Changes

### Before (Cloud)
```bash
# Cloud workflow
supabase login
supabase projects list
supabase link --project-ref your-project-ref
supabase db push
```

### After (Local)
```bash
# Local workflow  
supabase init
supabase start
supabase db reset  # if needed
supabase migration new add_feature
# Edit migration file
supabase db push  # applies to local instance
```

## 📊 Feature Comparison Matrix

| Feature Category | Cloud Version | Local Version | Notes |
|-----------------|---------------|---------------|-------|
| **Database Operations** | ✅ Full | ✅ Full | Same functionality |
| **SQL Execution** | ✅ Via API | ✅ Direct connection | Better performance locally |
| **Migrations** | ✅ Cloud managed | ✅ CLI managed | Local file-based |
| **Edge Functions** | ✅ Cloud deployed | ✅ Local functions | Local development only |
| **Project Management** | ✅ Multi-project | ❌ Single instance | Local = one project |
| **Organization Management** | ✅ Full | ❌ Not applicable | No organizations locally |
| **Branching** | ✅ Database branches | ❌ Not supported | Use Git for code branching |
| **Logging** | ✅ Cloud logs | ✅ Docker logs | Different log sources |
| **Monitoring** | ✅ Cloud metrics | ✅ Local tools | Docker stats, local monitoring |

## 🚨 Breaking Changes

### Configuration Structure
- **Cloud**: Required `projectId` and `accessToken`
- **Local**: Requires `SUPABASE_URL`, `SUPABASE_ANON_KEY`, `SUPABASE_SERVICE_ROLE_KEY`

### Error Messages
- **Cloud**: API error codes and cloud-specific guidance
- **Local**: Docker and CLI-specific troubleshooting steps

### Tool Behavior
- **Cloud**: Multi-project context switching
- **Local**: Single project context (projectId = "local")

## 🔄 Rollback Strategy

If you need to revert to the cloud version:

1. **Restore Configuration**:
   ```bash
   cp ~/.config/Claude/claude_desktop_config.json.backup ~/.config/Claude/claude_desktop_config.json
   ```

2. **Switch Back to Cloud**:
   ```bash
   # Use the original cloud MCP server
   npm install @supabase/mcp-server-supabase@cloud-version
   ```

3. **Update Environment**:
   - Set cloud access tokens
   - Remove local environment variables

## 🤝 Dual Setup (Recommended)

You can run both cloud and local versions simultaneously:

```json
{
  "mcpServers": {
    "supabase-cloud": {
      "command": "npx",
      "args": ["@supabase/mcp-server-supabase", "--project-ref", "your-project"],
      "env": {
        "SUPABASE_ACCESS_TOKEN": "your-cloud-token"
      }
    },
    "supabase-local": {
      "command": "node",
      "args": ["/path/to/local/mcp-server-supabase/dist/transports/stdio.js"],
      "env": {
        "SUPABASE_URL": "https://devdb.syncrobit.net",
        "SUPABASE_ANON_KEY": "your-local-anon-key",
        "SUPABASE_SERVICE_ROLE_KEY": "your-local-service-role-key"
      }
    }
  }
}
```

## 📚 Additional Resources

- [Local Development Setup Guide](./README.md)
- [Client Configuration Examples](./examples/README.md)
- [Troubleshooting Guide](./README.md#-troubleshooting)
- [Supabase Local Development Docs](https://supabase.com/docs/guides/local-development)

## 🆘 Migration Support

If you encounter issues during migration:

1. **Check Compatibility**: Ensure your workflow doesn't rely on removed features
2. **Update Scripts**: Modify any scripts that used cloud-specific tools
3. **Test Thoroughly**: Verify all your use cases work with local tools
4. **Community Help**: Join the [Supabase Discord](https://discord.supabase.com) for support

## 🔮 Future Considerations

### Potential Enhancements
- **Hybrid Mode**: Support for both cloud and local in one server
- **Migration Tools**: Automated cloud-to-local migration utilities
- **Enhanced Monitoring**: Better local monitoring and alerting tools
- **Backup Integration**: Local backup and restore capabilities

### Compatibility
This local implementation will be maintained to stay compatible with:
- Latest Supabase CLI versions
- Current MCP protocol specifications
- Local Supabase Docker images

---

**Need help with migration?** Check the [examples](./examples/) directory for ready-to-use configurations or refer to the [troubleshooting guide](./README.md#-troubleshooting).