@echo off
REM =============================================================================
REM Local Supabase MCP Server Setup Script (Windows)
REM =============================================================================
REM This script automates the setup process for the local Supabase MCP server
REM Run with: setup-local.bat

setlocal enabledelayedexpansion

REM Colors for output (using echo with color codes)
set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "NC=[0m"

echo %BLUE%=============================================================================
echo 🚀 Local Supabase MCP Server Setup (Windows)
echo =============================================================================%NC%

REM =============================================================================
REM Prerequisites Check
REM =============================================================================

echo.
echo %BLUE%=============================================================================
echo 📋 Checking Prerequisites
echo =============================================================================%NC%

REM Check Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo %RED%❌ Node.js is not installed. Please install Node.js 18 or higher.%NC%
    echo Visit: https://nodejs.org/
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
echo %GREEN%✅ Node.js %NODE_VERSION%%NC%

REM Check npm
npm --version >nul 2>&1
if errorlevel 1 (
    echo %RED%❌ npm is not installed.%NC%
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i
echo %GREEN%✅ npm %NPM_VERSION%%NC%

REM Check Docker
docker --version >nul 2>&1
if errorlevel 1 (
    echo %RED%❌ Docker is not installed. Please install Docker Desktop.%NC%
    echo Visit: https://docs.docker.com/desktop/install/windows-install/
    pause
    exit /b 1
)

for /f "tokens=3" %%i in ('docker --version') do set DOCKER_VERSION=%%i
echo %GREEN%✅ Docker %DOCKER_VERSION%%NC%

REM Check if Docker is running
docker info >nul 2>&1
if errorlevel 1 (
    echo %RED%❌ Docker daemon is not running. Please start Docker Desktop.%NC%
    pause
    exit /b 1
)
echo %GREEN%✅ Docker daemon is running%NC%

REM Check Docker Compose
docker compose version >nul 2>&1
if errorlevel 1 (
    docker-compose --version >nul 2>&1
    if errorlevel 1 (
        echo %RED%❌ Docker Compose is not installed.%NC%
        pause
        exit /b 1
    ) else (
        for /f "tokens=3" %%i in ('docker-compose --version') do set COMPOSE_VERSION=%%i
        echo %GREEN%✅ Docker Compose %COMPOSE_VERSION%%NC%
        set DOCKER_COMPOSE_CMD=docker-compose
    )
) else (
    for /f "tokens=4" %%i in ('docker compose version') do set COMPOSE_VERSION=%%i
    echo %GREEN%✅ Docker Compose %COMPOSE_VERSION%%NC%
    set DOCKER_COMPOSE_CMD=docker compose
)

REM Check Supabase CLI
supabase --version >nul 2>&1
if errorlevel 1 (
    echo %YELLOW%⚠️ Supabase CLI is not installed.%NC%
    echo Installing Supabase CLI globally...
    npm install -g supabase
    supabase --version >nul 2>&1
    if errorlevel 1 (
        echo %RED%❌ Failed to install Supabase CLI%NC%
        pause
        exit /b 1
    )
)

for /f "tokens=*" %%i in ('supabase --version') do set SUPABASE_VERSION=%%i
echo %GREEN%✅ Supabase CLI %SUPABASE_VERSION%%NC%

REM =============================================================================
REM Project Setup
REM =============================================================================

echo.
echo %BLUE%=============================================================================
echo 🏗️ Setting Up Project
echo =============================================================================%NC%

echo Installing npm dependencies...
npm install
if errorlevel 1 (
    echo %RED%❌ Failed to install dependencies%NC%
    pause
    exit /b 1
)
echo %GREEN%✅ Dependencies installed%NC%

echo Building project...
npm run build
if errorlevel 1 (
    echo %RED%❌ Failed to build project%NC%
    pause
    exit /b 1
)
echo %GREEN%✅ Project built successfully%NC%

REM =============================================================================
REM Supabase Setup
REM =============================================================================

echo.
echo %BLUE%=============================================================================
echo 🗄️ Setting Up Local Supabase
echo =============================================================================%NC%

REM Check if already in a Supabase project
if exist "..\..\supabase\config.toml" (
    echo %GREEN%✅ Found existing Supabase configuration%NC%
    set SUPABASE_DIR=..\..
) else (
    echo %YELLOW%⚠️ No Supabase configuration found%NC%
    set /p INIT_SUPABASE="Do you want to initialize Supabase in the parent directory? (y/n): "
    
    if /i "!INIT_SUPABASE!"=="y" (
        cd ..\..
        echo Initializing Supabase...
        supabase init
        if errorlevel 1 (
            echo %RED%❌ Failed to initialize Supabase%NC%
            pause
            exit /b 1
        )
        echo %GREEN%✅ Supabase initialized%NC%
        set SUPABASE_DIR=.
        cd packages\mcp-server-supabase
    ) else (
        echo Please run 'supabase init' in your project directory first
        pause
        exit /b 1
    )
)

REM Start Supabase
echo Starting Supabase services...
cd "%SUPABASE_DIR%"

REM Check if already running
supabase status >nul 2>&1
if errorlevel 1 (
    echo Starting Supabase ^(this may take a few minutes on first run^)...
    supabase start
    if errorlevel 1 (
        echo %RED%❌ Failed to start Supabase%NC%
        cd packages\mcp-server-supabase
        pause
        exit /b 1
    )
    echo %GREEN%✅ Supabase started successfully%NC%
) else (
    echo %GREEN%✅ Supabase is already running%NC%
)

REM Get status and display it
echo Getting Supabase configuration...
supabase status

cd packages\mcp-server-supabase

REM =============================================================================
REM Environment Configuration
REM =============================================================================

echo.
echo %BLUE%=============================================================================
echo ⚙️ Configuring Environment
echo =============================================================================%NC%

REM Create .env file
if exist ".env" (
    echo %YELLOW%⚠️ .env file already exists%NC%
    set /p OVERWRITE_ENV="Do you want to overwrite it? (y/n): "
    
    if /i "!OVERWRITE_ENV!"=="y" (
        set SHOULD_CREATE_ENV=true
    ) else (
        echo Keeping existing .env file
        echo Please manually update your .env file with the values from 'supabase status'
        set SHOULD_CREATE_ENV=false
    )
) else (
    set SHOULD_CREATE_ENV=true
)

if "!SHOULD_CREATE_ENV!"=="true" (
    echo Creating .env file...
    copy .env.example .env
    echo %GREEN%✅ .env file created from template%NC%
    echo %YELLOW%Please manually update the keys in .env with values from 'supabase status'%NC%
)

REM =============================================================================
REM Testing
REM =============================================================================

echo.
echo %BLUE%=============================================================================
echo 🧪 Testing Connection
echo =============================================================================%NC%

REM Test build
if exist "dist\transports\stdio.js" (
    echo %GREEN%✅ Built files found%NC%
) else (
    echo %RED%❌ Built files not found. Build may have failed.%NC%
    pause
    exit /b 1
)

REM Test with a simple connection check
echo Testing MCP server executable...
node dist\transports\stdio.js --help >nul 2>&1
if errorlevel 1 (
    echo %YELLOW%⚠️ Could not test MCP server ^(this might be normal^)%NC%
) else (
    echo %GREEN%✅ MCP server executable works%NC%
)

REM =============================================================================
REM Final Instructions
REM =============================================================================

echo.
echo %BLUE%=============================================================================
echo 🎉 Setup Complete!
echo =============================================================================%NC%

echo.
echo %GREEN%✅ Local Supabase MCP Server is ready!%NC%
echo.
echo Next steps:
echo   1. Verify your .env file has the correct values:
echo      type .env
echo.
echo   2. Test the MCP server:
echo      npm run dev:local
echo.
echo   3. Configure your MCP client ^(Claude Desktop, Cline, Cursor^):
echo      See examples\ directory for configuration samples
echo.
echo   4. Access Supabase Studio:
echo      Open https://devdb.syncrobit.net/studio in your browser
echo.
echo Useful commands:
echo   supabase status          - Check service status
echo   supabase stop           - Stop all services
echo   supabase start          - Start all services
echo   supabase logs           - View logs
echo   docker compose ps       - Check containers
echo.
echo Configuration files created:
echo   .env                    - Environment variables
echo   dist\                   - Built MCP server
echo.
echo %GREEN%Setup complete! Happy coding! 🚀%NC%
echo.
pause