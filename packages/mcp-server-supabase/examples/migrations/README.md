# Migration Examples and Best Practices

This directory contains example migrations that demonstrate best practices for database schema management using the Supabase MCP server.

## Example Migrations

### 001_create_users_table.sql
**Template**: CREATE_TABLE  
**Demonstrates**:
- Idempotent table creation with `IF NOT EXISTS`
- Standard audit fields (`created_at`, `updated_at`)
- Appropriate data types and constraints
- Performance indexes for commonly queried fields
- Email format validation
- Automatic `updated_at` trigger

### 002_create_posts_table.sql
**Template**: CREATE_TABLE  
**Demonstrates**:
- Foreign key relationships with proper constraints
- ENUM types for controlled values
- JSONB for flexible metadata storage
- Array fields for tags
- Full-text search indexes
- Soft delete pattern
- Complex check constraints

### 003_alter_users_add_profile_fields.sql
**Template**: ALTER_TABLE  
**Demonstrates**:
- Safe column additions with `IF NOT EXISTS`
- Adding constraints after column creation
- Updating existing data with sensible defaults
- Creating indexes for new searchable fields
- Data validation constraints

### 004_create_performance_indexes.sql
**Template**: CREATE_INDEX  
**Demonstrates**:
- Concurrent index creation for production safety
- Composite indexes for multi-column queries
- Partial indexes for filtered queries
- Covering indexes for query optimization
- Expression indexes for case-insensitive searches

### 005_data_migration_user_cleanup.sql
**Template**: DATA_MIGRATION  
**Demonstrates**:
- Transaction-based data consistency
- Audit trail creation for changes
- Complex data transformations with CTEs
- Handling edge cases and duplicates
- Data normalization techniques

## Best Practices Demonstrated

### 1. Idempotent Operations
All migrations use `IF NOT EXISTS` or `IF EXISTS` to ensure they can be run multiple times safely.

### 2. Performance Considerations
- Use `CONCURRENTLY` for index creation in production
- Create indexes for foreign keys and commonly queried columns
- Use partial indexes to reduce index size
- Consider covering indexes for read-heavy queries

### 3. Data Integrity
- Add appropriate constraints and validations
- Use foreign keys to maintain referential integrity
- Implement check constraints for business rules
- Use transactions for multi-step data changes

### 4. Audit and Tracking
- Include standard audit fields (`created_at`, `updated_at`)
- Create audit tables for tracking data changes
- Log migration activities for troubleshooting

### 5. Rollback Strategy
- Always include commented rollback statements
- Consider the reversibility of data migrations
- Document any irreversible changes

### 6. Schema Evolution
- Use ENUM types for controlled vocabularies
- Design for extensibility with JSONB metadata
- Plan for soft deletes when appropriate
- Consider timezone handling from the start

## Using These Examples

### With the CLI
```bash
# Create a new migration based on these examples
mcp-supabase migrate:create create_users_table --template table --table-name users

# Run migrations
mcp-supabase migrate:up

# Check status
mcp-supabase migrate:status
```

### Customizing Templates
The migration wrapper supports template variables:
- `{tableName}` - Table name
- `{columnName}` - Column name  
- `{columnType}` - Column data type
- `{indexName}` - Index name
- `{description}` - Migration description

### Testing Migrations
Always test migrations on a copy of production data:
1. Create a database backup
2. Run migrations on a test environment
3. Verify data integrity and performance
4. Test rollback procedures if needed

## Migration Naming Convention

Follow the pattern: `{timestamp}_{descriptive_name}.sql`
- Use timestamps for ordering (YYYYMMDDHHMMSS)
- Use descriptive names that explain the change
- Use underscores to separate words
- Keep names concise but clear

Examples:
- `20240101120000_create_users_table.sql`
- `20240101130000_add_user_profile_fields.sql`
- `20240101140000_create_posts_indexes.sql`

## Common Pitfalls to Avoid

1. **Not using transactions** for multi-step data migrations
2. **Missing rollback statements** or incomplete rollback logic
3. **Creating indexes without CONCURRENTLY** in production
4. **Not validating data** before applying constraints
5. **Forgetting to update related indexes** when adding columns
6. **Not considering query performance** impact of new indexes
7. **Missing foreign key constraints** for referential integrity
8. **Not handling NULL values** properly in data migrations

## Production Deployment Checklist

- [ ] Migration tested on staging environment
- [ ] Rollback procedure documented and tested
- [ ] Performance impact assessed
- [ ] Downtime requirements communicated
- [ ] Backup created before deployment
- [ ] Monitoring in place for post-deployment verification
