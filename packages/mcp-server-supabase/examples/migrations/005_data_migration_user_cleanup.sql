-- Migration: Data migration - user cleanup
-- Description: Clean up inactive users and normalize data
-- Template: DATA_MIGRATION
-- Best Practices Demonstrated:
--   - Use transactions for data consistency
--   - Backup critical data before changes
--   - Use CTEs for complex data transformations
--   - Log changes for audit trail
--   - Handle edge cases and validation

-- Up migration

-- Create audit table for tracking changes
CREATE TABLE IF NOT EXISTS user_cleanup_audit (
  id SERIAL PRIMARY KEY,
  user_id UUID,
  action VARCHAR(50),
  old_values JSONB,
  new_values JSONB,
  reason TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Begin transaction for data consistency
BEGIN;

-- 1. Archive users who haven't logged in for over 2 years
WITH inactive_users AS (
  SELECT id, email, username, last_login_at
  FROM users 
  WHERE last_login_at < (NOW() - INTERVAL '2 years')
    AND is_active = true
),
archived_users AS (
  UPDATE users 
  SET is_active = false,
      updated_at = NOW()
  FROM inactive_users
  WHERE users.id = inactive_users.id
  RETURNING users.id, users.email, users.username, inactive_users.last_login_at
)
INSERT INTO user_cleanup_audit (user_id, action, old_values, reason)
SELECT 
  id,
  'archive_inactive',
  jsonb_build_object(
    'email', email,
    'username', username,
    'last_login_at', last_login_at,
    'was_active', true
  ),
  'User inactive for over 2 years'
FROM archived_users;

-- 2. Normalize email addresses (convert to lowercase)
WITH email_updates AS (
  UPDATE users 
  SET email = lower(email),
      updated_at = NOW()
  WHERE email != lower(email)
  RETURNING id, email
)
INSERT INTO user_cleanup_audit (user_id, action, new_values, reason)
SELECT 
  id,
  'normalize_email',
  jsonb_build_object('email', email),
  'Converted email to lowercase'
FROM email_updates;

-- 3. Clean up duplicate usernames (add suffix to duplicates)
WITH duplicate_usernames AS (
  SELECT 
    username,
    array_agg(id ORDER BY created_at) as user_ids,
    count(*) as duplicate_count
  FROM users 
  WHERE is_active = true
  GROUP BY lower(username)
  HAVING count(*) > 1
),
username_updates AS (
  UPDATE users 
  SET username = username || '_' || (row_number() OVER (PARTITION BY lower(username) ORDER BY created_at) - 1),
      updated_at = NOW()
  FROM duplicate_usernames
  WHERE users.id = ANY(duplicate_usernames.user_ids[2:])
  RETURNING users.id, users.username
)
INSERT INTO user_cleanup_audit (user_id, action, new_values, reason)
SELECT 
  id,
  'resolve_duplicate_username',
  jsonb_build_object('username', username),
  'Added suffix to resolve duplicate username'
FROM username_updates;

-- 4. Set default timezone for users without one
WITH timezone_updates AS (
  UPDATE users 
  SET timezone = 'UTC',
      updated_at = NOW()
  WHERE timezone IS NULL
  RETURNING id
)
INSERT INTO user_cleanup_audit (user_id, action, new_values, reason)
SELECT 
  id,
  'set_default_timezone',
  jsonb_build_object('timezone', 'UTC'),
  'Set default timezone for user'
FROM timezone_updates;

-- 5. Update notification preferences for users with old format
WITH notification_updates AS (
  UPDATE users 
  SET notification_preferences = jsonb_build_object(
    'email', COALESCE((notification_preferences->>'email')::boolean, true),
    'push', COALESCE((notification_preferences->>'push')::boolean, true),
    'sms', COALESCE((notification_preferences->>'sms')::boolean, false),
    'marketing', false,
    'security', true
  ),
  updated_at = NOW()
  WHERE notification_preferences IS NULL 
     OR NOT notification_preferences ? 'marketing'
     OR NOT notification_preferences ? 'security'
  RETURNING id, notification_preferences
)
INSERT INTO user_cleanup_audit (user_id, action, new_values, reason)
SELECT 
  id,
  'update_notification_preferences',
  jsonb_build_object('notification_preferences', notification_preferences),
  'Updated notification preferences to new format'
FROM notification_updates;

COMMIT;

-- Create summary view of cleanup results
CREATE OR REPLACE VIEW user_cleanup_summary AS
SELECT 
  action,
  count(*) as affected_users,
  min(created_at) as first_change,
  max(created_at) as last_change
FROM user_cleanup_audit
GROUP BY action
ORDER BY max(created_at) DESC;

-- Down migration
-- Note: Data migrations are often irreversible, but we can provide guidance
-- 
-- To reverse this migration:
-- 1. Restore users from backup if needed
-- 2. DROP VIEW IF EXISTS user_cleanup_summary;
-- 3. DROP TABLE IF EXISTS user_cleanup_audit;
-- 
-- WARNING: This will lose audit trail of changes made during cleanup
