/**
 * MCP CRUD Tools Usage Examples
 * 
 * This file contains practical examples of using the Supabase MCP CRUD tools.
 * Each example demonstrates different features and use cases.
 */

import { MCPClient } from '@modelcontextprotocol/client';

// Initialize MCP client
const client = new MCPClient();

/**
 * Example 1: Basic CRUD Operations
 */
async function basicCrudExample() {
  console.log('=== Basic CRUD Operations ===');
  
  const projectId = 'your-project-id';
  
  try {
    // CREATE a new user
    const createResult = await client.callTool({
      name: 'create_record',
      arguments: {
        project_id: projectId,
        schema: 'public',
        table: 'users',
        data: {
          name: '<PERSON>',
          email: '<EMAIL>',
          age: 30,
          status: 'active'
        },
        returning: ['id', 'name', 'created_at']
      }
    });
    
    console.log('Created user:', createResult.content[0].data[0]);
    const userId = createResult.content[0].data[0].id;
    
    // READ users with filtering
    const readResult = await client.callTool({
      name: 'read_records',
      arguments: {
        project_id: projectId,
        schema: 'public',
        table: 'users',
        columns: ['id', 'name', 'email', 'status'],
        where: {
          column: 'status',
          operator: '=',
          value: 'active'
        },
        orderBy: [
          { column: 'name', direction: 'ASC' }
        ],
        limit: 10
      }
    });
    
    console.log('Active users:', readResult.content[0].data);
    
    // UPDATE user status
    const updateResult = await client.callTool({
      name: 'update_records',
      arguments: {
        project_id: projectId,
        schema: 'public',
        table: 'users',
        data: {
          status: 'verified',
          updated_at: new Date().toISOString()
        },
        where: {
          column: 'id',
          operator: '=',
          value: userId
        },
        returning: ['id', 'status', 'updated_at']
      }
    });
    
    console.log('Updated user:', updateResult.content[0].data[0]);
    
    // DELETE inactive users (example - be careful!)
    const deleteResult = await client.callTool({
      name: 'delete_records',
      arguments: {
        project_id: projectId,
        schema: 'public',
        table: 'users',
        where: {
          column: 'status',
          operator: '=',
          value: 'inactive'
        },
        returning: ['id', 'name']
      }
    });
    
    console.log('Deleted users:', deleteResult.content[0].data);
    
  } catch (error) {
    console.error('CRUD operation failed:', error);
  }
}

/**
 * Example 2: Complex WHERE Conditions
 */
async function complexQueryExample() {
  console.log('=== Complex WHERE Conditions ===');
  
  const projectId = 'your-project-id';
  
  try {
    // Query with complex conditions
    const result = await client.callTool({
      name: 'read_records',
      arguments: {
        project_id: projectId,
        schema: 'public',
        table: 'orders',
        columns: ['id', 'user_id', 'total', 'status', 'created_at'],
        where: {
          AND: [
            // Orders from last 30 days
            {
              column: 'created_at',
              operator: '>=',
              value: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()
            },
            // High value orders
            {
              column: 'total',
              operator: '>=',
              value: 100.00
            },
            // Completed or shipped status
            {
              OR: [
                { column: 'status', operator: '=', value: 'completed' },
                { column: 'status', operator: '=', value: 'shipped' }
              ]
            },
            // Exclude refunded orders
            {
              NOT: {
                column: 'status',
                operator: '=',
                value: 'refunded'
              }
            }
          ]
        },
        orderBy: [
          { column: 'total', direction: 'DESC' },
          { column: 'created_at', direction: 'DESC' }
        ],
        limit: 50
      }
    });
    
    console.log('High-value recent orders:', result.content[0].data);
    
  } catch (error) {
    console.error('Complex query failed:', error);
  }
}

/**
 * Example 3: Bulk Operations
 */
async function bulkOperationsExample() {
  console.log('=== Bulk Operations ===');
  
  const projectId = 'your-project-id';
  
  try {
    // Bulk create multiple users
    const users = [
      { name: 'Alice Smith', email: '<EMAIL>', department: 'Engineering' },
      { name: 'Bob Johnson', email: '<EMAIL>', department: 'Marketing' },
      { name: 'Carol Brown', email: '<EMAIL>', department: 'Sales' },
      { name: 'David Wilson', email: '<EMAIL>', department: 'Engineering' },
      { name: 'Eve Davis', email: '<EMAIL>', department: 'HR' }
    ];
    
    const bulkCreateResult = await client.callTool({
      name: 'bulk_create_records',
      arguments: {
        project_id: projectId,
        schema: 'public',
        table: 'employees',
        records: users,
        batchSize: 3,
        returning: ['id', 'name', 'department']
      }
    });
    
    console.log('Created employees:', bulkCreateResult.content[0].data);
    
    // Bulk update different records with different conditions
    const bulkUpdateResult = await client.callTool({
      name: 'bulk_update_records',
      arguments: {
        project_id: projectId,
        schema: 'public',
        table: 'employees',
        updates: [
          {
            data: { salary: 85000, level: 'Senior' },
            where: { column: 'department', operator: '=', value: 'Engineering' }
          },
          {
            data: { salary: 70000, level: 'Mid' },
            where: { column: 'department', operator: '=', value: 'Marketing' }
          },
          {
            data: { salary: 75000, level: 'Senior' },
            where: { column: 'department', operator: '=', value: 'Sales' }
          }
        ],
        returning: ['id', 'name', 'salary', 'level']
      }
    });
    
    console.log('Updated employees:', bulkUpdateResult.content[0].data);
    
  } catch (error) {
    console.error('Bulk operation failed:', error);
  }
}

/**
 * Example 4: Upsert Operation
 */
async function upsertExample() {
  console.log('=== Upsert Operations ===');
  
  const projectId = 'your-project-id';
  
  try {
    // Upsert user profile (insert if new, update if exists)
    const upsertResult = await client.callTool({
      name: 'upsert_record',
      arguments: {
        project_id: projectId,
        schema: 'public',
        table: 'user_profiles',
        data: {
          user_id: 123,
          bio: 'Software developer with 5 years experience',
          location: 'San Francisco, CA',
          website: 'https://johndoe.dev',
          updated_at: new Date().toISOString()
        },
        conflictColumns: ['user_id'],
        updateData: {
          bio: 'Senior software developer with 5 years experience',
          location: 'San Francisco, CA',
          website: 'https://johndoe.dev',
          updated_at: new Date().toISOString()
        },
        returning: ['user_id', 'bio', 'updated_at']
      }
    });
    
    console.log('Upserted profile:', upsertResult.content[0].data[0]);
    
  } catch (error) {
    console.error('Upsert operation failed:', error);
  }
}

/**
 * Example 5: Transaction Operations
 */
async function transactionExample() {
  console.log('=== Transaction Operations ===');
  
  const projectId = 'your-project-id';
  
  try {
    // Execute multiple operations as a single transaction
    const transactionResult = await client.callTool({
      name: 'execute_transaction',
      arguments: {
        project_id: projectId,
        operations: [
          // Create new order
          {
            type: 'CREATE',
            schema: 'public',
            table: 'orders',
            data: {
              user_id: 123,
              product_id: 456,
              quantity: 2,
              total: 99.98,
              status: 'pending'
            }
          },
          // Update product stock
          {
            type: 'UPDATE',
            schema: 'public',
            table: 'products',
            data: {
              stock: 'stock - 2' // Use SQL expression
            },
            where: {
              column: 'id',
              operator: '=',
              value: 456
            }
          },
          // Update user's last order date
          {
            type: 'UPDATE',
            schema: 'public',
            table: 'users',
            data: {
              last_order_at: new Date().toISOString()
            },
            where: {
              column: 'id',
              operator: '=',
              value: 123
            }
          },
          // Create audit log entry
          {
            type: 'CREATE',
            schema: 'public',
            table: 'audit_logs',
            data: {
              action: 'order_created',
              user_id: 123,
              details: JSON.stringify({
                order_total: 99.98,
                product_id: 456,
                quantity: 2
              }),
              created_at: new Date().toISOString()
            }
          }
        ],
        isolationLevel: 'READ_COMMITTED'
      }
    });
    
    console.log('Transaction completed:', transactionResult.content[0]);
    
  } catch (error) {
    console.error('Transaction failed:', error);
  }
}

/**
 * Example 6: JSON Column Operations
 */
async function jsonOperationsExample() {
  console.log('=== JSON Column Operations ===');
  
  const projectId = 'your-project-id';
  
  try {
    // Create record with JSON data
    await client.callTool({
      name: 'create_record',
      arguments: {
        project_id: projectId,
        schema: 'public',
        table: 'user_settings',
        data: {
          user_id: 123,
          preferences: {
            theme: 'dark',
            notifications: {
              email: true,
              push: false,
              sms: true
            },
            language: 'en'
          },
          tags: ['premium', 'early-adopter', 'beta-tester']
        }
      }
    });
    
    // Query using JSON operators
    const jsonQueryResult = await client.callTool({
      name: 'read_records',
      arguments: {
        project_id: projectId,
        schema: 'public',
        table: 'user_settings',
        where: {
          AND: [
            // Check if preferences contains specific key-value
            {
              column: 'preferences',
              operator: '@>',
              value: { theme: 'dark' }
            },
            // Check if tags array contains specific value
            {
              column: 'tags',
              operator: '?',
              value: 'premium'
            }
          ]
        }
      }
    });
    
    console.log('Users with dark theme and premium tag:', jsonQueryResult.content[0].data);
    
    // Update JSON field
    await client.callTool({
      name: 'update_records',
      arguments: {
        project_id: projectId,
        schema: 'public',
        table: 'user_settings',
        data: {
          preferences: {
            theme: 'light',
            notifications: {
              email: true,
              push: true,
              sms: false
            },
            language: 'en'
          }
        },
        where: {
          column: 'user_id',
          operator: '=',
          value: 123
        }
      }
    });
    
  } catch (error) {
    console.error('JSON operation failed:', error);
  }
}

/**
 * Example 7: Database Management
 */
async function databaseManagementExample() {
  console.log('=== Database Management ===');
  
  const projectId = 'your-project-id';
  
  try {
    // List all tables
    const tablesResult = await client.callTool({
      name: 'list_tables',
      arguments: {
        project_id: projectId,
        schema: 'public'
      }
    });
    
    console.log('Available tables:', tablesResult.content[0].data);
    
    // Execute raw SQL query
    const sqlResult = await client.callTool({
      name: 'execute_sql',
      arguments: {
        project_id: projectId,
        query: `
          SELECT 
            table_name,
            column_name,
            data_type,
            is_nullable
          FROM information_schema.columns 
          WHERE table_schema = 'public' 
            AND table_name = 'users'
          ORDER BY ordinal_position
        `,
        read_only: true
      }
    });
    
    console.log('User table structure:', sqlResult.content[0].data);
    
    // Get table statistics
    const statsResult = await client.callTool({
      name: 'execute_sql',
      arguments: {
        project_id: projectId,
        query: `
          SELECT 
            COUNT(*) as total_users,
            COUNT(CASE WHEN status = 'active' THEN 1 END) as active_users,
            COUNT(CASE WHEN status = 'inactive' THEN 1 END) as inactive_users,
            AVG(age) as average_age
          FROM users
        `,
        read_only: true
      }
    });
    
    console.log('User statistics:', statsResult.content[0].data[0]);
    
  } catch (error) {
    console.error('Database management operation failed:', error);
  }
}

/**
 * Example 8: Error Handling and Retry Logic
 */
async function errorHandlingExample() {
  console.log('=== Error Handling ===');
  
  const projectId = 'your-project-id';
  
  // Helper function with retry logic
  async function callWithRetry(toolName, args, maxRetries = 3) {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await client.callTool({ name: toolName, arguments: args });
      } catch (error) {
        console.log(`Attempt ${attempt} failed:`, error.message);
        
        if (attempt === maxRetries) {
          throw error;
        }
        
        // Wait before retry (exponential backoff)
        await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
      }
    }
  }
  
  try {
    // Attempt operation that might fail
    const result = await callWithRetry('create_record', {
      project_id: projectId,
      schema: 'public',
      table: 'users',
      data: {
        email: '<EMAIL>', // This might fail if email already exists
        name: 'Test User'
      }
    });
    
    console.log('Operation succeeded:', result.content[0].data[0]);
    
  } catch (error) {
    // Handle different error types
    if (error.data?.type === 'ValidationError') {
      console.error('Validation failed:', error.message);
    } else if (error.data?.type === 'DatabaseError') {
      console.error('Database error:', error.message);
      if (error.data?.context?.code === 'UNIQUE_VIOLATION') {
        console.log('Attempting upsert instead...');
        // Could implement fallback to upsert here
      }
    } else {
      console.error('Unexpected error:', error);
    }
  }
}

// Main execution function
async function runExamples() {
  try {
    await client.connect();
    console.log('Connected to MCP server');
    
    // Run all examples
    await basicCrudExample();
    await complexQueryExample();
    await bulkOperationsExample();
    await upsertExample();
    await transactionExample();
    await jsonOperationsExample();
    await databaseManagementExample();
    await errorHandlingExample();
    
  } catch (error) {
    console.error('Failed to run examples:', error);
  } finally {
    await client.disconnect();
    console.log('Disconnected from MCP server');
  }
}

// Run examples if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runExamples();
}

export {
  basicCrudExample,
  complexQueryExample,
  bulkOperationsExample,
  upsertExample,
  transactionExample,
  jsonOperationsExample,
  databaseManagementExample,
  errorHandlingExample
};