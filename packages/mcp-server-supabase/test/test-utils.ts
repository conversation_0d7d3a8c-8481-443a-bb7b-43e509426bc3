// Test utilities for mocking and testing CRUD operations

export interface MockPlatform {
  executeSql(projectId: string, options: { query: string; read_only?: boolean }): Promise<any[]>;
}

export function createMockPlatform(): MockPlatform {
  return {
    async executeSql(projectId: string, options: { query: string; read_only?: boolean }) {
      const { query } = options;
      
      console.log(`🔧 Mock SQL execution:`, query);
      
      // Simulate successful responses based on query type
      if (query.includes('SELECT')) {
        return [
          { id: 1, name: '<PERSON>', email: '<EMAIL>', age: 30 },
          { id: 2, name: '<PERSON>', email: '<EMAIL>', age: 25 }
        ];
      } else if (query.includes('INSERT') || query.includes('UPDATE') || query.includes('DELETE')) {
        return [
          { id: 1, name: '<PERSON>', email: '<EMAIL>', age: 30 }
        ];
      } else if (query.includes('BEGIN') || query.includes('COMMIT')) {
        // Transaction commands
        return [];
      }
      
      return [];
    }
  };
}

export function createFailingMockPlatform(): MockPlatform {
  return {
    async executeSql(projectId: string, options: { query: string; read_only?: boolean }) {
      throw new Error('Mock database error for testing error handling');
    }
  };
}