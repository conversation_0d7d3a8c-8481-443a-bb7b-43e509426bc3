import { describe, expect, test } from 'vitest';
import { z } from 'zod';
import { getDatabaseOperationTools } from '../src/tools/database-operation-tools.js';
import type { SupabasePlatform } from '../src/platform/types.js';

/**
 * Unit tests for Database Operation Tools validation
 * 
 * This test suite validates:
 * - Enhanced schema validation for database operations
 * - SQL injection protection
 * - Parameter bounds checking
 * - Error handling and reporting
 */

// Mock platform for testing
const mockPlatform: SupabasePlatform = {
  executeSql: async (projectId: string, options: { query: string; read_only?: boolean }) => {
    // Mock implementation for testing
    if (options.query.includes('ERROR')) {
      throw new Error('Mock SQL error');
    }
    return [{ mock: 'data' }];
  },
  listMigrations: async (projectId: string) => {
    return [{ name: 'test_migration', applied_at: new Date() }];
  },
  applyMigration: async (projectId: string, migration: { name: string; query: string }) => {
    if (migration.query.includes('ERROR')) {
      throw new Error('Mock migration error');
    }
    return { success: true };
  },
} as SupabasePlatform;

describe('Database Operation Tools Validation', () => {
  const tools = getDatabaseOperationTools({
    platform: mockPlatform,
    projectId: 'test-project',
    readOnly: false,
  });

  const readOnlyTools = getDatabaseOperationTools({
    platform: mockPlatform,
    projectId: 'test-project',
    readOnly: true,
  });

  describe('list_tables Tool Validation', () => {
    test('should validate schema names properly', async () => {
      const listTablesInput = tools.list_tables.parameters;
      
      // Valid schemas
      const validCases = [
        { schemas: ['public'] },
        { schemas: ['public', 'auth'] },
        { schemas: ['my_schema', 'another_schema'] },
      ];

      validCases.forEach(testCase => {
        const result = listTablesInput.safeParse({
          project_id: 'test',
          ...testCase,
        });
        expect(result.success).toBe(true);
      });
    });

    test('should reject invalid schema specifications', async () => {
      const listTablesInput = tools.list_tables.parameters;
      
      const invalidCases = [
        { schemas: [] }, // Empty array
        { schemas: ['information_schema'] }, // Forbidden schema
        { schemas: ['pg_catalog'] }, // System schema
        { schemas: ['invalid-schema'] }, // Invalid identifier
        { schemas: Array(51).fill('schema') }, // Too many schemas
        { schemas: ['public', 'public'] }, // Duplicates (after validation)
      ];

      invalidCases.forEach(testCase => {
        const result = listTablesInput.safeParse({
          project_id: 'test',
          ...testCase,
        });
        expect(result.success).toBe(false);
      });
    });

    test('should use default schema when not specified', () => {
      const listTablesInput = tools.list_tables.parameters;
      
      const result = listTablesInput.safeParse({
        project_id: 'test',
      });
      
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.schemas).toEqual(['public']);
      }
    });
  });

  describe('execute_sql Tool Validation', () => {
    test('should validate SQL queries properly', () => {
      const executeSqlInput = tools.execute_sql.parameters;
      
      const validQueries = [
        'SELECT * FROM users',
        'SELECT id, name FROM products WHERE active = true',
        'SELECT COUNT(*) FROM orders',
        'WITH cte AS (SELECT * FROM table1) SELECT * FROM cte',
      ];

      validQueries.forEach(query => {
        const result = executeSqlInput.safeParse({
          project_id: 'test',
          query,
        });
        expect(result.success).toBe(true);
      });
    });

    test('should reject invalid SQL queries', () => {
      const executeSqlInput = tools.execute_sql.parameters;
      
      const invalidQueries = [
        '', // Empty query
        ' ', // Whitespace only
        'a'.repeat(50001), // Too long
        'SELECT * FROM users; DROP TABLE users;', // SQL injection attempt
        'SELECT * FROM users; DELETE FROM sensitive_data;', // Dangerous pattern
      ];

      invalidQueries.forEach(query => {
        const result = executeSqlInput.safeParse({
          project_id: 'test',
          query,
        });
        expect(result.success).toBe(false);
      });
    });

    test('should detect write operations in read-only mode', async () => {
      const writeQueries = [
        'INSERT INTO users (name) VALUES (\'test\')',
        'UPDATE users SET name = \'test\'',
        'DELETE FROM users WHERE id = 1',
        'DROP TABLE users',
        'CREATE TABLE test (id INT)',
        'ALTER TABLE users ADD COLUMN test VARCHAR(255)',
        'TRUNCATE TABLE users',
      ];

      for (const query of writeQueries) {
        try {
          await readOnlyTools.execute_sql.execute({
            project_id: 'test',
            query,
          });
          // Should not reach here
          expect(true).toBe(false);
        } catch (error) {
          expect(error).toBeInstanceOf(Error);
          expect((error as Error).message).toContain('read-only');
        }
      }
    });

    test('should allow read operations in read-only mode', async () => {
      const readQueries = [
        'SELECT * FROM users',
        'SELECT COUNT(*) FROM orders',
        'EXPLAIN SELECT * FROM users',
        'SHOW TABLES',
      ];

      for (const query of readQueries) {
        try {
          const result = await readOnlyTools.execute_sql.execute({
            project_id: 'test',
            query,
          });
          expect(result).toBeDefined();
        } catch (error) {
          // Should not throw read-only errors for read queries
          expect((error as Error).message).not.toContain('read-only');
        }
      }
    });
  });

  describe('apply_migration Tool Validation', () => {
    test('should validate migration names properly', () => {
      const applyMigrationInput = tools.apply_migration.parameters;
      
      const validNames = [
        'create_users_table',
        'add_email_column',
        'update_user_permissions',
        '_private_migration',
        'migration_v2',
        'a'.repeat(100), // Maximum length
      ];

      validNames.forEach(name => {
        const result = applyMigrationInput.safeParse({
          project_id: 'test',
          name,
          query: 'CREATE TABLE test (id INT)',
        });
        expect(result.success).toBe(true);
      });
    });

    test('should reject invalid migration names', () => {
      const applyMigrationInput = tools.apply_migration.parameters;
      
      const invalidNames = [
        '', // Empty
        '123invalid', // Starts with number
        'invalid-name', // Contains hyphen
        'invalid name', // Contains space
        'CamelCase', // Not snake_case
        'a'.repeat(101), // Too long
      ];

      invalidNames.forEach(name => {
        const result = applyMigrationInput.safeParse({
          project_id: 'test',
          name,
          query: 'CREATE TABLE test (id INT)',
        });
        expect(result.success).toBe(false);
      });
    });

    test('should validate migration queries', () => {
      const applyMigrationInput = tools.apply_migration.parameters;
      
      const validQueries = [
        'CREATE TABLE users (id SERIAL PRIMARY KEY)',
        'ALTER TABLE users ADD COLUMN email VARCHAR(255)',
        'DROP TABLE old_table',
        'GRANT SELECT ON users TO public',
        'CREATE INDEX idx_users_email ON users(email)',
      ];

      validQueries.forEach(query => {
        const result = applyMigrationInput.safeParse({
          project_id: 'test',
          name: 'test_migration',
          query,
        });
        expect(result.success).toBe(true);
      });
    });

    test('should reject invalid migration queries', () => {
      const applyMigrationInput = tools.apply_migration.parameters;
      
      const invalidQueries = [
        '', // Empty
        'SELECT * FROM users', // Not a DDL operation
        'INSERT INTO users VALUES (1)', // Not a DDL operation
        'a'.repeat(100001), // Too long
      ];

      invalidQueries.forEach(query => {
        const result = applyMigrationInput.safeParse({
          project_id: 'test',
          name: 'test_migration',
          query,
        });
        expect(result.success).toBe(false);
      });
    });

    test('should prevent migrations in read-only mode', async () => {
      try {
        await readOnlyTools.apply_migration.execute({
          project_id: 'test',
          name: 'test_migration',
          query: 'CREATE TABLE test (id INT)',
        });
        // Should not reach here
        expect(true).toBe(false);
      } catch (error) {
        expect(error).toBeInstanceOf(Error);
        expect((error as Error).message).toContain('read-only');
      }
    });
  });

  describe('Parameter Injection and Security', () => {
    test('should prevent SQL injection in schema names', () => {
      const listTablesInput = tools.list_tables.parameters;
      
      const injectionAttempts = [
        "public'; DROP TABLE users; --",
        "public' OR '1'='1",
        "public/**/UNION/**/SELECT",
      ];

      injectionAttempts.forEach(attempt => {
        const result = listTablesInput.safeParse({
          project_id: 'test',
          schemas: [attempt],
        });
        expect(result.success).toBe(false);
      });
    });

    test('should sanitize migration names', () => {
      const applyMigrationInput = tools.apply_migration.parameters;
      
      const maliciousNames = [
        "../../../etc/passwd",
        "test; rm -rf /",
        "$(malicious_command)",
        "`malicious_command`",
      ];

      maliciousNames.forEach(name => {
        const result = applyMigrationInput.safeParse({
          project_id: 'test',
          name,
          query: 'CREATE TABLE test (id INT)',
        });
        expect(result.success).toBe(false);
      });
    });
  });

  describe('Error Handling', () => {
    test('should provide meaningful error messages', () => {
      const executeSqlInput = tools.execute_sql.parameters;
      
      const result = executeSqlInput.safeParse({
        project_id: 'test',
        query: '', // Invalid empty query
      });
      
      expect(result.success).toBe(false);
      if (!result.success) {
        const errorMessage = result.error.errors[0]?.message;
        expect(errorMessage).toBeDefined();
        expect(errorMessage).toContain('empty');
      }
    });

    test('should handle validation errors gracefully', () => {
      const applyMigrationInput = tools.apply_migration.parameters;
      
      const result = applyMigrationInput.safeParse({
        project_id: 'test',
        name: '123invalid',
        query: 'SELECT * FROM users', // Not DDL
      });
      
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.errors.length).toBeGreaterThan(0);
        
        // Should have errors for both name and query
        const errorPaths = result.error.errors.map(err => err.path[0]);
        expect(errorPaths).toContain('name');
        expect(errorPaths).toContain('query');
      }
    });
  });

  describe('Performance and Limits', () => {
    test('should enforce reasonable limits on input sizes', () => {
      const executeSqlInput = tools.execute_sql.parameters;
      
      // Test query size limit
      const largeQuery = 'SELECT * FROM users WHERE ' + 'id = 1 OR '.repeat(10000) + 'id = 1';
      const result = executeSqlInput.safeParse({
        project_id: 'test',
        query: largeQuery,
      });
      
      expect(result.success).toBe(false);
    });

    test('should limit schema array size', () => {
      const listTablesInput = tools.list_tables.parameters;
      
      const manySchemas = Array(51).fill(0).map((_, i) => `schema_${i}`);
      const result = listTablesInput.safeParse({
        project_id: 'test',
        schemas: manySchemas,
      });
      
      expect(result.success).toBe(false);
    });
  });
});