import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import { describe, expect, test, beforeAll, afterAll } from 'vitest';
import { 
  <PERSON><PERSON>rC<PERSON>,
  McpError,
  LoggingMessageNotificationSchema
} from '@modelcontextprotocol/sdk/types.js';
import { MCP_CLIENT_NAME, MCP_CLIENT_VERSION } from './mocks.js';
import { testConfig } from './test-config.js';

/**
 * MCP Protocol Edge Cases and Advanced Scenarios
 * 
 * This test suite focuses on:
 * - Edge cases in protocol implementation
 * - Stress testing and reliability
 * - Advanced error scenarios
 * - Protocol compliance under unusual conditions
 * - Security boundary testing
 */

type MCPTestClient = {
  client: Client;
  transport: StdioClientTransport;
};

async function setupMCPClient(options: {
  anonKey?: string;
  serviceKey?: string;
  supabaseUrl?: string;
  readOnly?: boolean;
} = {}): Promise<MCPTestClient> {
  const {
    anonKey = process.env.SUPABASE_ANON_KEY,
    serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY,
    supabaseUrl = process.env.SUPABASE_URL,
    readOnly = false
  } = options;

  const client = new Client(
    {
      name: MCP_CLIENT_NAME,
      version: MCP_CLIENT_VERSION,
    },
    {
      capabilities: {
        tools: {},
        prompts: {},
        resources: {},
        logging: {}
      },
    }
  );

  // Enhanced logging for edge case debugging
  const logs: Array<{ level: string; data: any; timestamp: number }> = [];
  
  client.setNotificationHandler(LoggingMessageNotificationSchema, (message) => {
    const { level, data } = message.params;
    logs.push({ level, data, timestamp: Date.now() });
  });

  const command = 'npx';
  const args = ['@supabase/mcp-server-supabase'];

  if (supabaseUrl) args.push('--supabase-url', supabaseUrl);
  if (anonKey) args.push('--anon-key', anonKey);
  if (serviceKey) args.push('--service-key', serviceKey);
  if (readOnly) args.push('--read-only');

  const transport = new StdioClientTransport({ command, args });
  await client.connect(transport);

  return { client, transport };
}

async function cleanupMCPClient(mcpClient: MCPTestClient): Promise<void> {
  try {
    await mcpClient.client.close();
    await mcpClient.transport.close();
  } catch (error) {
    console.warn('Error during MCP client cleanup:', error);
  }
}

describe('MCP Protocol Edge Cases', () => {
  let mcpClient: MCPTestClient;

  beforeAll(async () => {
    mcpClient = await setupMCPClient();
  }, testConfig.testTimeout);

  afterAll(async () => {
    if (mcpClient) {
      await cleanupMCPClient(mcpClient);
    }
  });

  describe('Parameter Boundary Testing', () => {
    test('should handle extremely long SQL queries', async () => {
      // Generate a very long but valid SQL query
      const longQuery = `SELECT ${
        Array.from({ length: 100 }, (_, i) => `'column_${i}' as col_${i}`).join(', ')
      }`;
      
      try {
        const response = await mcpClient.client.callTool({
          name: 'execute_sql',
          arguments: { query: longQuery }
        });
        
        expect(response.content).toBeDefined();
      } catch (error) {
        // Should handle gracefully, not crash
        expect(error).toBeInstanceOf(McpError);
      }
    });

    test('should handle empty parameters', async () => {
      try {
        await mcpClient.client.callTool({
          name: 'list_tables',
          arguments: {
            schemas: [] // Empty array
          }
        });
        
        // Should either succeed with default behavior or fail gracefully
        expect(true).toBe(true);
      } catch (error) {
        expect(error).toBeInstanceOf(McpError);
      }
    });

    test('should handle null and undefined parameters', async () => {
      const testCases = [
        { schemas: null },
        { schemas: undefined },
        {}, // Missing parameter entirely
      ];
      
      for (const testCase of testCases) {
        try {
          await mcpClient.client.callTool({
            name: 'list_tables',
            arguments: testCase
          });
          
          // Should handle gracefully
          expect(true).toBe(true);
        } catch (error) {
          // Should be a proper MCP error, not a crash
          expect(error).toBeInstanceOf(McpError);
        }
      }
    });

    test('should handle special characters in parameters', async () => {
      const specialChars = [
        "'; DROP TABLE test; --", // SQL injection attempt
        '\x00\x01\x02', // Binary characters
        '🚀🔥💯', // Unicode emojis
        '\n\r\t', // Control characters
        '"\\"', // Escaped quotes
      ];
      
      for (const specialChar of specialChars) {
        try {
          await mcpClient.client.callTool({
            name: 'execute_sql',
            arguments: {
              query: `SELECT '${specialChar}' as test_value`
            }
          });
          
          // Should handle safely
          expect(true).toBe(true);
        } catch (error) {
          // Should be handled gracefully
          expect(error).toBeDefined();
        }
      }
    });
  });

  describe('Connection Resilience Testing', () => {
    test('should handle rapid connection establishment and teardown', async () => {
      const clients: MCPTestClient[] = [];
      
      try {
        // Create multiple clients rapidly
        for (let i = 0; i < 5; i++) {
          const client = await setupMCPClient();
          clients.push(client);
          
          // Quick operation to verify connection
          const response = await client.client.listTools();
          expect(response.tools).toBeDefined();
        }
      } finally {
        // Clean up all clients
        await Promise.all(clients.map(client => cleanupMCPClient(client)));
      }
    });

    test('should handle client disconnection gracefully', async () => {
      const tempClient = await setupMCPClient();
      
      // Verify connection works
      const response1 = await tempClient.client.listTools();
      expect(response1.tools).toBeDefined();
      
      // Force disconnect
      await cleanupMCPClient(tempClient);
      
      // Verify main client still works
      const response2 = await mcpClient.client.listTools();
      expect(response2.tools).toBeDefined();
    });
  });

  describe('Protocol Message Edge Cases', () => {
    test('should handle malformed tool arguments gracefully', async () => {
      const malformedArgs = [
        { query: { nested: 'object' } }, // Object instead of string
        { query: 123 }, // Number instead of string
        { query: ['array', 'values'] }, // Array instead of string
        { query: true }, // Boolean instead of string
      ];
      
      for (const args of malformedArgs) {
        try {
          await mcpClient.client.callTool({
            name: 'execute_sql',
            arguments: args
          });
          
          // Should either work with type coercion or fail gracefully
          expect(true).toBe(true);
        } catch (error) {
          expect(error).toBeInstanceOf(McpError);
          expect((error as McpError).code).toBe(ErrorCode.InvalidParams);
        }
      }
    });

    test('should validate JSON schema compliance strictly', async () => {
      // Test with extra unexpected parameters
      try {
        await mcpClient.client.callTool({
          name: 'list_tables',
          arguments: {
            schemas: ['public'],
            unexpected_param: 'should_be_ignored_or_rejected',
            another_param: { complex: 'object' }
          }
        });
        
        // Should either ignore extra params or reject them
        expect(true).toBe(true);
      } catch (error) {
        expect(error).toBeInstanceOf(McpError);
      }
    });
  });

  describe('Security Boundary Testing', () => {
    test('should prevent SQL injection attempts', async () => {
      const injectionAttempts = [
        "'; DROP TABLE users; --",
        "' UNION SELECT * FROM sensitive_table --",
        "'; INSERT INTO logs (message) VALUES ('hacked'); --",
        "' OR '1'='1",
        "'; EXEC xp_cmdshell('dir'); --"
      ];
      
      for (const injection of injectionAttempts) {
        try {
          await mcpClient.client.callTool({
            name: 'execute_sql',
            arguments: {
              query: `SELECT * FROM token_store WHERE token = '${injection}'`
            }
          });
          
          // Should execute safely without side effects
          expect(true).toBe(true);
        } catch (error) {
          // Should be handled as a normal SQL error, not a security breach
          expect(error).toBeDefined();
        }
      }
    });

    test('should prevent unauthorized schema access', async () => {
      const unauthorizedSchemas = [
        'information_schema',
        'pg_catalog',
        'pg_toast',
        'memory_master', // Should require proper permissions
      ];
      
      for (const schema of unauthorizedSchemas) {
        try {
          await mcpClient.client.callTool({
            name: 'list_tables',
            arguments: { schemas: [schema] }
          });
          
          // Should either work with proper permissions or be denied
          expect(true).toBe(true);
        } catch (error) {
          // Should be a permission error, not a crash
          expect(error).toBeDefined();
        }
      }
    });

    test('should prevent system command execution', async () => {
      const systemCommands = [
        "'; COPY (SELECT '') TO PROGRAM 'rm -rf /' --",
        "'; SELECT pg_read_file('/etc/passwd') --",
        "'; CREATE FUNCTION evil() RETURNS void AS 'rm -rf /' LANGUAGE C --"
      ];
      
      for (const command of systemCommands) {
        try {
          await mcpClient.client.callTool({
            name: 'execute_sql',
            arguments: { query: command }
          });
          
          // Should be blocked or fail safely
          expect(true).toBe(true);
        } catch (error) {
          // Expected - should be blocked
          expect(error).toBeDefined();
        }
      }
    });
  });

  describe('Performance Edge Cases', () => {
    test('should handle high-frequency requests', async () => {
      const startTime = Date.now();
      const requests = [];
      
      // Send 20 rapid requests
      for (let i = 0; i < 20; i++) {
        requests.push(
          mcpClient.client.callTool({
            name: 'execute_sql',
            arguments: {
              query: `SELECT ${i} as request_number`
            }
          })
        );
      }
      
      const results = await Promise.allSettled(requests);
      const endTime = Date.now();
      
      // Should handle all requests within reasonable time
      expect(endTime - startTime).toBeLessThan(10000); // 10 seconds
      
      // Most requests should succeed
      const successful = results.filter(r => r.status === 'fulfilled').length;
      expect(successful).toBeGreaterThan(results.length * 0.8); // At least 80% success
    });

    test('should handle large result sets gracefully', async () => {
      try {
        // Query that might return large results
        const response = await mcpClient.client.callTool({
          name: 'execute_sql',
          arguments: {
            query: `
              SELECT 
                generate_series(1, 1000) as id,
                md5(random()::text) as random_hash,
                now() as timestamp
            `
          }
        });
        
        expect(response.content).toBeDefined();
        
        // Should either succeed or fail gracefully with size limits
        const textContent = response.content.find(item => item.type === 'text');
        if (textContent) {
          expect(textContent.text.length).toBeGreaterThan(0);
        }
      } catch (error) {
        // Should fail gracefully if result set is too large
        expect(error).toBeDefined();
      }
    });
  });

  describe('Error Recovery Testing', () => {
    test('should recover from database connection errors', async () => {
      // Try to cause a connection error with invalid query
      try {
        await mcpClient.client.callTool({
          name: 'execute_sql',
          arguments: {
            query: 'SELECT * FROM definitely_nonexistent_table_12345'
          }
        });
      } catch (error) {
        // Expected to fail
      }
      
      // Should be able to execute valid queries after error
      const response = await mcpClient.client.callTool({
        name: 'list_tables',
        arguments: { schemas: ['public'] }
      });
      
      expect(response.content).toBeDefined();
    });

    test('should handle timeout scenarios gracefully', async () => {
      try {
        // Query that might take a long time
        await mcpClient.client.callTool({
          name: 'execute_sql',
          arguments: {
            query: 'SELECT pg_sleep(0.1)' // Short sleep to test timeout handling
          }
        });
        
        expect(true).toBe(true);
      } catch (error) {
        // Should handle timeouts gracefully
        expect(error).toBeDefined();
      }
    });
  });

  describe('Protocol Compliance Edge Cases', () => {
    test('should handle missing required capabilities', async () => {
      // Test with minimal client capabilities
      const minimalClient = new Client(
        {
          name: MCP_CLIENT_NAME,
          version: MCP_CLIENT_VERSION,
        },
        {
          capabilities: {}, // Minimal capabilities
        }
      );
      
      const transport = new StdioClientTransport({
        command: 'npx',
        args: [
          '@supabase/mcp-server-supabase',
          '--supabase-url', process.env.SUPABASE_URL || '',
          '--anon-key', process.env.SUPABASE_ANON_KEY || ''
        ],
      });
      
      try {
        await minimalClient.connect(transport);
        
        // Should still be able to perform basic operations
        const response = await minimalClient.listTools();
        expect(response.tools).toBeDefined();
        
        await minimalClient.close();
        await transport.close();
      } catch (error) {
        // Should fail gracefully if capabilities are insufficient
        expect(error).toBeDefined();
      }
    });

    test('should validate protocol version compatibility', async () => {
      // Test with different client version
      const versionTestClient = new Client(
        {
          name: MCP_CLIENT_NAME,
          version: '999.999.999', // Future version
        },
        {
          capabilities: {
            tools: {},
          },
        }
      );
      
      const transport = new StdioClientTransport({
        command: 'npx',
        args: [
          '@supabase/mcp-server-supabase',
          '--supabase-url', process.env.SUPABASE_URL || '',
          '--anon-key', process.env.SUPABASE_ANON_KEY || ''
        ],
      });
      
      try {
        await versionTestClient.connect(transport);
        
        // Should handle version differences gracefully
        const response = await versionTestClient.listTools();
        expect(response.tools).toBeDefined();
        
        await versionTestClient.close();
        await transport.close();
      } catch (error) {
        // Should fail gracefully if version is incompatible
        expect(error).toBeDefined();
      }
    });
  });
});