import { describe, expect, test, beforeEach, afterEach, vi } from 'vitest';
import express from 'express';
import request from 'supertest';
import { 
  MonitoringSystem, 
  healthEndpoints, 
  metricsEndpoints,
  distributedTracer,
  alertingSystem,
  testFixtures
} from '../src/monitoring/index.js';

/**
 * Integration tests for the comprehensive monitoring system
 */
describe('Monitoring System Integration', () => {
  let app: express.Application;
  let monitoringSystem: MonitoringSystem;

  beforeEach(async () => {
    // Create Express app
    app = express();
    app.use(express.json());

    // Initialize monitoring system
    monitoringSystem = new MonitoringSystem({
      enableHealthEndpoints: true,
      enableMetricsEndpoints: true,
      enableDistributedTracing: true,
      enableAlerting: true,
      alertCheckInterval: 1000, // 1 second for testing
    });

    await monitoringSystem.initialize(app);
  });

  afterEach(async () => {
    await monitoringSystem.shutdown();
    distributedTracer.clear();
    alertingSystem.stopMonitoring();
  });

  describe('Health Endpoints', () => {
    test('should return basic health status', async () => {
      const response = await request(app)
        .get('/health')
        .expect(200);

      expect(response.body).toMatchObject({
        status: expect.stringMatching(/^(healthy|degraded|unhealthy)$/),
        timestamp: expect.any(String),
        uptime: expect.any(Number),
        version: expect.any(String),
      });
    });

    test('should return detailed health information', async () => {
      const response = await request(app)
        .get('/health/detailed')
        .expect(200);

      expect(response.body).toMatchObject({
        status: expect.stringMatching(/^(healthy|degraded|unhealthy)$/),
        timestamp: expect.any(String),
        uptime: expect.any(Number),
        version: expect.any(String),
        checks: expect.any(Object),
        metrics: expect.any(Object),
        configuration: expect.any(Object),
        performance: expect.any(Object),
      });
    });

    test('should return readiness status', async () => {
      const response = await request(app)
        .get('/health/ready')
        .expect(200);

      expect(response.body).toMatchObject({
        ready: expect.any(Boolean),
        timestamp: expect.any(String),
        checks: expect.any(Object),
      });
    });

    test('should return liveness status', async () => {
      const response = await request(app)
        .get('/health/live')
        .expect(200);

      expect(response.body).toMatchObject({
        alive: true,
        timestamp: expect.any(String),
        uptime: expect.any(Number),
        pid: expect.any(Number),
      });
    });

    test('should return startup status', async () => {
      const response = await request(app)
        .get('/health/startup')
        .expect(200);

      expect(response.body).toMatchObject({
        started: expect.any(Boolean),
        timestamp: expect.any(String),
        status: expect.stringMatching(/^(healthy|degraded|unhealthy)$/),
      });
    });
  });

  describe('Metrics Endpoints', () => {
    test('should return Prometheus metrics', async () => {
      const response = await request(app)
        .get('/metrics')
        .expect(200);

      expect(response.headers['content-type']).toContain('text/plain');
      expect(response.text).toContain('mcp_server_info');
      expect(response.text).toContain('mcp_server_memory_usage_bytes');
      expect(response.text).toContain('mcp_server_uptime_seconds');
    });

    test('should return JSON metrics', async () => {
      const response = await request(app)
        .get('/metrics/json')
        .expect(200);

      expect(response.body).toMatchObject({
        timestamp: expect.any(String),
        metrics: expect.any(Object),
      });
    });

    test('should return performance metrics', async () => {
      const response = await request(app)
        .get('/metrics/performance')
        .expect(200);

      expect(response.body).toMatchObject({
        timestamp: expect.any(String),
        performance: expect.any(Object),
        operations: expect.any(Object),
      });
    });

    test('should return configuration metrics', async () => {
      const response = await request(app)
        .get('/metrics/config')
        .expect(200);

      expect(response.body).toMatchObject({
        timestamp: expect.any(String),
        configuration: expect.any(Object),
        health: expect.any(Object),
      });
    });

    test('should return system metrics', async () => {
      const response = await request(app)
        .get('/metrics/system')
        .expect(200);

      expect(response.body).toMatchObject({
        timestamp: expect.any(String),
        system: {
          memory: expect.any(Object),
          cpu: expect.any(Object),
          process: expect.any(Object),
        },
      });
    });
  });

  describe('Distributed Tracing', () => {
    test('should create and track spans', async () => {
      const span = distributedTracer.startTrace('test-operation');
      
      expect(span).toBeDefined();
      expect(span.getContext().traceId).toBeDefined();
      expect(span.getContext().spanId).toBeDefined();
      expect(span.getContext().operationName).toBe('test-operation');
      
      span.setTag('test', true);
      span.log('info', 'Test log message');
      
      const context = span.finish();
      
      expect(context.tags.test).toBe(true);
      expect(context.logs).toHaveLength(1);
      expect(context.duration).toBeGreaterThan(0);
    });

    test('should create child spans', async () => {
      const parentSpan = distributedTracer.startTrace('parent-operation');
      const childSpan = parentSpan.createChild('child-operation');
      
      expect(childSpan.getContext().traceId).toBe(parentSpan.getContext().traceId);
      expect(childSpan.getContext().parentSpanId).toBe(parentSpan.getContext().spanId);
      
      parentSpan.finish();
      childSpan.finish();
    });

    test('should trace async operations', async () => {
      const result = await distributedTracer.trace('async-operation', async () => {
        await new Promise(resolve => setTimeout(resolve, 10));
        return 'test-result';
      });
      
      expect(result).toBe('test-result');
      
      const stats = distributedTracer.getStats();
      expect(stats.totalSpans).toBeGreaterThan(0);
    });

    test('should handle errors in traced operations', async () => {
      await expect(
        distributedTracer.trace('error-operation', async () => {
          throw new Error('Test error');
        })
      ).rejects.toThrow('Test error');
      
      const stats = distributedTracer.getStats();
      expect(stats.errorRate).toBeGreaterThan(0);
    });
  });

  describe('Alerting System', () => {
    test('should trigger alerts based on conditions', async () => {
      let alertTriggered = false;
      
      // Add test alert handler
      alertingSystem.addHandler('test', (alert) => {
        alertTriggered = true;
        expect(alert.name).toBe('Test Alert');
        expect(alert.severity).toBe('warning');
      });
      
      // Add test rule that always triggers
      alertingSystem.addRule({
        id: 'test-rule',
        name: 'Test Alert',
        description: 'Test alert for integration testing',
        severity: 'warning',
        condition: () => true, // Always trigger
        enabled: true,
        cooldownMs: 1000,
        tags: { test: 'true' },
      });
      
      // Manually trigger alert check
      await alertingSystem.checkAlertConditions();
      
      expect(alertTriggered).toBe(true);
      
      const activeAlerts = alertingSystem.getActiveAlerts();
      expect(activeAlerts).toHaveLength(1);
      expect(activeAlerts[0].name).toBe('Test Alert');
    });

    test('should resolve alerts when conditions are no longer met', async () => {
      let conditionMet = true;
      
      alertingSystem.addRule({
        id: 'resolve-test-rule',
        name: 'Resolve Test Alert',
        description: 'Test alert resolution',
        severity: 'error',
        condition: () => conditionMet,
        enabled: true,
        cooldownMs: 100,
        tags: { test: 'resolve' },
      });
      
      // Trigger alert
      await alertingSystem.checkAlertConditions();
      expect(alertingSystem.getActiveAlerts()).toHaveLength(1);
      
      // Change condition and check again
      conditionMet = false;
      await alertingSystem.checkAlertConditions();
      expect(alertingSystem.getActiveAlerts()).toHaveLength(0);
    });

    test('should respect cooldown periods', async () => {
      let triggerCount = 0;
      
      alertingSystem.addHandler('cooldown-test', () => {
        triggerCount++;
      });
      
      alertingSystem.addRule({
        id: 'cooldown-rule',
        name: 'Cooldown Test',
        description: 'Test cooldown functionality',
        severity: 'info',
        condition: () => true,
        enabled: true,
        cooldownMs: 5000, // 5 second cooldown
        tags: { test: 'cooldown' },
      });
      
      // Trigger multiple times quickly
      await alertingSystem.checkAlertConditions();
      await alertingSystem.checkAlertConditions();
      await alertingSystem.checkAlertConditions();
      
      // Should only trigger once due to cooldown
      expect(triggerCount).toBe(1);
    });
  });

  describe('Test Fixtures', () => {
    test('should generate valid health check data', () => {
      const healthData = testFixtures.generateHealthCheckData();
      
      expect(healthData).toMatchObject({
        status: expect.stringMatching(/^(healthy|degraded|unhealthy)$/),
        timestamp: expect.any(String),
        uptime: expect.any(Number),
        version: expect.any(String),
        checks: expect.any(Object),
        metrics: expect.any(Object),
      });
    });

    test('should generate valid trace contexts', () => {
      const traces = testFixtures.generateTraces(5);
      
      expect(traces).toHaveLength(5);
      traces.forEach(trace => {
        expect(trace).toMatchObject({
          traceId: expect.any(String),
          spanId: expect.any(String),
          operationName: expect.any(String),
          startTime: expect.any(Number),
          endTime: expect.any(Number),
          duration: expect.any(Number),
          tags: expect.any(Object),
          logs: expect.any(Array),
          status: expect.stringMatching(/^(ok|error|timeout)$/),
        });
      });
    });

    test('should generate valid alerts', () => {
      const alerts = testFixtures.generateAlerts(3);
      
      expect(alerts).toHaveLength(3);
      alerts.forEach(alert => {
        expect(alert).toMatchObject({
          id: expect.any(String),
          ruleId: expect.any(String),
          name: expect.any(String),
          description: expect.any(String),
          severity: expect.stringMatching(/^(info|warning|error|critical)$/),
          timestamp: expect.any(Date),
          tags: expect.any(Object),
          resolved: expect.any(Boolean),
          metadata: expect.any(Object),
        });
      });
    });

    test('should generate scenario data', () => {
      const scenarios = ['healthy', 'degraded', 'critical'];
      
      scenarios.forEach(scenarioName => {
        const scenario = testFixtures.generateScenario(scenarioName);
        
        expect(scenario).toMatchObject({
          health: expect.any(Object),
          metrics: expect.any(Object),
          traces: expect.any(Array),
          alerts: expect.any(Array),
        });
        
        // Verify scenario-specific characteristics
        if (scenarioName === 'healthy') {
          expect(scenario.health.status).toBe('healthy');
          expect(scenario.alerts).toHaveLength(0);
        } else if (scenarioName === 'critical') {
          expect(scenario.health.status).toBe('unhealthy');
          expect(scenario.alerts.length).toBeGreaterThan(0);
        }
      });
    });
  });

  describe('Dashboard Integration', () => {
    test('should return comprehensive dashboard data', async () => {
      const response = await request(app)
        .get('/monitoring/dashboard')
        .expect(200);

      expect(response.body).toMatchObject({
        health: expect.any(Object),
        metrics: expect.any(Object),
        alerts: expect.any(Object),
        tracing: expect.any(Object),
        system: expect.any(Object),
      });
    });
  });
});
