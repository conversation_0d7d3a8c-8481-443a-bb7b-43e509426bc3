import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import { describe, expect, test, beforeAll, afterAll } from 'vitest';
import { 
  LoggingMessageNotificationSchema,
  ErrorCode,
  McpError
} from '@modelcontextprotocol/sdk/types.js';
import { MCP_CLIENT_NAME, MCP_CLIENT_VERSION } from './mocks.js';
import { testConfig } from './test-config.js';

/**
 * Comprehensive MCP Protocol Tests
 * 
 * This test suite extends the existing MCP protocol tests with:
 * - Advanced tool parameter validation
 * - Protocol compliance edge cases
 * - Performance and reliability testing
 * - Security boundary validation
 * - Resource management testing
 * - Concurrent operation handling
 */

type MCPTestClient = {
  client: Client;
  transport: StdioClientTransport;
};

async function setupMCPClient(): Promise<MCPTestClient> {
  const client = new Client(
    {
      name: MCP_CLIENT_NAME,
      version: MCP_CLIENT_VERSION,
    },
    {
      capabilities: {
        tools: {},
        prompts: {},
        resources: {},
        logging: {}
      },
    }
  );

  // Enhanced logging for comprehensive testing
  const logs: Array<{ level: string; data: any; timestamp: number }> = [];
  
  client.setNotificationHandler(LoggingMessageNotificationSchema, (message) => {
    const { level, data } = message.params;
    logs.push({ level, data, timestamp: Date.now() });
    if (level === 'error') {
      console.error('MCP Server Error:', data);
    }
  });

  const command = 'node';
  const args = ['dist/transports/stdio.js'];

  const env = {
    ...process.env,
    SUPABASE_URL: process.env.SUPABASE_URL || testConfig.supabaseUrl,
    SUPABASE_ANON_KEY: process.env.SUPABASE_ANON_KEY || testConfig.anonKey,
    SUPABASE_SERVICE_ROLE_KEY: process.env.SUPABASE_SERVICE_ROLE_KEY || testConfig.serviceKey,
    READ_ONLY: 'false'
  };

  const transport = new StdioClientTransport({
    command,
    args,
    env
  });

  await client.connect(transport);
  return { client, transport };
}

async function cleanupMCPClient(mcpClient: MCPTestClient): Promise<void> {
  try {
    await mcpClient.client.close();
    await mcpClient.transport.close();
  } catch (error) {
    console.warn('Error during MCP client cleanup:', error);
  }
}

describe('Comprehensive MCP Protocol Tests', () => {
  let mcpClient: MCPTestClient;

  beforeAll(async () => {
    if (!process.env.SUPABASE_URL && !testConfig.supabaseUrl) {
      console.warn('Skipping Comprehensive MCP Protocol tests: SUPABASE_URL not configured');
      return;
    }
    
    try {
      mcpClient = await setupMCPClient();
    } catch (error) {
      console.warn('Failed to setup MCP client:', error);
      throw error;
    }
  }, testConfig.testTimeout);

  afterAll(async () => {
    if (mcpClient) {
      await cleanupMCPClient(mcpClient);
    }
  });

  describe('Advanced Tool Parameter Validation', () => {
    test('should handle complex nested parameters correctly', async () => {
      if (!mcpClient) {
        console.warn('MCP client not available, skipping test');
        return;
      }
      
      const response = await mcpClient.client.callTool({
        name: 'list_tables',
        arguments: {
          schemas: ['public', 'information_schema']
        }
      });
      
      expect(response.content).toBeDefined();
      expect(Array.isArray(response.content)).toBe(true);
      
      // Validate response structure
      response.content.forEach(item => {
        expect(item.type).toBe('text');
        expect(typeof item.text).toBe('string');
      });
    });

    test('should validate parameter types strictly', async () => {
      if (!mcpClient) {
        console.warn('MCP client not available, skipping test');
        return;
      }
      
      try {
        // Test with invalid parameter type (number instead of array)
        await mcpClient.client.callTool({
          name: 'list_tables',
          arguments: {
            schemas: 123 // Should be an array
          }
        });
        
        // If no error, the tool should handle gracefully
        expect(true).toBe(true);
      } catch (error) {
        // Should throw appropriate error for invalid parameters
        expect(error).toBeDefined();
      }
    });

    test('should handle empty and null parameters appropriately', async () => {
      if (!mcpClient) {
        console.warn('MCP client not available, skipping test');
        return;
      }
      
      // Test with empty array
      const response1 = await mcpClient.client.callTool({
        name: 'list_tables',
        arguments: {
          schemas: []
        }
      });
      
      expect(response1.content).toBeDefined();
      
      // Test with no schemas parameter (should use default)
      const response2 = await mcpClient.client.callTool({
        name: 'list_tables',
        arguments: {}
      });
      
      expect(response2.content).toBeDefined();
    });
  });

  describe('Protocol Compliance and Standards', () => {
    test('should maintain consistent response format across all tools', async () => {
      if (!mcpClient) {
        console.warn('MCP client not available, skipping test');
        return;
      }
      
      const tools = await mcpClient.client.listTools();
      
      // Test each tool for consistent response format
      for (const tool of tools.tools.slice(0, 3)) { // Test first 3 tools to avoid timeout
        try {
          const response = await mcpClient.client.callTool({
            name: tool.name,
            arguments: {}
          });
          
          // All responses should have content array
          expect(response.content).toBeDefined();
          expect(Array.isArray(response.content)).toBe(true);
          
          // Each content item should have type and appropriate data
          response.content.forEach(item => {
            expect(item.type).toBeDefined();
            expect(['text', 'image', 'resource'].includes(item.type)).toBe(true);
          });
        } catch (error) {
          // Some tools may require specific parameters, that's acceptable
          console.log(`Tool ${tool.name} requires specific parameters:`, error.message);
        }
      }
    });

    test('should handle concurrent tool calls efficiently', async () => {
      if (!mcpClient) {
        console.warn('MCP client not available, skipping test');
        return;
      }
      
      const startTime = Date.now();
      
      // Execute multiple concurrent calls
      const promises = [
        mcpClient.client.callTool({
          name: 'list_tables',
          arguments: { schemas: ['public'] }
        }),
        mcpClient.client.callTool({
          name: 'execute_sql',
          arguments: { query: 'SELECT 1 as test_value' }
        }),
        mcpClient.client.callTool({
          name: 'list_tables',
          arguments: { schemas: ['information_schema'] }
        })
      ];
      
      const results = await Promise.all(promises);
      const endTime = Date.now();
      
      // All calls should succeed
      expect(results).toHaveLength(3);
      results.forEach(result => {
        expect(result.content).toBeDefined();
        expect(Array.isArray(result.content)).toBe(true);
      });
      
      // Should complete within reasonable time (concurrent should be faster than sequential)
      expect(endTime - startTime).toBeLessThan(10000); // 10 seconds max
    });

    test('should properly handle tool discovery metadata', async () => {
      if (!mcpClient) {
        console.warn('MCP client not available, skipping test');
        return;
      }
      
      const tools = await mcpClient.client.listTools();
      
      expect(tools.tools).toBeDefined();
      expect(tools.tools.length).toBeGreaterThan(0);
      
      // Validate each tool has complete metadata
      tools.tools.forEach(tool => {
        expect(tool.name).toBeDefined();
        expect(typeof tool.name).toBe('string');
        expect(tool.name.length).toBeGreaterThan(0);
        
        expect(tool.description).toBeDefined();
        expect(typeof tool.description).toBe('string');
        expect(tool.description.length).toBeGreaterThan(0);
        
        expect(tool.inputSchema).toBeDefined();
        expect(typeof tool.inputSchema).toBe('object');
        expect(tool.inputSchema.type).toBeDefined();
      });
    });
  });

  describe('Performance and Reliability', () => {
    test('should respond to simple queries within acceptable time limits', async () => {
      if (!mcpClient) {
        console.warn('MCP client not available, skipping test');
        return;
      }
      
      const startTime = Date.now();
      
      await mcpClient.client.callTool({
        name: 'execute_sql',
        arguments: { query: 'SELECT 1' }
      });
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // Should respond within 5 seconds for simple queries
      expect(duration).toBeLessThan(5000);
    });

    test('should handle multiple sequential calls without degradation', async () => {
      if (!mcpClient) {
        console.warn('MCP client not available, skipping test');
        return;
      }
      
      const times: number[] = [];
      
      // Execute 5 sequential calls and measure performance
      for (let i = 0; i < 5; i++) {
        const startTime = Date.now();
        
        await mcpClient.client.callTool({
          name: 'execute_sql',
          arguments: { query: `SELECT ${i + 1} as iteration` }
        });
        
        const endTime = Date.now();
        times.push(endTime - startTime);
      }
      
      // Performance should not degrade significantly
      const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
      const maxTime = Math.max(...times);
      
      // Only check degradation if we have meaningful timing data
      if (avgTime > 0) {
        expect(maxTime).toBeLessThan(avgTime * 3); // Max should not be more than 3x average
      } else {
        // If all calls are extremely fast (< 1ms), just verify they all completed
        expect(times.length).toBe(5);
      }
    });
  });

  describe('Security and Boundary Testing', () => {
    test('should reject malicious SQL injection attempts', async () => {
      if (!mcpClient) {
        console.warn('MCP client not available, skipping test');
        return;
      }
      
      const maliciousQueries = [
        "SELECT * FROM users; DROP TABLE users; --",
        "'; DELETE FROM important_table; --",
        "UNION SELECT password FROM users"
      ];
      
      for (const query of maliciousQueries) {
        try {
          const response = await mcpClient.client.callTool({
            name: 'execute_sql',
            arguments: { query }
          });
          
          // If it doesn't throw an error, it should at least not execute harmful operations
          expect(response.content).toBeDefined();
        } catch (error) {
          // Throwing an error for malicious queries is acceptable
          expect(error).toBeDefined();
        }
      }
    });

    test('should handle extremely long parameter values gracefully', async () => {
      if (!mcpClient) {
        console.warn('MCP client not available, skipping test');
        return;
      }
      
      const longString = 'A'.repeat(10000); // 10KB string
      
      try {
        await mcpClient.client.callTool({
          name: 'execute_sql',
          arguments: { query: `SELECT '${longString}' as long_value` }
        });
        
        // Should handle or reject gracefully
        expect(true).toBe(true);
      } catch (error) {
        // Rejecting very long parameters is acceptable
        expect(error).toBeDefined();
      }
    });
  });

  describe('Resource Management', () => {
    test('should properly manage database connections', async () => {
      if (!mcpClient) {
        console.warn('MCP client not available, skipping test');
        return;
      }
      
      // Execute multiple queries to test connection pooling
      const queries = [
        'SELECT COUNT(*) FROM information_schema.tables',
        'SELECT current_database()',
        'SELECT version()',
        'SELECT current_timestamp',
        'SELECT 1+1 as result'
      ];
      
      for (const query of queries) {
        const response = await mcpClient.client.callTool({
          name: 'execute_sql',
          arguments: { query }
        });
        
        expect(response.content).toBeDefined();
        expect(Array.isArray(response.content)).toBe(true);
      }
      
      // All queries should succeed, indicating proper connection management
      expect(true).toBe(true);
    });
  });
});