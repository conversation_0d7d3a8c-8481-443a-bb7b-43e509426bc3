# MCP Integration Guide for Supabase CRUD Tools

## Overview

This guide explains how to integrate and use the Supabase MCP Server with Model Context Protocol (MCP) clients. The server provides database CRUD operations through a standardized MCP interface.

## Table of Contents

1. [MCP Client Setup](#mcp-client-setup)
2. [Tool Registration](#tool-registration)
3. [Authentication & Configuration](#authentication--configuration)
4. [Tool Discovery](#tool-discovery)
5. [Error Handling Integration](#error-handling-integration)
6. [Best Practices](#best-practices)
7. [Troubleshooting](#troubleshooting)

## MCP Client Setup

### Installation

```bash
npm install @supabase/mcp-server-supabase
```

### Basic Configuration

Configure your MCP client to connect to the Supabase MCP Server:

```json
{
  "mcpServers": {
    "supabase": {
      "command": "node",
      "args": ["path/to/mcp-server-supabase/dist/index.js"],
      "env": {
        "SUPABASE_URL": "https://your-project.supabase.co",
        "SUPABASE_ANON_KEY": "your-anon-key",
        "SUPABASE_SERVICE_ROLE_KEY": "your-service-role-key"
      }
    }
  }
}
```

### Environment Variables

Required environment variables:

```bash
# Required
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key

# Optional (for admin operations)
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Optional configuration
SUPABASE_PROJECT_ID=your-project-id
LOG_LEVEL=info
READ_ONLY_MODE=false
```

## Tool Registration

### Available MCP Tools

The server registers the following tools with the MCP client:

#### Core CRUD Tools
- `create_record` - Insert new records
- `read_records` - Query and retrieve records
- `update_records` - Modify existing records
- `delete_records` - Remove records
- `upsert_record` - Insert or update records

#### Bulk Operations
- `bulk_create_records` - Insert multiple records
- `bulk_update_records` - Update multiple records
- `bulk_delete_records` - Delete multiple records

#### Advanced Operations
- `execute_transaction` - Atomic multi-operation transactions
- `execute_sql` - Raw SQL execution

#### Database Management
- `list_tables` - Enumerate database tables
- `list_extensions` - Show PostgreSQL extensions
- `list_migrations` - View migration history
- `apply_migration` - Execute database migrations

### Tool Discovery

Use the MCP protocol to discover available tools:

```typescript
// Example using MCP client
const client = new MCPClient();
await client.connect();

const tools = await client.listTools();
console.log('Available tools:', tools.tools.map(t => t.name));
```

### Tool Schema Validation

Each tool includes comprehensive zod schemas for parameter validation:

```typescript
// Example tool schema structure
{
  name: "create_record",
  description: "Insert a new record into a table",
  inputSchema: {
    type: "object",
    properties: {
      project_id: { type: "string" },
      schema: { type: "string" },
      table: { type: "string" },
      data: { type: "object" },
      returning: { 
        type: "array", 
        items: { type: "string" },
        optional: true 
      }
    },
    required: ["project_id", "schema", "table", "data"]
  }
}
```

## Authentication & Configuration

### Authentication Methods

1. **Anonymous Key (Read-Only)**
   ```typescript
   // Limited to public data and RLS policies
   env: {
     SUPABASE_ANON_KEY: "your-anon-key"
   }
   ```

2. **Service Role Key (Full Access)**
   ```typescript
   // Bypasses RLS, full database access
   env: {
     SUPABASE_SERVICE_ROLE_KEY: "your-service-role-key"
   }
   ```

3. **User Authentication**
   ```typescript
   // Pass user JWT tokens in requests
   {
     headers: {
       "Authorization": "Bearer user-jwt-token"
     }
   }
   ```

### Project Configuration

Configure multiple Supabase projects:

```json
{
  "mcpServers": {
    "supabase-dev": {
      "command": "node",
      "args": ["path/to/mcp-server-supabase/dist/index.js"],
      "env": {
        "SUPABASE_URL": "https://dev-project.supabase.co",
        "SUPABASE_ANON_KEY": "dev-anon-key"
      }
    },
    "supabase-prod": {
      "command": "node", 
      "args": ["path/to/mcp-server-supabase/dist/index.js"],
      "env": {
        "SUPABASE_URL": "https://prod-project.supabase.co",
        "SUPABASE_SERVICE_ROLE_KEY": "prod-service-role-key"
      }
    }
  }
}
```

## Tool Discovery

### Listing Available Tools

```typescript
import { MCPClient } from '@modelcontextprotocol/client';

async function discoverTools() {
  const client = new MCPClient();
  await client.connect();
  
  const response = await client.listTools();
  
  response.tools.forEach(tool => {
    console.log(`Tool: ${tool.name}`);
    console.log(`Description: ${tool.description}`);
    console.log(`Schema:`, tool.inputSchema);
  });
}
```

### Tool Categories

Tools are organized by functionality:

```typescript
const toolCategories = {
  crud: [
    'create_record',
    'read_records', 
    'update_records',
    'delete_records',
    'upsert_record'
  ],
  bulk: [
    'bulk_create_records',
    'bulk_update_records',
    'bulk_delete_records'
  ],
  advanced: [
    'execute_transaction',
    'execute_sql'
  ],
  management: [
    'list_tables',
    'list_extensions',
    'list_migrations',
    'apply_migration'
  ]
};
```

## Error Handling Integration

### MCP Error Response Format

```typescript
interface MCPErrorResponse {
  error: {
    code: number;
    message: string;
    data?: {
      type: string;
      context: Record<string, any>;
      requestId: string;
      suggestions?: string[];
    };
  };
}
```

### Error Code Mapping

```typescript
const errorCodeMapping = {
  // Validation errors
  'INVALID_IDENTIFIER': 1001,
  'INVALID_WHERE_CLAUSE': 1002,
  'VALIDATION_FAILED': 1003,
  
  // Database errors
  'CONSTRAINT_VIOLATION': 2001,
  'PERMISSION_DENIED': 2002,
  'TABLE_NOT_FOUND': 2003,
  'COLUMN_NOT_FOUND': 2004,
  
  // Connection errors
  'CONNECTION_FAILED': 3001,
  'TIMEOUT': 3002,
  'RATE_LIMITED': 3003,
  
  // System errors
  'INTERNAL_ERROR': 5001,
  'CONFIGURATION_ERROR': 5002
};
```

### Client-Side Error Handling

```typescript
async function handleMCPCall(toolName: string, params: any) {
  try {
    const result = await client.callTool({
      name: toolName,
      arguments: params
    });
    
    return result.content;
  } catch (error) {
    if (error.code) {
      switch (error.code) {
        case 1001:
          console.error('Invalid identifier:', error.data?.context);
          break;
        case 2001:
          console.error('Constraint violation:', error.message);
          break;
        case 3002:
          console.error('Operation timed out, retrying...');
          // Implement retry logic
          break;
        default:
          console.error('Unexpected error:', error);
      }
    }
    
    throw error;
  }
}
```

## Best Practices

### Performance Optimization

1. **Use Bulk Operations**
   ```typescript
   // Instead of multiple create_record calls
   await client.callTool({
     name: 'bulk_create_records',
     arguments: {
       project_id: 'your-project',
       schema: 'public',
       table: 'users',
       records: userArray,
       batchSize: 1000
     }
   });
   ```

2. **Optimize Query Selection**
   ```typescript
   // Only select needed columns
   await client.callTool({
     name: 'read_records',
     arguments: {
       project_id: 'your-project',
       schema: 'public',
       table: 'users',
       columns: ['id', 'name', 'email'], // Instead of '*'
       limit: 100
     }
   });
   ```

3. **Use Transactions for Related Operations**
   ```typescript
   await client.callTool({
     name: 'execute_transaction',
     arguments: {
       project_id: 'your-project',
       operations: [
         {
           type: 'CREATE',
           schema: 'public',
           table: 'orders',
           data: orderData
         },
         {
           type: 'UPDATE',
           schema: 'public', 
           table: 'inventory',
           data: { quantity: newQuantity },
           where: { column: 'product_id', operator: '=', value: productId }
         }
       ]
     }
   });
   ```

### Security Best Practices

1. **Use Least Privilege**
   - Use anonymous keys for read-only operations
   - Reserve service role keys for admin tasks
   - Implement proper RLS policies

2. **Validate Input Data**
   ```typescript
   // Validate data before sending to MCP
   const validateUserData = (data: any) => {
     if (!data.email || !data.email.includes('@')) {
       throw new Error('Invalid email format');
     }
     if (data.age && (data.age < 0 || data.age > 150)) {
       throw new Error('Invalid age');
     }
   };
   ```

3. **Handle Sensitive Data**
   ```typescript
   // Don't log sensitive information
   const sanitizeForLogging = (params: any) => {
     const { password, token, ...safe } = params;
     return safe;
   };
   ```

### Connection Management

1. **Connection Pooling**
   ```typescript
   // The server automatically manages connections
   // But you can configure pool settings
   env: {
     DB_POOL_MIN: "2",
     DB_POOL_MAX: "10",
     DB_POOL_IDLE_TIMEOUT: "30000"
   }
   ```

2. **Retry Logic**
   ```typescript
   async function callWithRetry(toolName: string, params: any, maxRetries = 3) {
     for (let i = 0; i < maxRetries; i++) {
       try {
         return await client.callTool({ name: toolName, arguments: params });
       } catch (error) {
         if (error.code === 3002 && i < maxRetries - 1) { // Timeout
           await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
           continue;
         }
         throw error;
       }
     }
   }
   ```

## Troubleshooting

### Common Issues

1. **Connection Errors**
   ```
   Error: Connection failed
   Solution: Verify SUPABASE_URL and keys are correct
   ```

2. **Permission Denied**
   ```
   Error: PERMISSION_DENIED
   Solution: Check RLS policies or use service role key
   ```

3. **Invalid Identifier**
   ```
   Error: Cannot use PostgreSQL reserved word 'select'
   Solution: Use different column/table names or quote identifiers
   ```

4. **Validation Errors**
   ```
   Error: String value too large
   Solution: Reduce data size or split into smaller chunks
   ```

### Debug Mode

Enable debug logging:

```bash
LOG_LEVEL=debug node mcp-server-supabase
```

### Health Checks

```typescript
// Test server connectivity
async function healthCheck() {
  try {
    const result = await client.callTool({
      name: 'execute_sql',
      arguments: {
        project_id: 'your-project',
        query: 'SELECT 1 as health_check',
        read_only: true
      }
    });
    
    console.log('Server healthy:', result);
  } catch (error) {
    console.error('Health check failed:', error);
  }
}
```

### Performance Monitoring

```typescript
// Monitor tool performance
const performanceTracker = {
  async callTool(name: string, args: any) {
    const start = Date.now();
    try {
      const result = await client.callTool({ name, arguments: args });
      const duration = Date.now() - start;
      console.log(`Tool ${name} completed in ${duration}ms`);
      return result;
    } catch (error) {
      const duration = Date.now() - start;
      console.error(`Tool ${name} failed after ${duration}ms:`, error);
      throw error;
    }
  }
};
```

## Integration Examples

### React Integration

```typescript
import { useMCP } from '@modelcontextprotocol/react';

function UserList() {
  const { callTool, loading, error } = useMCP('supabase');
  const [users, setUsers] = useState([]);
  
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        const result = await callTool('read_records', {
          project_id: 'your-project',
          schema: 'public',
          table: 'users',
          limit: 50
        });
        setUsers(result.data);
      } catch (err) {
        console.error('Failed to fetch users:', err);
      }
    };
    
    fetchUsers();
  }, [callTool]);
  
  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;
  
  return (
    <ul>
      {users.map(user => (
        <li key={user.id}>{user.name} ({user.email})</li>
      ))}
    </ul>
  );
}
```

### Node.js CLI Integration

```typescript
#!/usr/bin/env node

import { MCPClient } from '@modelcontextprotocol/client';
import { Command } from 'commander';

const program = new Command();
const client = new MCPClient();

program
  .command('create-user')
  .option('-n, --name <name>', 'User name')
  .option('-e, --email <email>', 'User email')
  .action(async (options) => {
    await client.connect();
    
    try {
      const result = await client.callTool({
        name: 'create_record',
        arguments: {
          project_id: process.env.SUPABASE_PROJECT_ID,
          schema: 'public',
          table: 'users',
          data: {
            name: options.name,
            email: options.email
          }
        }
      });
      
      console.log('User created:', result.data[0]);
    } catch (error) {
      console.error('Failed to create user:', error.message);
      process.exit(1);
    } finally {
      await client.disconnect();
    }
  });

program.parse();
```

---

*This guide provides comprehensive information for integrating the Supabase MCP Server with MCP clients. For additional support, refer to the MCP specification and Supabase documentation.*