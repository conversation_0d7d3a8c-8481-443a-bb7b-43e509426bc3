# Testing Quick Reference

## 🚀 Quick Start

```bash
# Run all tests
npm test

# Run specific test types
npm run test:unit          # Fast unit tests
npm run test:integration   # Database integration tests
npm run test:e2e          # End-to-end tests

# Run with coverage
npm run test:coverage
```

## 📁 File Naming

| Test Type | File Pattern | Example |
|-----------|--------------|---------|
| Unit | `*.test.ts` | `utils.test.ts` |
| Integration | `*.integration.ts` | `database.integration.ts` |
| E2E | `*.e2e.ts` | `workflow.e2e.ts` |

## 🔧 Environment Setup

Create `.env.local`:
```env
SUPABASE_URL=https://devdb.syncrobit.net
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## 📝 Test Templates

### Unit Test
```typescript
import { describe, expect, test } from 'vitest';

describe('Component', () => {
  test('should do something', () => {
    expect(true).toBe(true);
  });
});
```

### Integration Test
```typescript
import { describe, expect, test } from 'vitest';
import { createClient } from '@supabase/supabase-js';

describe('Integration', () => {
  test('should connect to database', async () => {
    const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
    // Test implementation
  });
});
```

## 🎯 Common Assertions

```typescript
// Basic assertions
expect(value).toBe(expected);
expect(value).toEqual(expected);
expect(value).toBeTruthy();
expect(value).toBeFalsy();
expect(value).toBeNull();
expect(value).toBeUndefined();

// Arrays and objects
expect(array).toHaveLength(3);
expect(array).toContain(item);
expect(object).toHaveProperty('key');
expect(string).toMatch(/pattern/);

// Async assertions
await expect(promise).resolves.toBe(value);
await expect(promise).rejects.toThrow('error');

// Error handling
expect(() => throwingFunction()).toThrow();
```

## 🔍 Debugging

```bash
# Verbose output
npx vitest run --reporter=verbose

# Single test file
npx vitest run test/specific.test.ts

# Watch mode
npx vitest --watch

# Debug mode
node --inspect-brk ./node_modules/.bin/vitest run
```

## ⚡ Performance Tips

```typescript
// Parallel execution
test.concurrent('parallel test', async () => {
  // Test implementation
});

// Custom timeout
test('slow test', async () => {
  // Implementation
}, { timeout: 10000 });

// Skip tests
test.skip('not ready yet', () => {
  // Implementation
});

// Only run specific tests
test.only('focus on this', () => {
  // Implementation
});
```

## 🚨 Common Issues

| Issue | Solution |
|-------|----------|
| Environment variables undefined | Check `.env.local` exists and is loaded |
| Database connection fails | Verify Supabase instance is running |
| Permission denied | Use service role key for elevated permissions |
| Tests timeout | Increase timeout or optimize test logic |

## 📊 Coverage

```bash
# Generate coverage report
npm run test:coverage

# View HTML report
open coverage/index.html

# Coverage thresholds in vitest.config.ts
coverage: {
  thresholds: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  }
}
```

## 🔄 CI/CD Integration

```yaml
# GitHub Actions example
- name: Run Tests
  run: |
    npm ci
    npm run test:unit
    npm run test:integration
    npm run test:coverage
```

## 📚 Key Files

- `vitest.config.ts` - Main configuration
- `vitest.workspace.ts` - Multi-project setup
- `vitest.setup.ts` - Global test setup
- `.env.local` - Environment variables
- `docs/TESTING.md` - Full documentation

## 🎨 Best Practices

1. **One assertion per test** - Keep tests focused
2. **Descriptive names** - Test names should explain what they verify
3. **Arrange-Act-Assert** - Clear test structure
4. **Independent tests** - No test dependencies
5. **Clean up** - Reset state after tests
6. **Mock external dependencies** - Use real services only when necessary

## 🆘 Getting Help

1. Check `docs/TESTING.md` for detailed documentation
2. Review existing test files for patterns
3. Check Vitest documentation: https://vitest.dev/
4. Create an issue for framework problems

---

**Remember**: Good tests are fast, reliable, and easy to understand! 🎯
