# CRUD Tools Quick Reference

## Quick Start

```bash
# Install
npm install @supabase/mcp-server-supabase

# Configure
export SUPABASE_URL="https://your-project.supabase.co"
export SUPABASE_ANON_KEY="your-anon-key"

# Start server
node dist/index.js
```

## Common Patterns

### Basic CRUD Operations

```json
// CREATE
{
  "name": "create_record",
  "arguments": {
    "project_id": "your-project",
    "schema": "public", 
    "table": "users",
    "data": { "name": "<PERSON>", "email": "<EMAIL>" }
  }
}

// READ
{
  "name": "read_records",
  "arguments": {
    "project_id": "your-project",
    "schema": "public",
    "table": "users",
    "where": { "column": "status", "operator": "=", "value": "active" },
    "limit": 10
  }
}

// UPDATE  
{
  "name": "update_records",
  "arguments": {
    "project_id": "your-project",
    "schema": "public",
    "table": "users", 
    "data": { "status": "inactive" },
    "where": { "column": "id", "operator": "=", "value": 1 }
  }
}

// DELETE
{
  "name": "delete_records", 
  "arguments": {
    "project_id": "your-project",
    "schema": "public",
    "table": "users",
    "where": { "column": "status", "operator": "=", "value": "deleted" }
  }
}
```

### Complex WHERE Conditions

```json
// AND conditions
"where": {
  "AND": [
    { "column": "age", "operator": ">=", "value": 18 },
    { "column": "status", "operator": "=", "value": "active" }
  ]
}

// OR conditions  
"where": {
  "OR": [
    { "column": "role", "operator": "=", "value": "admin" },
    { "column": "role", "operator": "=", "value": "moderator" }
  ]
}

// NOT conditions
"where": {
  "NOT": { "column": "status", "operator": "=", "value": "banned" }
}

// Nested conditions
"where": {
  "AND": [
    { "column": "active", "operator": "=", "value": true },
    {
      "OR": [
        { "column": "plan", "operator": "=", "value": "premium" },
        { "column": "trial_days", "operator": ">", "value": 0 }
      ]
    }
  ]
}
```

### Advanced Operators

```json
// NULL checks
{ "column": "deleted_at", "operator": "IS NULL" }
{ "column": "verified_at", "operator": "IS NOT NULL" }

// IN/NOT IN
{ "column": "status", "operator": "IN", "value": ["active", "pending"] }
{ "column": "role", "operator": "NOT IN", "value": ["banned", "suspended"] }

// BETWEEN
{ "column": "age", "operator": "BETWEEN", "value": [18, 65] }
{ "column": "created_at", "operator": "BETWEEN", "value": ["2023-01-01", "2023-12-31"] }

// LIKE patterns
{ "column": "email", "operator": "LIKE", "value": "%@gmail.com" }
{ "column": "name", "operator": "ILIKE", "value": "john%" }

// JSON operators
{ "column": "metadata", "operator": "@>", "value": {"plan": "premium"} }
{ "column": "tags", "operator": "?", "value": "featured" }
```

### Bulk Operations

```json
// Bulk insert
{
  "name": "bulk_create_records",
  "arguments": {
    "project_id": "your-project", 
    "schema": "public",
    "table": "users",
    "records": [
      { "name": "John", "email": "<EMAIL>" },
      { "name": "Jane", "email": "<EMAIL>" },
      { "name": "Bob", "email": "<EMAIL>" }
    ],
    "batchSize": 100
  }
}

// Bulk update
{
  "name": "bulk_update_records",
  "arguments": {
    "project_id": "your-project",
    "schema": "public", 
    "table": "users",
    "updates": [
      {
        "data": { "status": "verified" },
        "where": { "column": "email", "operator": "=", "value": "<EMAIL>" }
      },
      {
        "data": { "status": "pending" },
        "where": { "column": "email", "operator": "=", "value": "<EMAIL>" }
      }
    ]
  }
}
```

### Transactions

```json
{
  "name": "execute_transaction",
  "arguments": {
    "project_id": "your-project",
    "operations": [
      {
        "type": "CREATE",
        "schema": "public",
        "table": "orders", 
        "data": { "user_id": 1, "total": 99.99, "status": "pending" }
      },
      {
        "type": "UPDATE",
        "schema": "public",
        "table": "users",
        "data": { "last_order_at": "2023-01-01T00:00:00Z" },
        "where": { "column": "id", "operator": "=", "value": 1 }
      },
      {
        "type": "UPDATE", 
        "schema": "public",
        "table": "products",
        "data": { "stock": "stock - 1" },
        "where": { "column": "id", "operator": "=", "value": 42 }
      }
    ],
    "isolationLevel": "READ_COMMITTED"
  }
}
```

## Available Tools

| Tool | Purpose | Write Operation |
|------|---------|----------------|
| `create_record` | Insert single record | ✅ |
| `read_records` | Query records | ❌ |
| `update_records` | Update records | ✅ |
| `delete_records` | Delete records | ✅ |
| `upsert_record` | Insert or update | ✅ |
| `bulk_create_records` | Insert multiple | ✅ |
| `bulk_update_records` | Update multiple | ✅ |
| `bulk_delete_records` | Delete multiple | ✅ |
| `execute_transaction` | Atomic operations | ✅ |
| `execute_sql` | Raw SQL | ✅/❌ |
| `list_tables` | List database tables | ❌ |

## Common Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `project_id` | string | ✅ | Supabase project ID |
| `schema` | string | ✅ | Database schema (usually "public") |
| `table` | string | ✅ | Table name |
| `data` | object | ✅* | Column-value pairs |
| `where` | object | ✅* | WHERE conditions |
| `columns` | string[] | ❌ | Columns to select |
| `orderBy` | object[] | ❌ | Sort specification |
| `limit` | number | ❌ | Row limit |
| `offset` | number | ❌ | Row offset |
| `returning` | string[] | ❌ | Columns to return |

*Required for specific operations

## Validation Rules

### Identifiers
- 1-63 characters
- Start with letter/underscore
- Letters, numbers, underscores only
- No PostgreSQL reserved words

### Values
- Strings: max 1MB
- Arrays: max 10,000 items
- Numbers: must be finite
- Objects: must be JSON serializable

### WHERE Operators
```
=, !=, <>, >, <, >=, <=
LIKE, ILIKE, NOT LIKE, NOT ILIKE  
IS NULL, IS NOT NULL
IN, NOT IN
BETWEEN, NOT BETWEEN
~, !~, ~*, !~* (regex)
@>, <@, ?, ?&, ?| (JSON)
```

## Error Codes

| Code | Type | Description |
|------|------|-------------|
| 1001 | `INVALID_IDENTIFIER` | Invalid table/column name |
| 1002 | `INVALID_WHERE_CLAUSE` | Malformed WHERE condition |
| 1003 | `VALIDATION_FAILED` | General validation error |
| 2001 | `CONSTRAINT_VIOLATION` | Database constraint failed |
| 2002 | `PERMISSION_DENIED` | Insufficient permissions |
| 2003 | `TABLE_NOT_FOUND` | Table doesn't exist |
| 3001 | `CONNECTION_FAILED` | Database connection error |
| 3002 | `TIMEOUT` | Operation timed out |
| 5001 | `INTERNAL_ERROR` | Server error |

## Response Formats

### Success Response
```json
{
  "success": true,
  "data": [...],
  "rowsAffected": 1,
  "requestId": "req_123..."
}
```

### Error Response  
```json
{
  "success": false,
  "error": {
    "type": "ValidationError",
    "message": "Invalid column name",
    "code": "INVALID_IDENTIFIER",
    "context": {...},
    "requestId": "req_123..."
  }
}
```

## Performance Tips

1. **Use bulk operations** for multiple records
2. **Select only needed columns** with `columns` parameter
3. **Add proper indexes** for WHERE clause columns
4. **Use transactions** for related operations
5. **Tune batch sizes** based on data size
6. **Enable read-only mode** when possible

## Environment Variables

```bash
# Required
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key

# Optional  
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
SUPABASE_PROJECT_ID=your-project-id
LOG_LEVEL=info
READ_ONLY_MODE=false
DB_POOL_MIN=2
DB_POOL_MAX=10
```

---

*For complete documentation see [CRUD_API_REFERENCE.md](./CRUD_API_REFERENCE.md)*