# MCP CRUD Tools API Reference

## Overview

The Supabase MCP Server provides comprehensive CRUD (Create, Read, Update, Delete) operations for PostgreSQL databases through Supabase. This reference documents all available tools, their parameters, validation rules, and response formats.

## Table of Contents

1. [Core CRUD Operations](#core-crud-operations)
2. [Bulk Operations](#bulk-operations)
3. [Transaction Operations](#transaction-operations)
4. [Database Operations](#database-operations)
5. [Validation Schemas](#validation-schemas)
6. [<PERSON><PERSON><PERSON>ling](#error-handling)
7. [Usage Examples](#usage-examples)

## Core CRUD Operations

### create_record

Create a new record in a table.

**Parameters:**
```typescript
{
  project_id: string;      // Supabase project ID
  schema: string;          // Database schema name (default: "public")
  table: string;           // Table name
  data: Record<string, any>; // Column-value pairs for the new record
  returning?: string[];    // Columns to return after insert (default: all)
}
```

**Example:**
```json
{
  "project_id": "your-project-id",
  "schema": "public",
  "table": "users",
  "data": {
    "name": "<PERSON>",
    "email": "<EMAIL>",
    "age": 30
  },
  "returning": ["id", "name", "created_at"]
}
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "John Doe", 
      "created_at": "2023-01-01T00:00:00Z"
    }
  ],
  "rowsAffected": 1,
  "requestId": "req_123..."
}
```

### read_records

Read records from a table with optional filtering, sorting, and pagination.

**Parameters:**
```typescript
{
  project_id: string;
  schema: string;
  table: string;
  columns?: string[];         // Columns to select (default: all)
  where?: WhereClause;       // WHERE conditions
  orderBy?: OrderByClause[]; // ORDER BY clauses
  limit?: number;            // LIMIT clause
  offset?: number;           // OFFSET clause
}
```

**WHERE Clause Structure:**
```typescript
type WhereClause = 
  | SimpleCondition
  | { AND: WhereClause[] }
  | { OR: WhereClause[] }
  | { NOT: WhereClause }

type SimpleCondition = {
  column: string;
  operator: WhereOperator;
  value?: any;
}
```

**Supported Operators:**
- `=`, `!=`, `<>`, `>`, `<`, `>=`, `<=`
- `LIKE`, `ILIKE`, `NOT LIKE`, `NOT ILIKE`
- `IS NULL`, `IS NOT NULL`
- `IN`, `NOT IN`
- `BETWEEN`, `NOT BETWEEN`
- `~`, `!~`, `~*`, `!~*` (regex operators)
- `@>`, `<@`, `?`, `?&`, `?|` (JSON operators)

**Example:**
```json
{
  "project_id": "your-project-id",
  "schema": "public",
  "table": "users",
  "columns": ["id", "name", "email"],
  "where": {
    "AND": [
      { "column": "age", "operator": ">=", "value": 18 },
      { "column": "status", "operator": "=", "value": "active" }
    ]
  },
  "orderBy": [
    { "column": "name", "direction": "ASC" }
  ],
  "limit": 50,
  "offset": 0
}
```

### update_records

Update existing records in a table.

**Parameters:**
```typescript
{
  project_id: string;
  schema: string;
  table: string;
  data: Record<string, any>; // Column-value pairs to update
  where: WhereClause;       // WHERE conditions (required)
  returning?: string[];     // Columns to return after update
}
```

**Example:**
```json
{
  "project_id": "your-project-id",
  "schema": "public",
  "table": "users",
  "data": {
    "status": "inactive",
    "updated_at": "2023-01-01T00:00:00Z"
  },
  "where": {
    "column": "id",
    "operator": "=",
    "value": 1
  }
}
```

### delete_records

Delete records from a table.

**Parameters:**
```typescript
{
  project_id: string;
  schema: string;
  table: string;
  where: WhereClause;       // WHERE conditions (required)
  returning?: string[];     // Columns to return from deleted records
}
```

**Example:**
```json
{
  "project_id": "your-project-id",
  "schema": "public",
  "table": "users",
  "where": {
    "column": "status",
    "operator": "=",
    "value": "deleted"
  }
}
```

### upsert_record

Insert a record or update it if it already exists (ON CONFLICT).

**Parameters:**
```typescript
{
  project_id: string;
  schema: string;
  table: string;
  data: Record<string, any>;
  conflictColumns: string[]; // Columns that define uniqueness constraint
  updateData?: Record<string, any>; // Data to use for updates (optional)
  returning?: string[];
}
```

**Example:**
```json
{
  "project_id": "your-project-id",
  "schema": "public",
  "table": "users",
  "data": {
    "email": "<EMAIL>",
    "name": "John Doe",
    "status": "active"
  },
  "conflictColumns": ["email"],
  "updateData": {
    "name": "John Doe Updated",
    "updated_at": "2023-01-01T00:00:00Z"
  }
}
```

## Bulk Operations

### bulk_create_records

Insert multiple records in a single operation.

**Parameters:**
```typescript
{
  project_id: string;
  schema: string;
  table: string;
  records: Record<string, any>[]; // Array of records to insert
  batchSize?: number;            // Batch size for processing (default: 1000)
  returning?: string[];
}
```

**Example:**
```json
{
  "project_id": "your-project-id",
  "schema": "public",
  "table": "users",
  "records": [
    { "name": "John", "email": "<EMAIL>" },
    { "name": "Jane", "email": "<EMAIL>" },
    { "name": "Bob", "email": "<EMAIL>" }
  ],
  "batchSize": 100
}
```

### bulk_update_records

Update multiple records with different conditions and data.

**Parameters:**
```typescript
{
  project_id: string;
  schema: string;
  table: string;
  updates: Array<{
    data: Record<string, any>;
    where: WhereClause;
  }>;
  batchSize?: number;
  returning?: string[];
}
```

### bulk_delete_records

Delete multiple records using different WHERE conditions.

**Parameters:**
```typescript
{
  project_id: string;
  schema: string;
  table: string;
  conditions: WhereClause[];
  batchSize?: number;
  returning?: string[];
}
```

## Transaction Operations

### execute_transaction

Execute multiple operations as a single atomic transaction.

**Parameters:**
```typescript
{
  project_id: string;
  operations: Array<{
    type: 'CREATE' | 'READ' | 'UPDATE' | 'DELETE' | 'UPSERT';
    schema: string;
    table: string;
    data?: Record<string, any>;
    where?: WhereClause;
    // ... other operation-specific parameters
  }>;
  isolationLevel?: 'READ_UNCOMMITTED' | 'READ_COMMITTED' | 'REPEATABLE_READ' | 'SERIALIZABLE';
}
```

**Example:**
```json
{
  "project_id": "your-project-id",
  "operations": [
    {
      "type": "CREATE",
      "schema": "public",
      "table": "orders",
      "data": { "user_id": 1, "total": 100.50 }
    },
    {
      "type": "UPDATE", 
      "schema": "public",
      "table": "users",
      "data": { "last_order_at": "2023-01-01T00:00:00Z" },
      "where": { "column": "id", "operator": "=", "value": 1 }
    }
  ],
  "isolationLevel": "READ_COMMITTED"
}
```

## Database Operations

### list_tables

List all tables in a schema.

**Parameters:**
```typescript
{
  project_id: string;
  schema?: string; // Default: "public"
}
```

### execute_sql

Execute raw SQL queries.

**Parameters:**
```typescript
{
  project_id: string;
  query: string;
  read_only?: boolean; // Default: true
}
```

**Example:**
```json
{
  "project_id": "your-project-id",
  "query": "SELECT COUNT(*) as total FROM users WHERE status = 'active'",
  "read_only": true
}
```

## Validation Schemas

### PostgreSQL Identifiers

All PostgreSQL identifiers (schema names, table names, column names) must follow these rules:

- **Length:** 1-63 characters
- **Format:** Must start with letter or underscore, followed by letters, numbers, or underscores
- **Reserved Words:** Cannot use PostgreSQL reserved words (SELECT, TABLE, WHERE, etc.)
- **System Schemas:** Cannot use `information_schema` or names starting with `pg_`

### Column Values

Column values support the following types with validation:

- **Strings:** Maximum 1MB size
- **Numbers:** Must be finite
- **Booleans:** `true` or `false`
- **NULL:** Explicit null values
- **Objects:** Must be JSON serializable
- **Arrays:** Maximum 10,000 items, must be JSON serializable

### WHERE Clause Validation

WHERE clauses are validated for:

- Operator-value compatibility (e.g., `IS NULL` should not have a value)
- Required values for operators (e.g., `IN` requires an array)
- Proper array sizes (e.g., `BETWEEN` requires exactly 2 values)

## Error Handling

### Error Types

The system provides structured error handling with these error types:

- **ValidationError:** Invalid parameters or data
- **DatabaseError:** Database-level errors (constraints, permissions)
- **TimeoutError:** Operation timeout
- **RetryableError:** Temporary failures that can be retried

### Error Response Format

```json
{
  "success": false,
  "error": {
    "type": "ValidationError",
    "message": "Invalid column name 'select': Cannot use PostgreSQL reserved word",
    "code": "INVALID_IDENTIFIER",
    "context": {
      "operation": "CREATE",
      "schema": "public",
      "table": "users",
      "column": "select"
    },
    "requestId": "req_123..."
  }
}
```

### Retry Logic

Operations implement intelligent retry logic:

- **CREATE/UPDATE/DELETE:** 2 retries with exponential backoff
- **READ:** 3 retries with exponential backoff
- **Bulk Operations:** Configurable retries per batch
- **Transactions:** Single retry (due to isolation requirements)

## Usage Examples

### Complex Query Example

```json
{
  "project_id": "your-project-id",
  "schema": "public",
  "table": "orders",
  "columns": ["id", "user_id", "total", "status", "created_at"],
  "where": {
    "AND": [
      {
        "OR": [
          { "column": "status", "operator": "=", "value": "completed" },
          { "column": "status", "operator": "=", "value": "shipped" }
        ]
      },
      { "column": "total", "operator": ">=", "value": 50.00 },
      { 
        "column": "created_at", 
        "operator": "BETWEEN", 
        "value": ["2023-01-01", "2023-12-31"] 
      }
    ]
  },
  "orderBy": [
    { "column": "created_at", "direction": "DESC" },
    { "column": "total", "direction": "ASC" }
  ],
  "limit": 100
}
```

### JSON Column Operations

```json
{
  "project_id": "your-project-id",
  "schema": "public",
  "table": "user_preferences",
  "where": {
    "column": "settings",
    "operator": "@>",
    "value": { "theme": "dark" }
  }
}
```

### Bulk Insert with Error Handling

```json
{
  "project_id": "your-project-id",
  "schema": "public",
  "table": "import_logs",
  "records": [
    { "batch_id": "batch_001", "status": "processing", "record_count": 1000 },
    { "batch_id": "batch_002", "status": "processing", "record_count": 1500 },
    { "batch_id": "batch_003", "status": "processing", "record_count": 750 }
  ],
  "batchSize": 50,
  "returning": ["id", "batch_id", "created_at"]
}
```

## Performance Considerations

### Optimization Tips

1. **Use Bulk Operations:** For multiple records, use bulk operations instead of individual calls
2. **Limit Column Selection:** Only select required columns to reduce data transfer
3. **Use Proper Indexing:** Ensure WHERE clause columns are indexed
4. **Batch Size Tuning:** Adjust batch sizes based on record size and network conditions
5. **Connection Pooling:** The server automatically manages connection pooling

### Limits and Quotas

- **Maximum Query Size:** 1MB
- **Maximum Batch Size:** 10,000 records
- **Maximum String Column Size:** 1MB
- **Maximum Array Size:** 10,000 items
- **Default Request Timeout:** 30 seconds
- **Maximum Concurrent Requests:** Based on Supabase plan

## Security

### SQL Injection Prevention

All CRUD operations use:

- **Parameterized Queries:** Values are properly escaped
- **Identifier Quoting:** All identifiers are quoted to prevent injection
- **Input Validation:** Comprehensive validation before query execution
- **Reserved Word Checking:** Prevents use of SQL keywords as identifiers

### Permission Model

Operations respect Supabase's Row Level Security (RLS) and database permissions:

- **Read-Only Mode:** Can be enabled to prevent write operations
- **Schema Restrictions:** Access can be limited to specific schemas
- **Row Level Security:** Honors Supabase RLS policies
- **Database Roles:** Respects PostgreSQL role permissions

---

*This documentation covers all available CRUD tools and operations. For implementation details, see the source code in `src/tools/crud-tools.ts` and related files.*