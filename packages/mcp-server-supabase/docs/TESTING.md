# Integration Test Framework Documentation

## Overview

This document provides comprehensive guidance for using the integration test framework for the MCP Server Supabase project. The framework is built on Vitest and provides robust testing capabilities for unit, integration, and end-to-end testing.

## Framework Architecture

### Test Types

The project supports three types of tests:

1. **Unit Tests** (`*.test.ts`) - Test individual functions and components in isolation
2. **Integration Tests** (`*.integration.ts`) - Test interactions between components and external services
3. **End-to-End Tests** (`*.e2e.ts`) - Test complete user workflows and system behavior

### Configuration Files

- `vitest.config.ts` - Main Vitest configuration
- `vitest.workspace.ts` - Workspace configuration for multiple test projects
- `vitest.setup.ts` - Global test setup and configuration
- `.env.local` - Environment variables for testing

## Getting Started

### Prerequisites

- Node.js 18+ installed
- Local Supabase instance running
- Environment variables configured

### Installation

```bash
# Install dependencies
npm install

# Verify installation
npm run test:unit
```

### Environment Setup

Create or update `.env.local` with the following variables:

```env
# Local Supabase Configuration
SUPABASE_URL=https://devdb.syncrobit.net
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
DATABASE_URL=***********************************************************
READ_ONLY=false
DEBUG_SQL=false
```

## Running Tests

### Available Commands

```bash
# Run all tests
npm test

# Run specific test types
npm run test:unit          # Unit tests only
npm run test:integration   # Integration tests only
npm run test:e2e          # End-to-end tests only

# Run tests with coverage
npm run test:coverage

# Run tests in watch mode
npm run test:watch

# Run specific test file
npx vitest run test/framework-validation.integration.ts
```

### Test Execution Examples

```bash
# Run a specific test suite
npx vitest run test/local-database.integration.ts --project integration

# Run tests with verbose output
npx vitest run --reporter=verbose

# Run tests and generate coverage report
npx vitest run --coverage
```

## Writing Tests

### Unit Test Example

```typescript
import { describe, expect, test } from 'vitest';
import { myFunction } from '../src/utils';

describe('myFunction', () => {
  test('should return expected result', () => {
    const result = myFunction('input');
    expect(result).toBe('expected');
  });
});
```

### Integration Test Example

```typescript
import { describe, expect, test } from 'vitest';
import { createClient } from '@supabase/supabase-js';

describe('Database Integration', () => {
  test('can connect to database', async () => {
    const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
    
    const { data, error } = await supabase
      .from('table_name')
      .select('*')
      .limit(1);
    
    expect(error).toBeNull();
    expect(data).toBeDefined();
  });
});
```

## Test Organization

### File Structure

```
packages/mcp-server-supabase/
├── test/
│   ├── framework-validation.integration.ts
│   ├── local-database.integration.ts
│   └── stdio.integration.ts
├── src/
│   └── *.test.ts (unit tests alongside source)
└── docs/
    └── TESTING.md (this file)
```

### Naming Conventions

- Unit tests: `*.test.ts`
- Integration tests: `*.integration.ts`
- End-to-end tests: `*.e2e.ts`
- Test utilities: `test-utils.ts`

## Best Practices

### Test Writing Guidelines

1. **Descriptive Names**: Use clear, descriptive test names
2. **Single Responsibility**: Each test should verify one specific behavior
3. **Arrange-Act-Assert**: Structure tests with clear setup, execution, and verification
4. **Isolation**: Tests should not depend on each other
5. **Cleanup**: Clean up resources after tests complete

### Performance Considerations

- Use `test.concurrent` for independent tests that can run in parallel
- Mock external dependencies in unit tests
- Use real services only in integration tests
- Set appropriate timeouts for async operations

### Error Handling

```typescript
test('should handle errors gracefully', async () => {
  const invalidInput = null;
  
  await expect(async () => {
    await functionThatShouldThrow(invalidInput);
  }).rejects.toThrow('Expected error message');
});
```

## Troubleshooting

### Common Issues

#### 1. Environment Variables Not Loaded

**Problem**: Tests fail with undefined environment variables

**Solution**: 
- Verify `.env.local` exists and contains correct values
- Check that `vitest.setup.ts` loads environment variables
- Ensure environment variables are not overridden

#### 2. Database Connection Failures

**Problem**: Integration tests fail to connect to Supabase

**Solution**:
- Verify local Supabase instance is running
- Check SUPABASE_URL and keys are correct
- Ensure network connectivity to the database

#### 3. Permission Denied Errors

**Problem**: Tests fail with schema permission errors

**Solution**:
- Use service role key for tests requiring elevated permissions
- Verify schema is exposed through the API
- Check RLS policies don't block test operations

#### 4. Test Timeouts

**Problem**: Tests timeout on slow operations

**Solution**:
```typescript
test('slow operation', async () => {
  // Increase timeout for specific test
}, { timeout: 10000 });
```

### Debugging Tests

```bash
# Run tests with debug output
DEBUG=* npm run test:integration

# Run single test with verbose logging
npx vitest run test/specific-test.ts --reporter=verbose

# Use Node.js debugger
node --inspect-brk ./node_modules/.bin/vitest run
```

## Continuous Integration

### GitHub Actions Integration

The test framework integrates with CI/CD pipelines:

```yaml
# .github/workflows/test.yml
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - run: npm ci
      - run: npm run test:unit
      - run: npm run test:integration
```

### Coverage Reports

Generate and view coverage reports:

```bash
# Generate coverage
npm run test:coverage

# View HTML report
open coverage/index.html
```

## Extending the Framework

### Adding New Test Types

1. Create test files with appropriate naming convention
2. Update `vitest.workspace.ts` if needed
3. Add npm scripts for new test types
4. Document new patterns in this file

### Custom Matchers

```typescript
// test/setup.ts
import { expect } from 'vitest';

expect.extend({
  toBeValidUUID(received) {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    return {
      pass: uuidRegex.test(received),
      message: () => `Expected ${received} to be a valid UUID`
    };
  }
});
```

## Maintenance

### Regular Tasks

1. **Update Dependencies**: Keep Vitest and related packages updated
2. **Review Coverage**: Maintain high test coverage (>80%)
3. **Clean Up**: Remove obsolete tests and update outdated ones
4. **Performance**: Monitor test execution time and optimize slow tests

### Monitoring

- Track test execution time trends
- Monitor coverage metrics
- Review test failure patterns
- Update documentation as framework evolves

## Resources

- [Vitest Documentation](https://vitest.dev/)
- [Supabase Testing Guide](https://supabase.com/docs/guides/testing)
- [Testing Best Practices](https://testing-library.com/docs/guiding-principles/)

---

For questions or issues with the testing framework, please refer to this documentation or create an issue in the project repository.
