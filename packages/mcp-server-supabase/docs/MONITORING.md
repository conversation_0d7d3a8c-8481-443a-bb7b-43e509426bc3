# Monitoring and Observability Guide

This guide covers the comprehensive monitoring and observability system implemented for the MCP Server Supabase project.

## Overview

The monitoring system provides:
- **Health Check Endpoints** - HTTP endpoints for service health monitoring
- **Metrics Collection** - Performance and operational metrics in Prometheus and JSON formats
- **Distributed Tracing** - Request correlation and performance tracking
- **Alerting System** - Threshold-based alerting with customizable rules
- **Test Coverage** - Automated test fixtures and comprehensive test suites

## Quick Start

### Enable Monitoring

```typescript
import { createLocalSupabaseMcpServer } from '@supabase/mcp-server-supabase';

const server = await createLocalSupabaseMcpServer({
  enableMonitoring: true,
  monitoringConfig: {
    enableHealthEndpoints: true,
    enableMetricsEndpoints: true,
    enableDistributedTracing: true,
    enableAlerting: true,
    alertCheckInterval: 60000, // 1 minute
  }
});
```

### Access Monitoring Endpoints

Once enabled, the following endpoints are available:

- `GET /health` - Basic health status
- `GET /health/detailed` - Detailed health information
- `GET /metrics` - Prometheus metrics
- `GET /metrics/json` - JSON formatted metrics
- `GET /monitoring/dashboard` - Comprehensive dashboard data

## Health Check Endpoints

### Basic Health Check
```bash
curl http://localhost:3000/health
```

Response:
```json
{
  "status": "healthy",
  "timestamp": "2025-06-16T09:47:13.721Z",
  "uptime": 86400000,
  "version": "1.0.0"
}
```

### Detailed Health Check
```bash
curl http://localhost:3000/health/detailed
```

Includes comprehensive system information, configuration health, and performance metrics.

### Kubernetes-Style Probes

- **Readiness**: `GET /health/ready` - Service ready to accept traffic
- **Liveness**: `GET /health/live` - Service is alive and responsive
- **Startup**: `GET /health/startup` - Service has completed startup

## Metrics Collection

### Prometheus Metrics
```bash
curl http://localhost:3000/metrics
```

Provides metrics in Prometheus format for integration with monitoring systems like Grafana.

### JSON Metrics
```bash
curl http://localhost:3000/metrics/json
```

### Specific Metric Categories

- **Performance**: `GET /metrics/performance` - Cache, connection pool, circuit breaker stats
- **Configuration**: `GET /metrics/config` - Configuration health and validation metrics
- **Events**: `GET /metrics/events` - Event system metrics and recent events
- **System**: `GET /metrics/system` - Memory, CPU, and process metrics

## Distributed Tracing

### Automatic Request Tracing

All HTTP requests are automatically traced when distributed tracing is enabled:

```typescript
// Traces are automatically created for HTTP requests
// with correlation IDs and performance metrics
```

### Manual Tracing

```typescript
import { distributedTracer } from '@supabase/mcp-server-supabase/monitoring';

// Trace an async operation
const result = await distributedTracer.trace('database-query', async () => {
  return await database.query('SELECT * FROM users');
});

// Create custom spans
const span = distributedTracer.startSpan('custom-operation');
span.setTag('user.id', '123');
span.log('info', 'Processing user data');
const result = span.finish();
```

### Trace Context Headers

The system supports standard trace context headers:
- `x-trace-id` - Trace identifier
- `x-span-id` - Span identifier  
- `traceparent` - W3C Trace Context format

## Alerting System

### Default Alert Rules

The system includes pre-configured alert rules:

1. **High Memory Usage** - Triggers when memory usage exceeds 80%
2. **Database Connection Failure** - Critical alert for database connectivity issues
3. **High Error Rate** - Triggers when error rate exceeds 5%

### Custom Alert Rules

```typescript
import { alertingSystem } from '@supabase/mcp-server-supabase/monitoring';

// Add custom alert rule
alertingSystem.addRule({
  id: 'custom-rule',
  name: 'Custom Alert',
  description: 'Custom condition monitoring',
  severity: 'warning',
  condition: (metrics) => {
    // Custom condition logic
    return metrics.system.cpu.user > 80000000;
  },
  threshold: 80000000,
  enabled: true,
  cooldownMs: 300000, // 5 minutes
  tags: { category: 'performance' },
});

// Add custom alert handler
alertingSystem.addHandler('email', async (alert) => {
  // Send email notification
  console.log(`Alert: ${alert.name} - ${alert.description}`);
});
```

### Alert Severity Levels

- **info** - Informational alerts
- **warning** - Warning conditions
- **error** - Error conditions requiring attention
- **critical** - Critical issues requiring immediate action

## Test Fixtures and Testing

### Using Test Fixtures

```typescript
import { testFixtures } from '@supabase/mcp-server-supabase/monitoring';

// Generate test data
const healthData = testFixtures.generateHealthCheckData();
const traces = testFixtures.generateTraces(10);
const alerts = testFixtures.generateAlerts(5);

// Generate scenario data
const scenario = testFixtures.generateScenario('degraded');
```

### Running Monitoring Tests

```bash
# Run monitoring integration tests
npm run test:integration test/monitoring.integration.ts

# Run with coverage
npm run test:integration -- --coverage
```

## Configuration

### Environment Variables

```env
# Enable monitoring features
ENABLE_MONITORING=true
ENABLE_HEALTH_ENDPOINTS=true
ENABLE_METRICS_ENDPOINTS=true
ENABLE_DISTRIBUTED_TRACING=true
ENABLE_ALERTING=true

# Monitoring intervals
HEALTH_CHECK_INTERVAL=300000
ALERT_CHECK_INTERVAL=60000
METRICS_COLLECTION_INTERVAL=60000

# Retention settings
TRACE_RETENTION_HOURS=24
METRICS_RETENTION_DAYS=7
ALERT_HISTORY_SIZE=1000
```

### Programmatic Configuration

```typescript
import { MonitoringSystem } from '@supabase/mcp-server-supabase/monitoring';

const monitoring = new MonitoringSystem({
  enableHealthEndpoints: true,
  enableMetricsEndpoints: true,
  enableDistributedTracing: true,
  enableAlerting: true,
  alertCheckInterval: 60000,
  tracingCleanupInterval: 3600000,
  metricsRetentionDays: 7,
});

await monitoring.initialize(expressApp);
```

## Dashboard Integration

### Comprehensive Dashboard Data

```bash
curl http://localhost:3000/monitoring/dashboard
```

Returns unified monitoring data including:
- Health status across all components
- Performance metrics and trends
- Active alerts and alert history
- Tracing statistics
- System resource utilization

### Integration with External Systems

The monitoring system is designed to integrate with:

- **Prometheus** - Metrics collection and alerting
- **Grafana** - Visualization and dashboards
- **Jaeger/Zipkin** - Distributed tracing visualization
- **PagerDuty/Slack** - Alert notifications
- **Kubernetes** - Health check probes

## Troubleshooting

### Common Issues

1. **Health Checks Failing**
   - Verify database connectivity
   - Check configuration validity
   - Review system resource usage

2. **Metrics Not Collecting**
   - Ensure monitoring is enabled
   - Check endpoint accessibility
   - Verify configuration settings

3. **Alerts Not Triggering**
   - Verify alert rules are enabled
   - Check condition logic
   - Review cooldown periods

4. **Tracing Data Missing**
   - Confirm distributed tracing is enabled
   - Check trace context headers
   - Verify cleanup intervals

### Debug Commands

```bash
# Check monitoring system status
curl http://localhost:3000/monitoring/dashboard | jq '.health'

# View active alerts
curl http://localhost:3000/metrics/events | jq '.recentEvents'

# Check system metrics
curl http://localhost:3000/metrics/system | jq '.system'
```

## Performance Considerations

- **Memory Usage**: Monitoring adds ~10-20MB memory overhead
- **CPU Impact**: <1% CPU overhead for typical workloads
- **Storage**: Traces and metrics are stored in memory with automatic cleanup
- **Network**: Minimal impact, only when endpoints are accessed

## Security Considerations

- Health and metrics endpoints should be secured in production
- Consider rate limiting for monitoring endpoints
- Trace data may contain sensitive information
- Alert handlers should validate input data

## Best Practices

1. **Monitor the Monitors** - Set up alerts for monitoring system health
2. **Gradual Rollout** - Enable monitoring features incrementally
3. **Regular Review** - Periodically review alert rules and thresholds
4. **Documentation** - Keep monitoring configuration documented
5. **Testing** - Include monitoring in your test suites

## Related Documentation

- [Health Check API Reference](./API.md#health-checks)
- [Metrics API Reference](./API.md#metrics)
- [Testing Guide](./TESTING.md)
- [Configuration Guide](./CONFIGURATION.md)
