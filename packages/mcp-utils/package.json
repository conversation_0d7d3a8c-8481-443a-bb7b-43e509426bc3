{"name": "@supabase/mcp-utils", "version": "0.2.1", "description": "MCP utilities", "license": "Apache-2.0", "type": "module", "main": "dist/index.cjs", "types": "dist/index.d.ts", "sideEffects": false, "scripts": {"build": "tsup --clean", "test": "vitest", "test:coverage": "vitest --coverage", "prepublishOnly": "npm run build"}, "files": ["dist/**/*"], "exports": {".": {"import": "./dist/index.js", "types": "./dist/index.d.ts", "default": "./dist/index.cjs"}}, "dependencies": {"@modelcontextprotocol/sdk": "^1.11.0", "zod": "^3.24.1", "zod-to-json-schema": "^3.24.1"}, "devDependencies": {"@total-typescript/tsconfig": "^1.0.4", "@types/node": "^22.8.6", "prettier": "^3.3.3", "tsup": "^8.3.5", "typescript": "^5.6.3", "vitest": "^2.1.9"}}