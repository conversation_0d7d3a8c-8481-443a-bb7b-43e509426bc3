{"$schema": "https://biomejs.dev/schemas/1.9.4/schema.json", "vcs": {"enabled": false, "clientKind": "git", "useIgnoreFile": false}, "files": {"ignoreUnknown": false, "ignore": ["**/dist", "packages/mcp-server-supabase/src/management-api/types.ts"]}, "formatter": {"enabled": true, "indentStyle": "space"}, "organizeImports": {"enabled": false}, "linter": {"enabled": false}, "javascript": {"formatter": {"quoteStyle": "single", "trailingCommas": "es5", "bracketSameLine": false, "arrowParentheses": "always"}}, "json": {"formatter": {"trailingCommas": "none"}}}