name: Tests

on:
  push:
    branches: [ main, master, develop ]
  pull_request:
    branches: [ main, master, develop ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_USER: postgres
          POSTGRES_DB: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Install package dependencies
        run: |
          cd packages/mcp-server-supabase
          npm ci
          
      - name: Build packages
        run: |
          cd packages/mcp-server-supabase
          npm run build
          
      - name: Setup test environment
        run: |
          cd packages/mcp-server-supabase
          cp .env.example .env.local
          
      - name: Setup Supabase CLI
        uses: supabase/setup-cli@v1
        with:
          version: latest
          
      - name: Start Supabase local development
        run: |
          cd packages/mcp-server-supabase
          supabase start
          
      - name: Run unit tests
        run: |
          cd packages/mcp-server-supabase
          npm run test:unit
          
      - name: Run integration tests
        run: |
          cd packages/mcp-server-supabase
          npm run test:integration
          
      - name: Run comprehensive MCP protocol tests
        run: |
          cd packages/mcp-server-supabase
          npx vitest run test/mcp-protocol-comprehensive.integration.ts
          
      - name: Generate test coverage
        run: |
          cd packages/mcp-server-supabase
          npm run test:coverage
          
      - name: Upload coverage reports
        uses: actions/upload-artifact@v4
        with:
          name: coverage-reports
          path: packages/mcp-server-supabase/coverage/
          
      - name: Stop Supabase
        if: always()
        run: |
          cd packages/mcp-server-supabase
          supabase stop