#!/usr/bin/env node

/**
 * Test script to verify MCP server connection and database functions
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY || process.env.SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing SUPABASE_URL or SUPABASE_SERVICE_KEY in environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testConnection() {
  console.log('🔄 Testing Supabase MCP Server Setup...\n');

  // Test 1: Basic connection
  console.log('1. Testing basic Supabase connection...');
  try {
    const { data, error } = await supabase.from('_health_check').select('1').limit(1);
    if (error && error.code !== 'PGRST116') {
      console.log('   ⚠️  Health check table not found, but connection successful');
    } else {
      console.log('   ✅ Basic connection successful');
    }
  } catch (error) {
    console.log(`   ❌ Connection failed: ${error.message}`);
    return;
  }

  // Test 2: Check if exec_sql function exists
  console.log('\n2. Testing exec_sql function...');
  try {
    const { data, error } = await supabase.rpc('exec_sql', { 
      query: 'SELECT 1 as test_value' 
    });
    
    if (error) {
      console.log(`   ❌ exec_sql function error: ${error.message}`);
      console.log('   💡 Make sure you\'ve executed the SQL setup script in your Supabase dashboard');
    } else {
      console.log('   ✅ exec_sql function working correctly');
      console.log(`   📊 Result: ${JSON.stringify(data, null, 2)}`);
    }
  } catch (error) {
    console.log(`   ❌ exec_sql test failed: ${error.message}`);
  }

  // Test 3: Check list_tables function
  console.log('\n3. Testing list_tables function...');
  try {
    const { data, error } = await supabase.rpc('list_tables');
    
    if (error) {
      console.log(`   ❌ list_tables function error: ${error.message}`);
    } else {
      console.log('   ✅ list_tables function working correctly');
      console.log(`   📊 Found ${data?.length || 0} tables`);
      if (data && data.length > 0) {
        console.log(`   📋 Tables: ${data.map(t => t.table_name).join(', ')}`);
      }
    }
  } catch (error) {
    console.log(`   ❌ list_tables test failed: ${error.message}`);
  }

  // Test 4: Test information_schema access
  console.log('\n4. Testing information_schema access...');
  try {
    const { data, error } = await supabase
      .from('information_schema.tables')
      .select('table_name, table_type')
      .eq('table_schema', 'public')
      .limit(5);
    
    if (error) {
      console.log(`   ❌ information_schema access error: ${error.message}`);
    } else {
      console.log('   ✅ information_schema access working correctly');
      console.log(`   📊 Found ${data?.length || 0} schema entries`);
    }
  } catch (error) {
    console.log(`   ❌ information_schema test failed: ${error.message}`);
  }

  console.log('\n🎯 Test Summary:');
  console.log('   If all tests passed, your MCP server should work with Claude Desktop');
  console.log('   If any tests failed, check the error messages above for guidance');
  console.log('\n📖 Next steps:');
  console.log('   1. Restart Claude Desktop');
  console.log('   2. Open a new conversation');
  console.log('   3. Try using the list_tables tool');
}

testConnection().catch(console.error);