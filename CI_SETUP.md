# CI/CD Setup for Gitea

This project uses Gitea Actions for continuous integration and testing.

## Workflow Configuration

The CI pipeline is configured in `.gitea/workflows/test.yml` and runs automatically on:
- Push to `main`, `master`, or `develop` branches
- Pull requests targeting `main`, `master`, or `develop` branches

## Test Pipeline

The CI pipeline includes the following steps:

### 1. Environment Setup
- Checkout code
- Setup Node.js 18
- Install dependencies
- Setup PostgreSQL service
- Setup Supabase CLI

### 2. Build Process
- Install package dependencies
- Build the MCP server package
- Setup test environment variables

### 3. Testing
- Start Supabase local development environment
- Run unit tests
- Run integration tests
- Run comprehensive MCP protocol tests
- Generate test coverage reports

### 4. Artifacts
- Upload coverage reports as artifacts
- Clean up Supabase environment

## Test Commands

The following npm scripts are used in the CI pipeline:

```bash
# Unit tests
npm run test:unit

# Integration tests
npm run test:integration

# Comprehensive MCP protocol tests
npx vitest run test/mcp-protocol-comprehensive.integration.ts

# Coverage reports
npm run test:coverage
```

## Local Development

To run the same tests locally:

```bash
cd packages/mcp-server-supabase

# Install dependencies
npm ci

# Build the package
npm run build

# Setup environment
cp .env.example .env.local

# Start Supabase
supabase start

# Run tests
npm run test:unit
npm run test:integration
npx vitest run test/mcp-protocol-comprehensive.integration.ts

# Stop Supabase
supabase stop
```

## Requirements

- Node.js 18+
- PostgreSQL 15
- Supabase CLI
- Docker (for Supabase local development)

## Environment Variables

The CI pipeline uses the following environment variables:
- `POSTGRES_PASSWORD`: Set to `postgres`
- `POSTGRES_USER`: Set to `postgres`
- `POSTGRES_DB`: Set to `postgres`

Additional environment variables are loaded from `.env.local` in the test environment.

## Troubleshooting

### Common Issues

1. **PostgreSQL Connection Issues**
   - Ensure PostgreSQL service is healthy before running tests
   - Check port 5432 is available

2. **Supabase CLI Issues**
   - Verify Supabase CLI is properly installed
   - Check Docker is running for local development

3. **Test Failures**
   - Check test logs in the CI pipeline
   - Verify all dependencies are installed
   - Ensure environment variables are properly set

### Debugging

To debug CI issues:
1. Check the workflow logs in Gitea
2. Run tests locally with the same environment
3. Verify all required services are running
4. Check for any missing dependencies or configuration