# Task ID: 26
# Title: Implement Simple Local Alternatives for get_logs and get_advisors Tools
# Status: pending
# Dependencies: 4, 5
# Priority: medium
# Description: Create basic local equivalents for cloud-only features with minimal implementation.
# Details:
This task involves creating simplified local alternatives for two cloud-dependent tools:

1. **Local get_logs Implementation**:
   - Use simple Docker commands to extract logs from running Supabase containers
   - Implement basic log filtering with grep or similar
   - Focus on functionality over feature parity
   - Example implementation:
   ```typescript
   async function getLocalLogs(options: LogOptions): Promise<LogEntry[]> {
     const { containerName, lines } = options;
     const command = `docker logs --tail ${lines || 100} ${containerName}`;
     const output = await execCommand(command);
     return parseBasicLogs(output);
   }
   ```

2. **Local get_advisors Implementation**:
   - Implement only the most critical database checks
   - Focus on common issues that affect development:
     - Missing row-level security policies
     - Tables without primary keys
   - Example implementation:
   ```typescript
   async function getLocalAdvisors(): Promise<AdvisorResult[]> {
     const pool = await getDbConnectionPool();
     const results = [];
     
     // Check for tables without primary keys
     const tablesWithoutPK = await checkTablesWithoutPK(pool);
     if (tablesWithoutPK.length > 0) {
       results.push({
         type: 'performance',
         message: `Tables without primary keys: ${tablesWithoutPK.join(', ')}`
       });
     }
     
     // Check for tables without RLS
     const tablesWithoutRLS = await checkTablesWithoutRLS(pool);
     if (tablesWithoutRLS.length > 0) {
       results.push({
         type: 'security',
         message: `Tables without RLS: ${tablesWithoutRLS.join(', ')}`
       });
     }
     
     return results;
   }
   ```

3. **Integration**:
   - Use environment detection to switch between local and cloud implementations
   - Keep the interface consistent but simplify the implementation

4. **Error Handling**:
   - Provide clear error messages when Docker or database isn't available
   - Fail gracefully with helpful troubleshooting tips

# Test Strategy:
1. **Manual Testing**:
   - Test with local Supabase instance
   - Verify logs are retrieved correctly
   - Check that basic advisor checks work

2. **Simple Automated Tests**:
   - Write basic tests for the core functionality
   - Mock Docker and database responses

3. **Documentation**:
   - Document limitations compared to cloud version
   - Provide clear usage examples
